'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

type FilterType = 'All' | 'Locked' | 'Unlocked';

const badgeIconMap: { [key: string]: string } = {
  'Unstoppable': "/assets/images/fire2.png",
  'Course Champion': "/assets/images/graduation-hat1.png",
  'Elite Learner': "/assets/images/dimond.png",
  'Perfectionist': "/assets/images/star1.png",
  'Fast Finisher': "/assets/images/crack.png",
  'Speed Learner': "/assets/images/rocket1.png",
  'Sharp Shooter': "/assets/images/accuracy.png",
  'Quiz Master': "/assets/images/book4.png",
  'Referral Hero': "/assets/images/giftbox1.png",
};

const badges = [
  { id: 1, title: 'Unstoppable', description: 'Completed all 14-day streak', status: 'unlocked', count: 2 },
  { id: 2, title: 'Course Champion', description: 'Completed 100% course progress', status: 'unlocked', count: 1 },
  { id: 3, title: 'Elite Learner', description: 'Got top 5% marks in assessments', status: 'locked' },
  { id: 4, title: 'Perfectionist', description: 'Completing every quiz and assignment with excellence', status: 'unlocked', count: 1 },
  { id: 5, title: 'Fast Finisher', description: 'Completed a full course ahead of deadline', status: 'locked' },
  { id: 6, title: 'Speed Learner', description: 'Finishing courses in record time', status: 'unlocked', count: 2 },
  { id: 7, title: 'Sharp Shooter', description: 'Score 100% in quiz challenges', status: 'locked' },
  { id: 8, title: 'Quiz Master', description: 'Get 90% in 10 quizzes', status: 'locked' },
  { id: 9, title: 'Referral Hero', description: 'Referred 5 people to platform', status: 'unlocked', count: 2 },
];

const AchievementBadges: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<FilterType>('All');
  const router = useRouter();

  const handleBadgeClick = (badgeId: number): void => {
    router.push(`/dashboard/achievement?badgeId=${badgeId}`);
  };

  const filteredBadges = badges.filter(badge => {
    if (activeFilter === 'All') return true;
    return badge.status === activeFilter.toLowerCase();
  });

  return (
    <div className="w-full px-4 py-10">
      {/* Filter Tabs */}
      <div className="mb-8 flex justify-start">
        <div className="bg-gray-100 border border-black rounded-full flex w-130 overflow-hidden">
          {(['All', 'Locked', 'Unlocked'] as FilterType[]).map((filter, index, filters) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              className={`flex-1 py-2 text-center transition-colors cursor-pointer
                ${activeFilter === filter ? 'bg-[#dddddd] font-medium shadow' : 'text-gray-600'}
                ${index !== filters.length - 1 ? 'border-r' : ''}`}
            >
              {filter}
            </button>
          ))}
        </div>
      </div>

      {/* Badges Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-7 container   cursor-pointer">
        {filteredBadges.map((badge, index) => (
          <div
            key={badge.id}
            onClick={index === 0 ? () => handleBadgeClick(badge.id) : undefined}
            className="bg-[#e5e5e5] shadow-[0_4px_4px_0_rgba(0,0,0,0.25)] rounded-lg  border border-gray-200 p-2 flex flex-col items-center text-center transition-shadow relative"
          >
            {/* Badge Count */}
            {badge.count && badge.status === 'unlocked' && (
              <div className="absolute top-2 right-[-15px] z-10">
                <span className="bg-[#4b207a] text-white text-xs px-5 py-1 font-bold rounded-full flex items-center justify-center">
                  {badge.count}
                </span>
              </div>
            )}

            {/* Badge Image */}
            <div className="w-20 h-20 rounded-full flex items-center justify-center">
              <div className="relative w-12 h-12">
                <Image
                  src={badgeIconMap[badge.title]}
                  alt={`${badge.title} icon`}
                  className={`object-contain ${badge.status === 'locked' ? 'opacity-25' : ''}`}
                  fill
                />
              </div>
            </div>

            {/* Title */}
            <h3 className="font-poppins font-semibold text-[20px] leading-[23px] text-center text-black pb-1">
              {badge.title}
            </h3>

            {/* Description */}
            <p className="font-normal text-[12px] leading-[18px] text-center font-[Poppins] mb-4 flex-grow">
              {badge.description}
            </p>

            {/* Progress Bar */}
            <div className="w-full h-1 bg-[#f3f3f3] rounded-full mt-2">
              <div
                className={`h-1 rounded-full ${badge.status === 'unlocked' ? 'bg-[#00a13b]' : 'bg-[#adadad]'}`}
                style={{ width: '100%' }}
              ></div>
            </div>

            {/* Status */}
            <div className="w-full mt-2 flex items-center justify-center">
              <Image
                src={badge.status === 'unlocked' ? "/assets/images/unlocked.png" : '/assets/images/locked.png'}
                alt={badge.status === 'unlocked' ? 'Unlocked' : 'Locked'}
                width={20}
                height={20}
                className="mr-1"
              />
              <span className={`text-xs ${badge.status === 'unlocked' ? 'text-[#0a5224]' : 'text-[#d9d9d9]'}`}>
                {badge.status === 'unlocked' ? 'Unlocked' : 'Locked'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AchievementBadges;
