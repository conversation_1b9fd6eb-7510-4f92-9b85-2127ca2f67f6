import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

const SortDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative " ref={dropdownRef}>
       
      <button 
        className=""
        onClick={toggleDropdown}
      >
         <div className='cursor-pointer flex items-center justify-center'>
        <Image
          src="/assets/images/sort_btn.png"
          alt="Sort"
          width={45}
          height={45}
          className="w-[35px] sm:w-[40px] lg:w-[45px] cursor-pointer "
        />
        </div>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
          <div className="py-1">
            <h3 className="px-4 py-2 text-purple-800  border-b border-gray-200 font-poppins font-semibold">Sort by</h3>
            
            <button className="w-full text-left px-4 py-2 flex items-center hover:bg-gray-100">
              <div className="mr-3">
                <Image
                       src="/assets/images/loder.png"
                       alt="New Update"
                       width={16}
                       height={16}
                       className="h-5 w-5 object-contain text-black"
                     />
              </div>
              <span>New Update</span>
            </button>
            
            <button className="w-full text-left px-4 py-2 flex items-center hover:bg-gray-100">
              <div className="mr-3">
              <Image
                       src="/assets/images/fire.png"
                       alt="New Update"
                       width={16}
                       height={16}
                       className="h-5 w-5 object-contain text-black"
                     />
              </div>
              <span>Trending</span>
            </button>
            
            <button className="w-full text-left px-4 py-2 flex items-center hover:bg-gray-100">
              <div className="mr-3">
              <Image
                       src="/assets/images/trophy2.png"
                       alt="New Update"
                       width={16}
                       height={16}
                       className="h-5 w-5 object-contain text-black"
                     />
              </div>
              <span>Popular</span>
            </button>
            
            <button className="w-full text-left px-4 py-2 flex items-center hover:bg-gray-100">
              <div className="mr-3">
               <Image
                       src="/assets/images/dot.png"
                       alt="Live"
                       width={5}
                       height={5}
                       className="h-2 w-2 object-contain mr-2"
                     />
              </div>
              <span>Live</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SortDropdown;