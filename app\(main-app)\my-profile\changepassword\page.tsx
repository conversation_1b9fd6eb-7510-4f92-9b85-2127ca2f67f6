"use client";

import { useState } from "react";
import { FaAngleLeft } from "react-icons/fa";
import { RiDeleteBinLine } from "react-icons/ri";
import { HiEye, HiEyeOff } from "react-icons/hi";
import Link from "next/link";

export default function ChangePasswordPage() {
  const [formData, setFormData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const toggleVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  return (
    <div className="flex flex-col px-4 md:px-[50px] py-10 font-poppins bg-white min-h-screen">
      <div className="flex gap-2 text-black text-[18px] font-normal items-center px-4">
        <Link href="/" className="hover:text-gray-800 flex items-center gap-1">
          <FaAngleLeft />
          Back to Home Page
        </Link>
        <span>{`>`}</span>
        <span className="font-semibold text-[#4B207A]">My Profile</span>
      </div>

      <div className="w-full max-w-[900px] mx-auto mt-12">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-[24px] md:text-[28px] font-semibold text-[#4B207A] underline">Change Password</h2>
          <button className="text-[#F10404] hover:text-red-800 flex items-center text-[16px] font-medium">
            <RiDeleteBinLine className="mr-2" />
            Delete this account
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Old Password */}
          <div className="relative">
            <label className="block text-[18px] text-[#4B207A] mb-1 font-medium">Old Password</label>
            <input
              type={showPasswords.old ? "text" : "password"}
              name="oldPassword"
              value={formData.oldPassword}
              onChange={handleInputChange}
              className="w-full p-2 border border-[#C3A4EC] text-[#404040] rounded-md focus:outline-none text-[18px] pr-10"
            />
            <span
              onClick={() => toggleVisibility("old")}
              className="absolute right-3 top-[40px] cursor-pointer text-[20px] text-gray-600"
            >
              {showPasswords.old ? <HiEye /> : <HiEyeOff />}
            </span>
          </div>

          {/* New Password */}
          <div className="relative">
            <label className="block text-[18px] text-[#4B207A] mb-1 font-medium">New Password</label>
            <input
              type={showPasswords.new ? "text" : "password"}
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              className="w-full p-2 border border-[#C3A4EC] text-[#404040] rounded-md focus:outline-none text-[18px] pr-10"
            />
            <span
              onClick={() => toggleVisibility("new")}
              className="absolute right-3 top-[40px] cursor-pointer text-[20px] text-gray-600"
            >
              {showPasswords.new ? <HiEye /> : <HiEyeOff />}
            </span>
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <label className="block text-[18px] text-[#4B207A] mb-1 font-medium">Confirm Password</label>
            <input
              type={showPasswords.confirm ? "text" : "password"}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="w-full p-2 border border-[#C3A4EC] text-[#404040] rounded-md focus:outline-none text-[18px] pr-10"
            />
            <span
              onClick={() => toggleVisibility("confirm")}
              className="absolute right-3 top-[40px] cursor-pointer text-[20px] text-gray-600"
            >
              {showPasswords.confirm ? <HiEye /> : <HiEyeOff />}
            </span>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <button
            className="px-10 py-2 bg-[#0A5224] hover:bg-green-700 text-white font-medium text-[18px] rounded-md"
          >
            Change Password
          </button>
        </div>
      </div>
    </div>
  );
}
