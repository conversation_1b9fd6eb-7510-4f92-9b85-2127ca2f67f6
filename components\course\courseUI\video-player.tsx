"use client"
import React, { useRef, useState, useEffect } from "react"
import {
  CirclePlay,
  CirclePause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Maximize2,
  Settings,
} from "lucide-react"

export function VideoPlayer() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  const togglePlay = () => {
    if (!videoRef.current) return
    if (videoRef.current.paused) {
      videoRef.current.play()
      setIsPlaying(true)
    } else {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }

  const skip = (seconds: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime += seconds
    }
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted
      setIsMuted(videoRef.current.muted)
    }
  }

  const toggleFullscreen = () => {
    if (videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        videoRef.current.requestFullscreen()
      }
    }
  }

  const formatTime = (time: number) => {
    const mins = Math.floor(time / 60)
    const secs = Math.floor(time % 60)
    return `${String(mins).padStart(2, "0")}:${String(secs).padStart(2, "0")}`
  }

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const width = rect.width
    const newTime = (clickX / width) * duration
    if (videoRef.current) {
      videoRef.current.currentTime = newTime
    }
  }

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTime = () => setCurrentTime(video.currentTime)
    const updateDuration = () => setDuration(video.duration)

    video.addEventListener("timeupdate", updateTime)
    video.addEventListener("loadedmetadata", updateDuration)

    return () => {
      video.removeEventListener("timeupdate", updateTime)
      video.removeEventListener("loadedmetadata", updateDuration)
    }
  }, [])

  return (
    <div className="flex-1 flex items-center">
      <div className="  items-center w-full flex flex-col p-[10px] md:px-[70px]">
      
<h1 className="text-[10px] md:text-[13px] lg:text-[20px] md:font-[500]   mt-3 mb-3 font-Poppins text-[#000000] ml-3 w-full">
                Lecture 1: 1st April: Introduction to prompt engineering
              </h1>
        {/* Video container */}
        <div className=" relative aspect-video  mb-6 rounded-none overflow-hidden  h-[200px] md:h-[300px] w-full lg:h-[342px] ">
          <video
            ref={videoRef}
            className="w-full h-full object-cover "
            src="/assets/video/dummyvid.mp4"
            preload="metadata"
          />

          {/* Controls Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            {/* Timeline */}
            <div className="flex justify-between items-center text-white text-xs">
              <span>{formatTime(currentTime)}</span>
              <div
                className="relative flex-1 mx-4 h-1 bg-gray-600 rounded-full cursor-pointer"
                onClick={handleSeek}
              >
                <div
                  className="absolute h-1 bg-white rounded-full"
                  style={{ width: `${(currentTime / duration) * 100 || 0}%` }}
                />
              </div>
              <span>{formatTime(duration)}</span>
            </div>

            {/* Control buttons */}
            <div className="flex justify-between items-center mt-2 text-white text-sm">
              <div className="flex items-center gap-3">
                <button onClick={togglePlay} className="p-1">
                  {isPlaying ? <CirclePause className="w-6 h-6" /> : <CirclePlay className="w-6 h-6" />}
                </button>
                <button onClick={() => skip(-10)} className="p-1">
                  <SkipBack className="w-6 h-6" />
                </button>
                <button onClick={() => skip(10)} className="p-1">
                  <SkipForward className="w-6 h-6" />
                </button>
                <button onClick={toggleMute} className="p-1">
                  {isMuted ? <VolumeX className="w-6 h-6" /> : <Volume2 className="w-6 h-6" />}
                </button>
              </div>

              <div className="flex items-center gap-3">
                <button className="p-1">
                  <Settings className="w-6 h-6" />
                </button>
                <button onClick={toggleFullscreen} className="p-1">
                  <Maximize2 className="w-6 h-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
