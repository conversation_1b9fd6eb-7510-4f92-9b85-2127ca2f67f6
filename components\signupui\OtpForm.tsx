"use client";

import React, { useState, useRef, ChangeEvent, KeyboardEvent } from "react";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";
import { useVerifyOtp } from "../../api-services/otp/otp"; // ✅ adjust path as needed

const OtpForm = () => {
  const [otp, setOtp] = useState<string[]>(new Array(6).fill(""));
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);
  const router = useRouter();

  const { mutate: verifyOtp, isPending, error } = useVerifyOtp();

  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;

    if (/^\d?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < inputRefs.current.length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = () => {
    const joinedOtp = otp.join("");
    if (joinedOtp.length === 6) {
      verifyOtp(
        { otp: parseInt(joinedOtp, 10) },
        {
          onSuccess: (data) => {
            console.log("Success:", data.message);
            router.push("/mycourse");
          },
          onError: (err: any) => {
            console.error("Verification failed:", err.message);
            alert("OTP verification failed. Please try again.");
          },
        }
      );
    }
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <div className="w-full bg-[#EBEBEB] p-6 sm:p-10 lg:p-14 rounded-2xl shadow-[4px_4px_10px_rgba(0,0,0,0.15)] relative font-poppins">
      <button
        className="absolute top-5 right-5 text-black hover:text-gray-700"
        onClick={handleClose}
      >
        <X size={24} />
      </button>

      <h2 className="text-[20px] lg:text-[32px] font-bold text-[#4B207A] mb-2">
        Login for ApexIQ <span className="text-[#0A5224]">EduTech</span>
      </h2>

      <p className="text-base text-[#003638] mb-8">
        You have received a{" "}
        <span className="text-[#0065FF] font-medium">verification code</span>{" "}
        on your registered Email address. Enter the{" "}
        <span className="text-[#0065FF] font-medium">OTP</span>.
      </p>

      <div className="flex justify-between gap-2 sm:gap-4 mb-8">
        {otp.map((digit, index) => (
          <input
            key={index}
            type="text"
            maxLength={1}
            placeholder="X"
            value={digit}
            onChange={(e) => handleChange(e, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            className="w-[34px] h-[34px] md:w-[79px] md:h-[65px] border border-black rounded-md text-center text-[16px] sm:text-[22px] text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0065FF]"
          />
        ))}
      </div>

      {error && (
        <p className="text-red-500 text-sm mb-4">
          {(error as Error).message || "Something went wrong"}
        </p>
      )}

      <button
        onClick={handleVerify}
        disabled={!otp.every((digit) => digit !== "") || isPending}
        className={`w-full py-3 sm:py-4 rounded-lg text-white font-semibold text-lg transition-all ${
          otp.every((digit) => digit !== "") && !isPending
            ? "bg-[#003F9A] hover:bg-[#002f7a]"
            : "bg-[#003F9A] opacity-50 cursor-not-allowed"
        }`}
      >
        {isPending ? "Verifying..." : "Verify"}
      </button>
    </div>
  );
};

export default OtpForm;
