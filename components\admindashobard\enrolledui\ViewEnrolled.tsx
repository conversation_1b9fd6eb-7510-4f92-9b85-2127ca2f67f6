"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { FiSearch, FiLink, FiSend } from "react-icons/fi";
import Image from "next/image";

// ✅ Dummy data
const dummyData = [
  {
    name: "Aditi S.",
    course: "Python",
    quizScore: "10/15",
    projects: [
      {
        title: "1. Prompt Assistant",
        description:
          "Build a prompt assistant that helps users write better prompts.",
        link: "https://drive.google.com/file/d/123456abcde/view",
      },
      {
        title: "2. AI Summary Generator",
        description:
          "Users ask questions, and AI provides summarized answers using well-structured prompts.",
        link: "https://drive.google.com/file/d/654321zyxwv/view",
      },
    ],
  },
   {
    name: "Aditi S.",
    course: "MongoDB",
    quizScore: "10/15",
    projects: [
      {
        title: "AI-Powered Resume Builder",
        description: "Users input basic details, and AI generates a professional resume using optimized prompts.",
        link: "https://drive.google.com/file/123456abcdef.view",
      },
      {
        title: "AI Study Assistant",
        description: "Users ask questions, and AI provides summarized answers using well-structured prompts.",
        link: "https://github.com/{file_name}/{repository_name}",
      },
    ],
  },
  {
    name: "Aditi S.",
    course: "MySQL",
    quizScore: "10/15",
    projects: [
      {
        title: "AI-Powered Resume Builder",
        description: "Users input basic details, and AI generates a professional resume using optimized prompts.",
        link: "https://drive.google.com/file/123456abcdef.view",
      },
      {
        title: "AI Study Assistant",
        description: "Users ask questions, and AI provides summarized answers using well-structured prompts.",
        link: "https://github.com/{file_name}/{repository_name}",
      },
    ],
  },
];
interface Project {
  title: string;
  link: string;
}
export default function ViewEnrolled() {
  const [searchTerm, setSearchTerm] = useState("");
  const [modalOpen, setModalOpen] = useState(false);
const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [feedback, setFeedback] = useState("");
  const router = useRouter();

  const handleBack = () => router.back();

  const filteredData = dummyData.filter((student) =>
    student.course.toLowerCase().includes(searchTerm.toLowerCase())
  );

 const openModal = (project: Project) => {
  setSelectedProject(project);
  setModalOpen(true);
};

  const closeModal = () => {
    setModalOpen(false);
    setFeedback("");
    setSelectedProject(null);
  };

  const sendFeedback = () => {
    console.log("Feedback sent for:", selectedProject?.title);
    console.log("Message:", feedback);
    closeModal();
  };

  return (
    <div className="p-4 sm:p-6 md:p-8 lg:p-10">
      {/* Header & Search */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-[30px]">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
        >
          <Image
            src="/assets/images/back-arrow.png"
            alt="Back"
            width={15}
            height={15}
          />
          Back
        </button>

        <div className="relative w-full md:w-[453px] rounded bg-[#fafafa] shadow-md">
          <FiSearch className="absolute left-3 top-3 text-gray-500" size={18} />
          <input
            type="text"
            placeholder="Search by course"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm rounded border border-gray-200 bg-transparent focus:outline-none"
          />
        </div>
      </div>

      {/* Student List */}
      <div className="space-y-8">
        {filteredData.map((student, idx) => (
          <div
            key={idx}
            className="bg-[#f3f3f3] shadow-md p-6 rounded-lg space-y-4"
          >
            <div className="text-[15px] font-semibold space-x-4">
              <span>
                <span className="font-bold">Student name :</span> {student.name}
              </span>
              <span>
                <span className="font-bold underline">Course :</span>{" "}
                {student.course}
              </span>
              <span>
                <span className="font-bold">Quiz Attempted :</span>{" "}
                {student.quizScore}
              </span>
            </div>

            <div>
              <p className="font-semibold mb-2">Projects Submitted :</p>
              {student.projects.map((project, i) => (
                <div key={i} className="mb-4">
                  <p className="font-medium text-[15px] mb-1">
                    {i + 1}) Title : {project.title}
                  </p>
                  <ul className="list-disc list-inside mb-2 text-[14px] text-gray-800">
                    <li>{project.description}</li>
                  </ul>
                  <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                    <div className="flex items-center border rounded px-3 py-2 bg-white w-full sm:w-auto">
                      <FiLink className="mr-2 text-gray-600" />
                      <a
                        href={project.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 text-sm break-all"
                      >
                        {project.link}
                      </a>
                    </div>
                    <button
                      onClick={() => openModal(project)}
                      className="border px-4 py-2 rounded text-sm font-medium hover:bg-gray-100"
                    >
                      Send Feedback
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Feedback Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-lg w-[90%] shadow-xl relative">
            <p className="font-bold text-[15px] mb-2">Feedback :</p>
            <textarea
              rows={6}
              className="w-full text-sm p-3 border border-gray-300 rounded focus:outline-none"
              placeholder="Write your feedback here..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            ></textarea>

            <div className="absolute -right-6 bottom-6">
              <button
                onClick={sendFeedback}
                className="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-md"
              >
                <FiSend size={24} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
