"use client";
import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const Partner = () => {
  const partners = [
    "/assets/images/fedex-express-61.png",
    "/assets/images/Vector(1).png",
    "/assets/images/fedex-express-61.png",
    "/assets/images/fedex-express-61.png",
  ];

  const settings = {
    dots: false,             
    infinite: true,          
    speed: 1000,             
    slidesToShow: 4,         
    slidesToScroll: 1,       
    autoplay: true,          
    autoplaySpeed: 2000,     
    arrows: false, // Hide arrows for better mobile experience
    responsive: [
      {
        breakpoint: 1024,
        settings: { slidesToShow: 4 },
      },
      {
        breakpoint: 768,
        settings: { slidesToShow: 3 },
      },
      {
        breakpoint: 480,
        settings: { slidesToShow: 2 },
      },
    ],
  };

  return (
    <div className="small-container border-b-2 border-t-2 border-gray-500 py-5">
      <div className="py-[30px] md:py-[70px]">
        <h2 className="text-[22px] md:text-[40px] font-semibold text-[#1D1D1D] text-center mb-[30px] md:mb-[70px] font-poppins">
        Trusted by over 800 companies
        </h2>
        <div className="max-w-[90%] mx-auto"> 
          <Slider {...settings}>
            {partners.map((image, index) => (
              <div key={index} className="flex justify-center">
                <img
                  src={image}
                  alt={`Partner ${index + 1}`}
                  className="w-[100px] sm:w-[120px] md:w-[150px] lg:w-[180px] max-w-full" 
                />
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </div>
  );
};

export default Partner;
