import { useMutation } from '@tanstack/react-query';
import { makeRequest } from '../utils'; // adjust this path as needed

export const useStudentLogout = () => {
  return useMutation({
    mutationFn: async () => {
      const refreshToken =
        localStorage.getItem('refreshToken') || sessionStorage.getItem('refreshToken');

      console.log('TOKEN before logout request:', localStorage.getItem('token'));
      console.log('REFRESH TOKEN before logout request:', refreshToken);

      if (!refreshToken) {
        console.warn('Refresh token not found, clearing storage.');
        localStorage.clear();
        sessionStorage.clear();
        return;
      }

      return await makeRequest({
        endpoint: '/accounts/logout/', 
        method: 'POST',
        data: { refresh: refreshToken },
        headers: {
          'Content-Type': 'application/json',
        },
      });
    },

    onSuccess: (response) => {
      localStorage.clear();
      sessionStorage.clear();
      console.log(response?.message || 'Student logged out successfully.');
    },

    onError: (error: any) => {
      const msg =
        error?.response?.data?.message || error?.message || 'Logout failed. Please try again.';
      console.error('Student logout failed:', msg);
      localStorage.clear();
      sessionStorage.clear();
    },
  });
};
