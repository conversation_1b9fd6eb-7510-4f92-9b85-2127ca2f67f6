import CertificateSection from "@/components/common/singlecourse/CertificateSection";
import CurriculumPage from "@/components/common/singlecourse/CurriculumPage";
import DeepLearningBenefits from "@/components/common/singlecourse/DeepLearningBenefits";
import Faculty from "@/components/common/singlecourse/faculty";
import PricingCard from "@/components/common/singlecourse/PricingCard";
import RealWorldProjects from "@/components/common/singlecourse/projects";
import Testimonials from "@/components/common/singlecourse/Testimonial";

import PaidHeader from "@/components/layout/PaidHeader";
import Image from "next/image";

const page = () => {
  return (
    <>
   
      <PaidHeader/>
      <section className="bg-gradient-to-r from-[#D4B4FF] via-[#D9D9D9] to-[#D9D9D9] pt-[50px] pb-[80px]">
        <div className="container">
          {/* Breadcrumb */}
          <nav className="text-[#4B4B4B] text-[10px] font-medium md:text-[20px] font-poppins">
  <a href="/paid-courses" className="hover:text-gray-900 cursor-pointer">
    Paid Course
  </a>{" "}
  &gt;{" "}
  <a href="/paid-courses/ethical-hacking-cybersecurity" className="hover:text-gray-900 cursor-pointer">
    Ethical Hacking & Cybersecurity
  </a>
</nav>


          {/* Content Section */}
          <div className="flex flex-col md:flex-row items-center">
  {/* Left Section - Text */}
  <div className="md:w-[60%] w-full">
    <h1 className="text-2xl md:text-[40px] font-medium text-black font-poppins">
      Ethical Hacking & Cybersecurity
    </h1>
    <p className="text-black text-sm md:text-[20px] mt-3 font-medium md:leading-[30px] font-poppins">
      Ethical Hacking & Cybersecurity protect systems from cyber threats by identifying vulnerabilities, preventing attacks, and ensuring data security. Ethical hackers use legal techniques to strengthen defenses and safeguard sensitive information.
    </p>
  {/* Price Section */}
  <div className="mt-8 flex gap-3 sm:gap-4 items-center font-poppins">
  <span className="relative text-[20px] md:text-[32px] font-[700] text-[#FFB200]">
  ₹1200
  <div className="absolute top-[14px] md:top-[24px] left-0 right-0 border-t-2 border-red-500"></div>
</span>

      <span className="text-[20px] md:text-[32px] font-[700] text-[#FF0000] ">₹1000</span>
      <span className="text-[13px] font-medium text-[#4CAF50]">20% Discount</span>
    </div>
    {/* Buttons */}
    <div className="mt-8 flex gap-3 sm:gap-4">
      <button className="sm:px-10 sm:py-3 px-8 py-2 font-semibold text-sm sm:text-xl bg-[#0A5224] text-white rounded-md hover:bg-green-800 transition">
        Buy
      </button>
      <button className="sm:px-10 sm:py-3 px-6 py-2 font-semibold border-2 border-[#0A5224] text-sm sm:text-xl text-[#0A5224] rounded-md hover:bg-green-100 transition">
        Share
      </button>
    </div>
  </div>

  {/* Right Section - Image */}
  <div className="md:w-[40%] w-full mt-8 md:mt-0 flex md:justify-end justify-around pr-[40px] md:pr-0 ">
    <div className="relative">
      <div className="absolute top-[30px] left-[30px] w-full h-full bg-gradient-to-r from-[#6721C3] to-[#0A5224] rounded-lg transform shadow-lg"></div>
      <Image
        src="/ethical.png"
        alt="Neural Networks & Deep Learning"
        width={400}
        height={250}
        className="rounded-lg shadow-lg relative z-10"
      />
    </div>
  </div>
</div>


        </div>
      </section>

      <DeepLearningBenefits/>
      <CurriculumPage/>
      <RealWorldProjects/>
      <Faculty/>
      <PricingCard/>
      <CertificateSection/>
      <h2 className="text-base px-10 sm:px-0 sm:text-3xl md:text-4xl font-poppins font-semibold text-center pt-3 sm:pt-10 mb-2 sm:mb-4">
        Champions of Change: Alumni Experiences
      </h2>
      <p className="text-black text-center font-medium text-[11px] sm:text-base md:text-lg px-4 sm:px-10 md:px-20 mb-5 sm:mb-10">
        Unlock the potential within our alumni s experiences and witness the
        transformative power of upscaling
        <span className="block">on their careers and lives.</span>
      </p>

      <Testimonials/>

    </>
  );
};

export default page;
