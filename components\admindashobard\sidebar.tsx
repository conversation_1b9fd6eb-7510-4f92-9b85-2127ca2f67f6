"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { LogOut, X } from "lucide-react";
import { useAdminLogout } from "../../api-services/adminlogout/AdminLogout"; // ✅ adjust path if needed

interface MenuItemProps {
  icon?: React.ReactNode;
  imageSrc?: string;
  label: string;
  href: string;
  badge?: number;
}

const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  imageSrc,
  label,
  href,
  badge,
}) => {
  const pathname = usePathname();
  const isActive = pathname.startsWith(href);

  const baseClasses =
    "flex items-center gap-2 px-4 pl-[40px] py-2 rounded-md text-sm font-poppins text-[16px] md:text-[18px] lg:text-[20px] leading-[100%] tracking-[-0.01em]";
  const activeClasses =
    "border border-black font-semibold bg-white text-black lg:py-3 lg:px-4 hover:px-4";
  const hoverClasses =
    "hover:border hover:border-black hover:font-semibold hover:bg-white hover:text-black lg:py-3 lg:px-4 hover:px-4";

  return (
    <Link
      href={href}
      className={`${baseClasses} ${isActive ? activeClasses : "text-black "} ${hoverClasses}`}
    >
      <span className="text-gray-500 flex items-center justify-between">
        {imageSrc ? (
          <Image
            src={imageSrc}
            alt={label}
            width={30}
            height={30}
            className="w-[30px] h-[30px] justify-center"
          />
        ) : (
          icon
        )}
      </span>
      <span className="px-4">{label}</span>
      {badge !== undefined && <span>{badge}</span>}
    </Link>
  );
};

const MainMenu: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();

  const { mutate: logout, status } = useAdminLogout();
const isLoading = status === "pending";


 const handleLogout = () => {
  logout(undefined, {
    onSuccess: (data) => {
      console.log(data?.message || 'Logged out'); // ✅ avoid crashing if `data` is undefined
      setShowModal(false);
      router.push("/Admin/login");
    },
    onError: (error: any) => {
      console.error("Logout failed:", error?.message || 'Unexpected logout error'); // ✅ safely handle missing message
      setShowModal(false);
    },
  });
};


  return (
    <div className="flex flex-col h-full bg-[#F2F2F2]">
      <div className="flex-1 overflow-y-auto px-5 py-4 mt-8">
        <nav className="flex flex-col space-y-3 lg:space-y-4">
          <MenuItem
            imageSrc="/assets/images/dashboard2.png"
            label="My Dashboard"
            href="/Admin/dashboard"
          />
          <MenuItem
            imageSrc="/assets/images/reading.png"
            label="Enrolled Students"
            href="/Admin/enrolledcourse"
          />
          <MenuItem
            imageSrc="/assets/images/legal-document1.png"
            label="Certificate Tracker"
            href="/Admin/certificatetracker"
          />
          <MenuItem
            imageSrc="/assets/images/badge1.png"
            label="Badge Tracker"
            href="/Admin/BadgeTracker"
          />
        </nav>
      </div>

      <div className="px-5 py-4">
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center gap-2 px-4 pl-[40px] py-2 rounded-md text-sm font-poppins text-[16px] md:text-[18px] lg:text-[20px] leading-[100%] tracking-[-0.01em] w-full hover:border hover:border-black hover:font-semibold hover:bg-white hover:text-black lg:py-3 lg:px-4 hover:px-4"
        >
          <LogOut className="w-6 h-6 text-black" />
          <span className="text-black">Log out</span>
        </button>
      </div>

      {/* Logout Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-[#00000047] z-[99] flex items-center justify-center">
          <div className="bg-white rounded shadow-lg p-6 relative w-[90%] max-w-md text-center">
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-2 right-3 text-black text-2xl"
            >
              <X />
            </button>
            <h2 className="text-red-600 font-bold text-xl mb-2">Logout Confirmation</h2>
            <p className="text-black mb-4">Are you sure you want to log out?</p>
            <div className="flex justify-center gap-6">
              <button
                onClick={() => setShowModal(false)}
                className="border border-black px-6 py-2 rounded"
              >
                No
              </button>
              <button
                onClick={handleLogout}
                className="border border-red-600 text-red-600 px-6 py-2 rounded"
              >
                {isLoading ? "Logging out..." : "Confirm"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MainMenu;
