// api-services/forgetpassword/resetPassword.ts
import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils"; // your helper for API calls

// Payload type matching your API request body
interface ResetPasswordPayload {
  new_password: string;
  confirm_password: string;
}

// Response type matching your API response
interface ResetPasswordResponse {
  success: string;
}

// API call function
async function resetPassword(payload: ResetPasswordPayload): Promise<ResetPasswordResponse> {
  const response = await makeRequest({
    endpoint: "/accounts/auth/reset-password/NDE/cquks7-ff043fe039cfc18d9364892b83b50666/", // adjust to your actual endpoint
    method: "POST",
     data: { ...payload },
  });
  return response;
}

// React Query hook
const useResetPassword = () => {
  return useMutation<ResetPasswordResponse, Error, ResetPasswordPayload>({
    mutationFn: resetPassword,
    onError: (error) => {
      console.error("Reset password error:", error);
    },
  });
};

export { useResetPassword };
