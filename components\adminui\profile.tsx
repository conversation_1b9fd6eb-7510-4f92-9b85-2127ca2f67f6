"use client";
import React, { useRef, useState , useEffect} from 'react';
import Image from 'next/image';
import InputField from '@/components/common/adminCommon/InputField';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

import { Pencil } from 'lucide-react';
const ProfileForm: React.FC = () => {
  
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    secretKey: '',
    password: '',
    confirmPassword: '',
  });

  
    const phoneRegex = React.useMemo(() => /^\+?[1-9]\d{1,14}$/, []);
   const [isFormValid, setIsFormValid] = useState(false);
 
   // Validation regexes
   const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);
   
 useEffect(() => {
    const { name, email, phone,  } =
      formData;

    const isValid: boolean =
      name.trim() !== '' &&
      emailRegex.test(email) &&
      phoneRegex.test(phone);
     

    setIsFormValid(isValid);
  }, [formData, emailRegex, phoneRegex,]);
 
   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     const { name, value } = e.target;
     setFormData(prev => ({ ...prev, [name]: value }));
   };
 
   const handleSubmit = (e: React.FormEvent) => {
     e.preventDefault();
     console.log("Form Data Submitted:", formData);
     // Add backend call or OTP verification here
   };
 

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4 py-8 ">
      <div className="bg-[#eeeeee] rounded-md shadow-md pt-8 lg:w-[914px] md:w-[600px]">
        <div className="flex justify-center">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={130}
            height={60}
            className="h-[55px] w-auto"
          />
        </div>
        <div className='px-15'>
          <p className="text-center justify-center font-[Poppins] font-semibold text-[20px] md:text-[32px] leading-[110%] tracking-[-0.01em] text-[#4b207a] mb-1">
            Admin Profile
          </p>
        </div>
        <form
          onSubmit={handleSubmit}
          className="space-y-2 w-full flex flex-col items-center pt-3 py-5 pb-10"
        >
          {/* Clickable Profile Image */}
          <div
            className="w-20 h-20 rounded-full mx-auto bg-gray-100 border flex items-center justify-center cursor-pointer overflow-hidden "
            onClick={handleImageClick}
          >
            {profileImage ? (
              <Image
                src="/assets/images/img.png"
                alt="Profile"
                width={96}
                height={96}
                className="w-full h-full object-cover"
              />
            ) : (
              <Image
                src="/assets/images/img.png"
                alt="Placeholder"
                width={48}
                height={48}
                className="w-6 h-6 opacity-50"
              />
            )}
            <input
              type="file"
              accept="image/*"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          <div className="w-full md:w-[75%]">
          <InputField
  id="name"
  name="name"
  type="text"
  value={formData.name}
  onChange={handleChange}
  label="Enter Name"
  suffix={
    <div className="flex items-center space-x-1">
     
      <Pencil size={14} />
      <span>Edit</span>
    </div>
  }
/>
          </div>
          <div className="w-full md:w-[75%]">
            <InputField
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              label="Enter Email"
            />
          </div>

          <div className="relative mb-6 w-full md:w-[75%]">
            <PhoneInput
              country={'in'}
              value={formData.phone}
              onChange={(phone) => setFormData({ ...formData, phone })}
              inputStyle={{
                width: '100%',
                backgroundColor: '#eeeeee',
                border: '1px solid black',
                borderRadius: '4px',
                height: '57px',
                paddingLeft: '48px',
                fontSize: '14px',
              }}
              buttonStyle={{
                backgroundColor: '#eeeeee',
                borderRight: '1px solid black',
                borderTop: '1px solid black',
                borderBottom: '1px solid black',
                borderLeft: '1px solid black',
              }}
              containerClass="w-full"
              inputProps={{
                name: 'phone',
                required: true,
              }}
            />
            <label className="absolute left-3 px-5 top-[-10px] text-center font-medium text-[16px] leading-[30px] tracking-[0] font-[Poppins] text-black bg-[#eeeeee]">
              Phone Number
            </label>
           {/* Edit Icon */}
  <div className="absolute right-3 top-[50%] -translate-y-1/2 flex items-center gap-1 text-sm text-[#4b207a] font-medium cursor-pointer">
  <Pencil size={14} />
    <span>Edit</span>
    
  </div>
          </div>

          <div className="w-full flex items-center justify-center">
            <button
              type="submit"
              disabled={!isFormValid}
              className={`py-3 px-10 rounded-lg font-bold   md:text-[24px] text-white font-[Poppins] ${
                isFormValid
                  ? 'bg-[#4b207a] opacity-100 cursor-pointer'
                  : 'bg-[#4b207a] opacity-50 cursor-not-allowed'
              }`}
            >
             Profile Update
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfileForm;


