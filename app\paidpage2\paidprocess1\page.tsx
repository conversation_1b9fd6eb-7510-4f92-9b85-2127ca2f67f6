import Image from "next/image";
import Link from "next/link"; // Import Link from Next.js
import PaidHeader from "@/components/layout/PaidHeader"; // Assuming you already have this component

const page = () => {
  return (
    <>
      <PaidHeader />

      <section className="pt-[30px] pb-[30px] min-h-screen">
        <div className="container font-poppins">
          <div className="flex justify-between items-start mb-6">
            {/* Back Arrow */}
            <Link href="/paidpage2" className="text-[32px] text-black ">
              &lt;
            </Link>
            <div className="text-right">
              <h3 className="text-[20px] md:text-[32px] font-[700] text-[#0A5224]">
                Make your safe <br /> payment
              </h3>
            </div>
          </div>

          <div className="flex justify-center items-center mt-[30px]">
            <div className="w-full max-w-[950px]">
              <div className="flex flex-col md:flex-row items-center md:gap-[40px]">
                <div className="flex-1 mb-6 md:mb-0">
                  <div className="relative mb-6">
                    <Image
                      src="/image.png" // Replace with the path to your image
                      alt="Ethical Hacking & Cybersecurity"
                      width={500}
                      height={300}
                      className="rounded-lg shadow-lg"
                    />
                  </div>
                </div>

                <div className="flex-1">
                  <h3 className="text-black text-[22px] md:text-[40px] font-medium md:leading-[50px]">
                    Ethical Hacking & Cybersecurity
                  </h3>

                  <div className="mt-6">
                    <div className="flex justify-between text-[20px] md:text-[24px] font-[400] text-black mb-3" >
                      <span>Subtotal</span>
                      <span className="font-[700]">₹1200</span>
                    </div>
                    <div className="flex justify-between text-[20px] md:text-[24px] font-[400] text-black mb-3">
                      <span>Discount</span>
                      <span className="font-[700]">-₹200</span>
                    </div>
                    <div className="flex justify-between text-[20px] md:text-[24px] font-[700] text-[#0A5224] mt-4 border-t-[1px] border-b-[1px] border-solid border-black py-3">
  <span>Total</span>
  <span className="font-[700]">₹1000</span>
</div>

                  </div>

                  {/* Proceed to Checkout Button */}
                  <div className="mt-8 w-full">
                    <Link
                      href="/checkout"
                      className="w-full block text-center sm:px-10 sm:py-3 px-8 py-2 font-semibold text-sm sm:text-[26px] font-sans bg-[#0A5224] text-white rounded-md hover:bg-green-800 transition"
                    >
                      Proceed to Checkout
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default page;
