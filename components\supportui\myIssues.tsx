'use client';

import { useState } from 'react';

interface Issue {
  id: number;
  title: string;
  category: string;
  comment: string;
  phone: string;
  batch: string;
  clientType: string;
  createdDate: string;
  solvedDate: string;
  status: 'Resolved' | 'In Process';
}

const issues: Issue[] = [
  {
    id: 1,
    title: "Subject Related Doubt",
    category: "Coding Related Doubt",
    comment: "I am not getting logic behind Python patterns code",
    phone: "8585838380",
    batch: "Full Stack Data Science Pro",
    clientType: "Web / Client version 2.0.0",
    createdDate: "10 May 2024",
    solvedDate: "12 Oct 2024",
    status: "Resolved",
  },
  {
    id: 2,
    title: "Subject Related Doubt",
    category: "Project Related Doubt",
    comment: "hjvli3ukgjl32hb2hei2j2yu2hej2il21y2he3ie",
    phone: "8585838380",
    batch: "Full Stack Data Science Pro",
    clientType: "Web / Client version 2.0.0",
    createdDate: "10 May 2024",
    solvedDate: "Not yet",
    status: "In Process",
  },
];

const tabs: Array<'All' | 'In Process' | 'Resolved'> = ["All", "In Process", "Resolved"];

const MyIssues = () => {
  const [activeTab, setActiveTab] = useState<'All' | 'In Process' | 'Resolved'>('All');

  const filteredIssues = issues.filter(issue =>
    activeTab === "All" || issue.status === activeTab
  );

  return (
    <div className="">
      {/* Tabs Section */}
      <div className="flex justify-center md:justify-end w-full mb-4">
        
  <div className="flex border border-black rounded-[15px] overflow-hidden">
    {tabs.map((tab) => (
      <button
        key={tab}
        onClick={() => setActiveTab(tab)}
        className={`px-5 py-2 border-r border-black last:border-r-0 transition text-xs md:text-lg  ${
          activeTab === tab ? "bg-[#DDDDDD] text-black text-xs md:text-lg  md:font-semibold" : "bg-[#F3F3F3] text-black"
        }`}
      >
        {tab} ({issues.filter(issue => tab === "All" || issue.status === tab).length})
      </button>
    ))}
  </div>
</div>


      {/* Issue Cards */}
      <div className="w-full max-w-[828px] p-3 md:px-[40px]">
        {filteredIssues.length > 0 ? (
          filteredIssues.map(issue => (
            <div 
              key={issue.id} 
              className="border-[1px] border-[#BCBCBC] rounded-lg p-3 md:p-6 mb-4 relative flex flex-col"
            >
              {/* Status Button on Top Right */}
              <div className="absolute top-3 right-3">
                <span
                  className={`px-4 py-1 text-sm font-semibold rounded-full ${
                    issue.status === "Resolved" ? "bg-green-200 text-green-700" : "bg-yellow-200 text-yellow-700"
                  }`}
                >
                  {issue.status}
                </span>
              </div>

              <h2 className="text-[15px] md:text-[20px] font-semibold">{issue.title}</h2>
              <p className="text-[10px] md:text-[14px] font-normal">{issue.category}</p>

              <div className="flex flex-col md:flex-row justify-between gap-4 items-start mt-2">
                {/* Issue Comment Box */}
                <div className="bg-gray-100 p-3 rounded-md w-full md:w-2/3">
                  <h4 className="text-[18px] font-medium">Issue Comment:</h4>
                  <p className="text-[11px] font-medium">{issue.comment}</p>
                  <p className="text-[11px] font-medium">Phone: {issue.phone}</p>
                  <p className="text-[11px] font-medium">Batch Name: {issue.batch}</p>
                  <p className="text-[11px] font-medium">Client Type: {issue.clientType}</p>
                </div>

                {/* Issue Created & Solved Dates */}
                <div className="text-[#555555] w-full md:w-1/3">
                  <p>
                    <span className="text-[15px] md:text-[18px] font-medium text-black">Issue Created: </span>
                    {issue.createdDate}
                  </p>
                  <p>
                    <span className="text-[15px] md:text-[18px] font-medium text-black">Issue Solved: </span>
                    <span className={issue.solvedDate === "Not yet" ? "text-orange-500" : ""}>
                      {issue.solvedDate}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500 p-6">
            No issues found for {activeTab}.
          </div>
        )}
      </div>
    </div>
  );
};

export default MyIssues;
