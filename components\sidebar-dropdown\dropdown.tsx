"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useStudentLogout } from "../../api-services/logout/logout" 

export default function ProfileDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const logoutMutation = useStudentLogout()

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: (data) => {
        console.log(data?.message || "Logout successful")
        router.push("/login") // ✅ Redirect to login
      },
      onError: (error) => {
        const msg = error?.response?.data?.message || error?.message || "Logout failed"
        console.error("Logout failed:", msg)
      },
    })
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Image Button */}
      <button onClick={toggleDropdown} className="focus:outline-none" aria-expanded={isOpen} aria-haspopup="true">
        <div className="h-14 w-14 mt-2 rounded-full overflow-hidden border-2 border-transparent transition-all cursor-pointer">
          <Image
            src="/assets/images/image(9).png"
            alt="Profile"
            width={48}
            height={48}
            className="object-cover"
          />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg overflow-hidden z-[999]">
          <div className="p-4">
            <h3 className="text-purple-900 text-xl font-semibold font-poppins">Hi</h3>
            <h4 className="text-purple-900 text-xl font-semibold font-poppins">Dineshka.oK</h4>
          </div>

          <div className="border-b border-gray-200"></div>

          <div className="py-1">
            <Link href="/mycourse" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100">
              <Image src="/assets/images/Maskgroup(15).png" alt="Courses" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">My Courses</span>
            </Link>

            <Link href="/progress-tracker" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100">
              <Image src="/assets/images/Maskgroup(16).png" alt="Dashboard" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">My Dashboard</span>
            </Link>

            <Link href="/my-purchases" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100">
              <Image src="/assets/images/Maskgroup(17).png" alt="Purchases" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">My purchases</span>
            </Link>

            <Link href="/my-profile" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100">
              <Image src="/assets/images/Maskgroup(8).png" alt="Profile" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">My Profile</span>
            </Link>

            <Link href="/support" className="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100">
              <Image src="/assets/images/Maskgroup(9).png" alt="Support" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">Support</span>
            </Link>

            {/* ✅ Logout button */}
            <button
              onClick={handleLogout}
              disabled={logoutMutation.isPending}
              className="flex items-center w-full text-left px-4 py-3 text-gray-700 hover:bg-gray-100"
            >
              <Image src="/assets/images/Maskgroup(10).png" alt="Logout" width={24} height={24} className="mr-3" />
              <span className="text-gray-600 font-poppins">
                {logoutMutation.isPending ? "Logging out..." : "Log out"}
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
