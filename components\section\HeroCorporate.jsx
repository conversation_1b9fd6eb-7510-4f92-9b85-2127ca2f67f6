import React from 'react'

const HeroCorporate = () => {
  return (
   <>
    {/* <!-- hero section --> */}
     <section className="bg-[#d2d2d2] shadow-lg h-[500px]    ">
      <div className=" min-h-fit flex items-center">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
                {/* <!-- Left Column - Text Content --> */}
                <div className="w-full lg:w-1/2 text-center lg:text-left  ">
                    <h1 className="text-4xl sm:text-6xl font-bold mb-4">
                        Elevate Your Skills with 
                        <span className="bg-gradient-to-r from-[#4B207A] to-[#0A5224] bg-clip-text text-transparent">ApexIQ</span>
                    </h1>
                    <p className="text-lg font-karla font-bold sm:text-xl text-black mb-8 max-w-2xl">
                        Empower your workforce of your team with cutting-edge skills through expert-led training in AI, data science, cloud computing, and more.
                    </p>
                    <button className="bg-blue-600 text-white px-8 py-3 rounded-md text-lg font-semibold hover:bg-blue-700 transition-colors">
                        Sign Up
                    </button>
                </div>

                {/* <!-- Right Column - Image --> */}
                <div className="w-full lg:w-1/2">
                    <img 
                        src="/assets/images/imaage.png"
                        alt="Business professionals shaking hands " 
                        className="min-w-auto h-auto rounded-lg hidden lg:block "
                    />
                </div>
            </div>
        </div>
    </div>
     </section>
   </>
  )
}

export default HeroCorporate