'use client'

import Image from "next/image"

interface StatCardsProps {
  totalCourses: number
  completedCourses: number
  tasksSubmitted: number
  totalTasks: number
}

export default function StatCards({ totalCourses, completedCourses, tasksSubmitted, totalTasks }: StatCardsProps) {
  return (
    <div>
        <div className="mb-3">
          <p className="font-poppins font-medium text-[24px]  text-[#4B207A] ">You studied for 5hrs this week!</p>
        </div>
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-[25px]">
      <StatCard
        title="Total Course Enrolled"
        value={totalCourses.toString()}
        imageSrc="/assets/images/bookdash.png"
      />
      <StatCard
        title="Complete Courses"
        value={completedCourses.toString().padStart(2, "0")}
        imageSrc="/assets/images/bookpen.png"
      />
      <StatCard
        title="Total Task & Project Submitted"
        value={`${tasksSubmitted}/${totalTasks}`}
        imageSrc="/assets/images/bookpen.png"
      />
    </div>
    </div>
  
  )
}

interface StatCardProps {
  title: string
  value: string
  imageSrc: string

}

function StatCard({ title, value, imageSrc }: StatCardProps) {
  return (
    
    <div className="bg-white p-6 rounded-lg border border-black flex flex-col items-start text-start">
     
      <div className=" flex justify-center items-center pb-5 ">
        <Image src={imageSrc} alt={title} width={50} height={50} />
      </div>
      <div className="font-poppins font-medium text-[32px] leading-[100%] tracking-[-0.01em] text-black  ">{value}</div>
      <div className="font-poppins font-bold text-[14px] leading-[100%] tracking-[-0.01em] text-black py-4">{title}</div>
    </div>
  )
}
