// components/Testimonials.tsx

import { abouttestimonials } from "@/utils/constant";
import Image from "next/image";
import React from "react";




const Testimonials: React.FC = () => {
  return (
    <section className="py-12 bg-white pt-0">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-xl sm:text-4xl font-poppins font-semibold mb-10">
          What our Students Say
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-10">
          {abouttestimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-[#F9F9F9] p-6 rounded-xl shadow-md text-left"
            >
              <div className="flex items-center mb-4">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  width={50}
                  height={50}
                  className="rounded-full"
                />
                <div className="ml-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-sm sm:text-base text-gray-900 font-poppins">
                      {testimonial.name}
                    </h3>
                  </div>
                  <p className="text-[#5C5C5C] text-xs sm:text-sm font-poppins">
                    {testimonial.title}
                  </p>
                </div>
                <div className="ml-auto flex">
                  {[...Array(Math.floor(testimonial.rating))].map((_, i) => (
                    <span key={i} className="text-yellow-500 text-lg">
                      ★
                    </span>
                  ))}
                  {testimonial.rating % 1 !== 0 && (
                    <span className="text-yellow-500 text-lg">☆</span>
                  )}
                </div>
              </div>
              <p className="text-[#4B4B4B] text-xs sm:text-sm font-poppins">
                {testimonial.feedback}
              </p>
            </div>
          ))}
        </div>
      </div>
      <div className="max-w-[1065px] mx-auto text-center ">
    <hr className="mt-8 sm:mt-20 border-black border-b-1 " />
    </div>
    </section>
  );
};

export default Testimonials;
