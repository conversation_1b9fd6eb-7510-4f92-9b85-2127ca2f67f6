import { Search, X } from "lucide-react";
import { useState } from "react";

const SearchBar = () => {
  const [searchText, setSearchText] = useState("");

  const clearSearch = () => {
    setSearchText("");
  };

  return (
    <div className="flex-1 w-full max-w-md relative">
    <div className="relative mt-5  sm:w-[400px] md:w-[500px] lg:w-[661px] h-[55px] lg:h-[61px] shadow-md text-black rounded-xl flex items-center border-none">
      <div className="bg-[#f1f1f1] w-[60px] h-full rounded-none flex items-center justify-between px-6 text-black">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#4B207A] h-5 w-5 sm:h-7 sm:w-7 font-poppins" />
      <input
  type="text" 
  onChange={(e) => setSearchText(e.target.value)}
  value={searchText} 
  placeholder="Search your courses"
  className="font-poppins font-medium text-[18px] leading-[27px] tracking-[-0.019em] text-[#656565]"
/>
     
    </div>
    <button 
        className="absolute right-3 top-1/2 transform -translate-y-1/2  "
        onClick={clearSearch}
      >
        <X className="h-5 w-5 sm:h-6 sm:w-6" />
      </button>
    </div>
    </div>
    
  );
};

export default SearchBar;