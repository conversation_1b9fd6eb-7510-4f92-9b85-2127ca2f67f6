import Image from 'next/image';
import { FaCircle } from "react-icons/fa";

const CertificateSection: React.FC = () => {
    return (
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
            {/* Header */}
            <h1 className="text-xl my-[40px] sm:mt-0 sm:text-[40px] font-semibold font-poppins text-center  sm:mb-8">
                Validating Your Success: About Your Certificate
            </h1>

            {/* Main Content */}
            <div className="flex flex-col lg:grid lg:grid-cols-2 items-center lg:pl-20 gap-6 lg:gap-0">
                {/* Certificate Preview */}
                <div className="rounded-lg shadow-md w-full sm:w-auto">
                    <Image 
                        src="/image(9).png" 
                        alt="Sample Certificate" 
                        width={600} 
                        height={400} 
                        className="w-full h-auto rounded-md"
                    />
                </div>

                {/* Certificate Information */}
                <div className="bg-gradient-to-b from-white to-[#B78DFF] rounded-lg p-4 sm:p-6 shadow-md w-full sm:w-[454px]">
                    <h2 className="text-base sm:text-[32px] font-medium font-poppins text-center text-black mb-3">
                        Neural Networks and Deep Learning
                    </h2>
                    <p className="text-sm sm:text-[24px] font-normal font-poppins text-black mb-4 text-center sm:text-left">
                        Once you complete the course videos, assignments, and quizzes, you will be able to generate the certificate.
                    </p>
                    <ul className="space-y-2 sm:space-y-1 text-sm sm:text-[24px] font-normal font-poppins px-2 sm:px-4 text-black">
                        <li className="flex items-center gap-2">
                            <span className="text-[9px] text-black "><FaCircle /></span>
                            After watching 60% of videos.
                        </li>
                        <li className="flex items-center gap-2">
                        <span className="text-[9px] text-black "><FaCircle /></span>
                            After scoring 60% in quiz & assignment.
                        </li>
                        <li className="flex items-center gap-2">
                        <span className="text-[9px] text-black "><FaCircle /></span>
                            After completing Intermediate level Project on Experience Portal.
                        </li>
                    </ul>
                </div>
            </div>

            {/* Button */}
            <div className="flex justify-center mt-6">
                <button className="text-center bg-[#0A5224] font-bold text-white py-3 sm:py-4 px-4 sm:px-6 rounded-full hover:bg-green-800 transition-colors text-sm sm:text-2xl shadow-lg">
                    To see the max Certificate
                </button>
            </div>

            <div className="w-4/5 mx-auto border-b-2 border-black mt-10 sm:mt-20"></div>
        </div>
    );
};

export default CertificateSection;
