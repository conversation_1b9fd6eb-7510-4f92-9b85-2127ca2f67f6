interface QuizQuestionProps {
    question: {
      id: number
      question: string
      options: string[]
      correctAnswer: number
    }
    selected?: number
    onSelect: (id: number, option: number) => void
  }
  
  export default function QuizQuestion({ question, selected, onSelect }: QuizQuestionProps) {
    return (
      <div className="mb-1 w-full">
        <div className="py-3 sm:py-4 md:py-6 font-semibold text-sm sm:text-[16px] leading-[130%] sm:leading-[100%] tracking-[-0.01em] font-poppins text-black">
          {question.id}. {question.question}
        </div>
        <div className="space-y-2 md:space-y-2 ">
          {question.options.map((opt, idx) => (
            <div key={idx} className="flex items-start sm:items-center mb-2 ">
              <input
                type="radio"
                id={`q${question.id}-opt${idx}`}
                name={`q${question.id}`}
                checked={selected === idx}
                onChange={() => onSelect(question.id, idx)}
                className="mt-1 sm:mt-0  cursor-pointer"
              />
              <label 
                htmlFor={`q${question.id}-opt${idx}`} 
                className="ml-2 font-normal text-sm sm:text-[16px] leading-tight sm:leading-[100%] tracking-[-0.01em] font-poppins text-black p-1"
              >
                {opt}
              </label>
            </div>
          ))}
        </div>
      </div>
    )
  }