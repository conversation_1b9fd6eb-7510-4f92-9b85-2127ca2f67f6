'use client'

import Image from "next/image"

export default function SimilarCourses() {
  const courses = [
    {
      title: "Model Evaluation & Performance Metrics",
      image: "/image(11).png",
      tag: "Trending",
      price: 1000,
    },
    {
      title: "Transfer Learning in Generative Models",
      image: "/image(12).png",
      tag: undefined,
      price: 1000,
    },
    {
      title: "Evaluation of Model Outputs Based on Prompts",
      image: "/image(11).png",
      tag: "Popular",
      price: 1000,
    },
    {
      title: "CI/CD for ML Models using Jenkins or GitHub",
      image: "/image(9).png",
      tag: undefined,
      price: 1000,
    },
  ]

  return (
    <section className="mt-12 pb-[60px]">
      <h2 className="text-[20px] font-medium text-black mb-4 px-4">
        Related Paid Courses
      </h2>

      <div className="overflow-x-auto w-full px-4">
        <div
          className="flex gap-4 pb-4"
          style={{
            width: 'calc(363px * 4 + 3 * 16px)', // 4 cards + 3 gaps
          }}
        >
          {courses.map((course, index) => (
            <div
              key={index}
              className="min-w-[363px] max-w-[363px] bg-[#f2f2f2] rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="relative w-full" style={{ height: '268px' }}>
                <Image
                  src={course.image}
                  alt={course.title}
                  fill
                  className="object-cover"
                />
                {course.tag && (
                  <span
                    className={`absolute top-2 right-2 text-xs px-2 py-1 rounded font-semibold flex items-center gap-1 ${
                      course.tag === "Trending"
                        ? "bg-blue-600 text-white"
                        : "bg-yellow-400 text-black"
                    }`}
                  >
                    {course.tag === "Trending" ? "🔥 Trending" : "🏆 Popular"}
                  </span>
                )}
              </div>
              <div className="p-[25px]">
                <h3 className="text-[20px] font-medium text-black mb-[20px]">
                  {course.title}
                </h3>
                <div className="flex items-center justify-between mt-3">
                  <span className="text-[#4CAF50] border border-[#4CAF50] text-[25px] font-[700] px-4 py-1 rounded-md">
                    paid
                  </span>
                  <span className="text-[#FF8400] font-[700] text-[32px]">
                    ₹{course.price}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
