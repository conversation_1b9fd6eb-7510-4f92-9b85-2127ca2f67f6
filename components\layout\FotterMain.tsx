import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const FotterCon: React.FC = () => {
  return (
    <>
      {/* footer */}
      <footer className="bg-[#353535] text-white py-12 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Company Info Column */}
          <div className="space-y-4">
            <div className="flex items-center w-[142px] h-[62px]">
              <Image src="/assets/images/logo.png" alt="Company Logo" width={142} height={62} className='' />
            </div>
            <div className="space-y-4 mt-7 ">
              <div className="flex items-center gap-2 ">
                <Image className="w-[40px] h-[30px]" src="/assets/images/mail1.png" alt="Mail Icon" width={40} height={30} />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Image className="w-[30px] h-[30px]" src="/assets/images/phone.png" alt="Phone Icon" width={30} height={30} />
                <span>************</span>
              </div>
              <div className="flex items-center gap-2 w-[300px] ">
                <Image className="w-[35px] h-[35px]" src="/assets/images/map.png" alt="Location Icon" width={35} height={35} />
                <span>Sonnenahalli Colony, medahalli
                Bangalore, Karnataka 560049</span>
              </div>
            </div>
          </div>

          {/* Contact Column */}
          <div className="space-y-4">
            <h3 className="font-semibold text-[36px] leading-[100%] font-poppins text-[#ffffff]">Contact</h3>
            <div className="border-4 border-[#0A5224] w-[195px] md:w-[208px]  rounded-4xl"></div>
            <ul className="space-y-5 ">
              <li>
                <Link href="/about" className="hover:text-gray-300">
                  About Us 
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-gray-300">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-gray-300">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="hover:text-gray-300">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-and-conditions" className="hover:text-gray-300">
                  Terms & Conditions
                </Link>
              </li>
            </ul>
          </div>

          {/* Products Column */}
          <div className="space-y-4">
            <h3 className="font-semibold text-[36px] leading-[100%] font-poppins text-[#ffffff]">Products</h3>
            <div className="border-4 border-[#0A5224] w-[195px]  md:w-[208px]  rounded-4xl"></div>
            <ul className="space-y-5">
              <li>
                <a href="https://talktopdf.apexiq.ai/" className="hover:text-gray-300">
                Talk to PDF
                </a>
              </li>
              <li>
                <a href="https://www.apexiq.ai/" className="hover:text-gray-300">
                Apex.iq
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-gray-300">
                Bolgs
                </a>
              </li>
             
            </ul>
          </div>
        </div>

        {/* Social Media Icons */}
        <div className="max-w-7xl mx-auto mt-8 pt-8">
          <div className="flex gap-4">
            <Image className="w-[43px] h-[43px]" src="/assets/images/facebook3.png" alt="Facebook" width={43} height={43} />
            <Image className="w-[43px] h-[43px]" src="/assets/images/linkedin1.png" alt="LinkedIn" width={43} height={43} />
            <Image className="w-[43px] h-[43px]" src="/assets/images/twitter1.png" alt="Twitter" width={43} height={43} />
            <Image className="w-[43px] h-[43px]" src="/assets/images/github2.png" alt="GitHub" width={43} height={43} />
            <Image className="w-[43px] h-[43px]" src="/assets/images/instagram1.png" alt="Instagram" width={43} height={43} />
          </div>
        </div>
        <div className="absolute lg:right-[82px] right-[30px] bottom-[100px] lg:bottom-[40px]">
        <Image className="w-[80px] lg:w-[104px]" src="/assets/images/footerlogo.png" alt="Phone Icon" width={104} height={104} />
        </div>
      </footer>

    
    </>
  );
};

export default FotterCon;
