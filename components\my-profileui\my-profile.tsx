"use client";

import { useState, ChangeEvent } from "react";
import Image from "next/image";
import { FaEdit } from "react-icons/fa";
import { RiDeleteBinLine } from "react-icons/ri";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import Link from "next/link";
import { FaAngleLeft } from "react-icons/fa";

interface FormData {
  firstName: string;
  lastName?: string;
  email: string;
  phone: string;
}

export default function MyProfileForm() {
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "<EMAIL>", // fixed email
    phone: "",
  });

  const [profileImage, setProfileImage] = useState<string>("/assets/images/user.png");

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setProfileImage(imageUrl);
    }
  };

  const isFormValid =
    formData.firstName.trim() !== "" &&
    formData.email.trim() !== "" &&
    formData.phone.trim() !== "";

  return (
    <div className="flex flex-col py-10 px-4 md:px-[50px] bg-white font-poppins">
      <div>
        <Link href="/" className="text-black hover:text-gray-800 text-[18px] font-normal flex gap-2 items-center px-4 py-12">
          <FaAngleLeft /> Back to Home Page
        </Link>
      </div>

      <div className="w-full max-w-[907px] bg-white md:py-[50px] mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center md:items-center">
          <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8">
            <div className="relative w-24 h-24 md:w-16 md:h-16">
              <Image
                src={profileImage}
                alt="Profile"
                width={96}
                height={96}
                className="w-24 h-24 md:w-16 md:h-16 rounded-full"
              />
              <label
                htmlFor="file-upload"
                className="absolute bottom-0 right-0 bg-[#D9D9D9] text-[#4B207A] rounded-full p-[7px] cursor-pointer"
              >
                <FaEdit className="text-[14px]" />
              </label>
              <input
                id="file-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
              />
            </div>
            <h2 className="text-[28px] md:text-[32px] font-medium text-black">Dineshka K</h2>
          </div>

          <button className="hidden md:flex text-[#F10404] hover:text-red-800 items-center text-[18px] md:text-[22px] font-medium cursor-pointer">
            <RiDeleteBinLine className="mr-2" />
            Delete this account
          </button>
        </div>

        <div className="mt-10 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-[20px] md:gap-[40px]">
            <div>
              <label className="block text-[20px] text-[#4B207A] font-medium">First Name</label>
              <input
                type="text"
                name="firstName"
                className="w-full p-2 border border-[#C3A4EC] text-[#404040] rounded-md focus:outline-none text-[15px] md:text-[22px]"
                value={formData.firstName}
                onChange={handleChange}
              />
            </div>

            <div>
              <label className="block text-[20px] text-[#4B207A] font-medium">
                Last Name (Optional)
              </label>
              <input
                type="text"
                name="lastName"
                className="w-full p-2 border border-[#C3A4EC] text-[#404040] rounded-md focus:outline-none text-[15px] md:text-[22px]"
                value={formData.lastName ?? ""}
                onChange={handleChange}
              />
            </div>
            <div className="relative">
              <label className="block text-[20px] text-[#4B207A] font-medium">
                Email Address
                <span className="absolute right-0 text-[#063585] text-[16px] font-medium">Fixed</span>
              </label>
              <input
                type="email"
                name="email"
                readOnly
                className="w-full p-2 border border-[#B28DE9] text-[#404040] rounded-md bg-gray-100  cursor-not-allowed text-[15px] md:text-[22px] fond-medium"
                value={formData.email}
              />
            </div>


            <div className="relative">
              <label className="block text-[20px] text-[#4B207A] font-medium mb-1">Phone Number</label>
              <div className="flex items-center border border-[#C3A4EC] rounded-md overflow-hidden p-2 bg-transparent">
                <PhoneInput
                  defaultCountry="IN"
                  international
                  value={formData.phone}
                  onChange={(phone) =>
                    setFormData((prev) => ({
                      ...prev,
                      phone: phone || "",
                    }))
                  }
                  className="w-full bg-transparent outline-none border-none focus:ring-0 focus:border-none text-[15px] text-[#404040] md:text-[22px]"
                />
              </div>
              {!isValidPhoneNumber(formData.phone || "") && formData.phone.length > 0 && (
                <span className="absolute top-0 right-0 text-red-500 text-[14px] font-semibold">
                  Invalid !
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 flex flex-col md:flex-row justify-between items-center gap-4 md:gap-0">
        <button
              className="px-5 md:px-10 py-2 bg-[#4B207A] text-white font-medium text-[12px] md:text-[22px] rounded-md hover:bg-[#3a1862]"
            >
              Change Password
            </button>
            <button
              className={`px-5 md:px-10 py-2 text-white font-medium text-[12px] md:text-[22px] rounded-md ${
                isFormValid ? "bg-[#0A5224] hover:bg-green-700" : "bg-[#6DCA8F] cursor-not-allowed"
              }`}
              disabled={!isFormValid}
            >
             Update Profile
            </button>

       
      

          <button className="md:hidden text-[#F10404] hover:text-red-800 flex items-center text-[13px] font-medium">
            <RiDeleteBinLine className="mr-1" />
            Delete this account
          </button>
        </div>
      </div>
    </div>
  );
}
