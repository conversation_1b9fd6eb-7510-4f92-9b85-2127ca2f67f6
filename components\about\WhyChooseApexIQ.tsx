// components/WhyChooseApexIQ.tsx

import Image from 'next/image';
import React from 'react';
import LearningImage from '@/public/assets/images/pathshala-about.png';
import ArrowIcon from '@/public/assets/images/about-arrow.png'; // Adjust path as needed

const WhyChooseApexIQ: React.FC = () => {
  return (
    <section className="w-full bg-white pt-0 pb-10">
      <div className="container">
        {/* Title */}
        <div className="text-center mb-10">
          <h2 className="text-[20px] md:text-[36px] font-poppins font-semibold text-black">
            Why Choose <span className="text-[#4E1A71]">Apex</span><span className="text-[#1A7131]">IQ</span>?
          </h2>
        </div>

        <div className="flex flex-col md:flex-row items-center gap-10">
          {/* Left side content */}
          <div className="md:w-1/2 px-6 space-y-10">
            <div className="flex items-start">
              <Image src={ArrowIcon} alt="Arrow Icon" className="w-[30px] h-[30px] mr-4 mt-1" />
              <div>
                <h3 className="text-[18px] md:text-[24px] font-poppins font-[500] text-black mb-[7px]">
                  Learn Anywhere, Anytime
                </h3>
                <p className="text-[16px] md:text-[18px] font-normal font-poppins text-black">
                  Study at your own pace with flexible learning modes.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <Image src={ArrowIcon} alt="Arrow Icon" className="w-[30px] h-[30px] mr-4 mt-1" />
              <div>
                <h3 className="text-[18px] md:text-[24px] font-poppins font-[500] text-black mb-[7px]">
                  Certification & Career Growth
                </h3>
                <p className="text-[16px] md:text-[18px] font-normal font-poppins text-black">
                  Earn recognized credentials for better opportunities.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <Image src={ArrowIcon} alt="Arrow Icon" className="w-[30px] h-[30px] mr-4 mt-1" />
              <div>
                <h3 className="text-[18px] md:text-[24px] font-poppins font-[500] text-black mb-[7px]">
                  Cutting-Edge Technology
                </h3>
                <p className="text-[16px] md:text-[18px] font-normal font-poppins text-black">
                  Leverage AI, ML, and modern tools for advanced learning, keeping you ahead in the industry.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <Image src={ArrowIcon} alt="Arrow Icon" className="w-[30px] h-[30px] mr-4 mt-1" />
              <div>
                <h3 className="text-[18px] md:text-[24px] font-poppins font-[500] text-black mb-[7px]">
                  Networking Opportunities
                </h3>
                <p className="text-[16px] md:text-[18px] font-normal font-poppins text-black">
                  Connect with like-minded learners, mentors, and professionals to grow your career.
                </p>
              </div>
            </div>
          </div>

          {/* Right side image */}
          <div className="md:w-1/2 hidden sm:flex justify-center">
            <div className="w-[419px] h-[526px] relative rounded-lg shadow-lg overflow-hidden">
              <Image
                src={LearningImage}
                alt="ApexIQ Learning"
                layout="fill"
                objectFit="cover"
                className="rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-[1065px] mx-auto text-center ">
    <hr className="mt-8 sm:mt-20 border-black border-b-1 " />
    </div>
    </section>
  );
};

export default WhyChooseApexIQ;
