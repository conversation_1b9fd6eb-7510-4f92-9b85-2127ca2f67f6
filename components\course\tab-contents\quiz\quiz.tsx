import { useState } from "react"
import QuizHeader from "@/components/course/tab-contents/quiz/QuizHeader"
import QuizQuestion from "@/components/course/tab-contents/quiz/QuizQuestion"
import QuizReview from "@/components/course/tab-contents/quiz/QuizReview"
import QuizResultModal from "@/components/course/tab-contents/quiz/QuizResultModal"
import { quizData } from "@/utils/constant"

export default function Quiz({ onNext }: { onNext?: () => void }) {
  const [step, setStep] = useState(0)
  const [userAnswers, setUserAnswers] = useState<Record<number, number>>({})
  const [score, setScore] = useState(0)
  const [attemptNumber, setAttemptNumber] = useState(1)
  const [showModal, setShowModal] = useState(false)
  const [isPerfectScore, setIsPerfectScore] = useState(false)

  const handleSubmit = () => {
    let correct = 0
    quizData.forEach((q) => {
      if (userAnswers[q.id] === q.correctAnswer) correct++
    })
    setScore(correct)
    setIsPerfectScore(correct === quizData.length)
    setShowModal(true)
  }

  const handleAnswerSelect = (id: number, opt: number) =>
    setUserAnswers((prev) => ({ ...prev, [id]: opt }))

  const handleModalAction = () => {
    setShowModal(false)

    if (isPerfectScore && attemptNumber < 3) {
      setStep(1)
    } else if (!isPerfectScore && attemptNumber < 3) {
      setStep(0)
      setUserAnswers({})
      setScore(0)
      setAttemptNumber((prev) => prev + 1)
    } else {
      setStep(1)
    }
  }

  return (
    <div className="bg-white rounded-lg p-2 sm:p-4 md:p-0 w-full ">
      <div className="w-full">
        {step === 0 && <QuizHeader attemptNumber={attemptNumber} />}
        {step === 0 ? (
          <>
            <div className="space-y-4 md:space-y-6">
              {quizData.map((q) => (
                <QuizQuestion
                  key={q.id}
                  question={q}
                  selected={userAnswers[q.id]}
                  onSelect={handleAnswerSelect}
                />
              ))}
            </div>
            <div className="mt-4 sm:mt-6 flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={quizData.some((q) => userAnswers[q.id] === undefined)}
                className="px-4 sm:px-6 md:px-9 py-2 md:py-3 bg-[#8b4fcc] text-white rounded-md font-semibold text-sm sm:text-base  cursor-pointer"
              >
                Submit
              </button>
            </div>
            <QuizResultModal
              show={showModal}
              score={score}
              total={quizData.length}
              perfect={isPerfectScore}
              attempt={attemptNumber}
              onAction={handleModalAction}
            />
          </>
        ) : (
          <QuizReview
            quizData={quizData}
            userAnswers={userAnswers}
            score={score}
            onNext={onNext} // ✅ Pass the prop here
          />
        )}
      </div>
    </div>
  )
}
