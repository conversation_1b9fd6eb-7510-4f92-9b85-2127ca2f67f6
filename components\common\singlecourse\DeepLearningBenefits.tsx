import { benefits } from "@/utils/constant";
// import Image from "next/image";
import { GiCheckMark } from "react-icons/gi";



const DeepLearningBenefits: React.FC = () => {


  return (
    <section className="pt-[50px]">
        <div className="container">
        <div className="">
  
      
      <div className="bg-white   flex flex-col md:flex-row items-center  ">
        {/* Left Section - Image */}
        {/* <div className="flex w-full md:w-1/3 mb-3 md:mb-0 justify-center">
          <Image
            src="/image2.png"
            alt="Deep Learning Course"
            width={340}
            height={400}
            className="rounded-lg shadow-lg"
          />
        </div> */}

      {/* Right Section - Benefits */}
<div className="flex-1 flex flex-col items-center justify-center text-start relative">
  <h2 className="text-[20px] md:text-[40px] font-semibold font-poppins mb-5">
  Ethical Hacking & Cybersecurity
  </h2>
  <p className="text-black mt-2 text-sm sm:text-[20px] font-[500] font-sans max-w-5xl ">
    Unlock a variety of benefits from our Neural Networks & Deep Learning course, with updated resources and materials. Become a skilled AI expert by mastering neural networks and deep learning techniques.
  </p>

  {/* Benefits List */}
  <div className="mt-6 relative space-y-4 lg:mr-20 lg:pr-10 w-full max-w-3xl">
    <div className="absolute left-[16px] top-2 bottom-2 w-[1px] bg-[#063585]"></div>
    {benefits.map((benefit, index) => (
      <div key={index} className="flex items-start space-x-3 relative">
        <GiCheckMark className="text-[#063585] text-4xl sm:text-3xl relative z-10 bg-white p-1 rounded-full" />
        <div>
          <h3 className="text-sm mt-2 sm:mt-0 sm:text-xl font-semibold text-gray-900">
            {benefit.title}
          </h3>
          <p className="text-gray-700 sm:pr-0 ml-4 text-md">{benefit.description}</p>
        </div>
      </div>
    ))}
  </div>
</div>

</div>
      {/* Bottom Border with Left & Right Space */}
      <div className="w-4/5 mx-auto border-b-2 border-black mt-10 md:mt-12"></div>
    </div>
        </div>
    </section>

  );
};

export default DeepLearningBenefits;
