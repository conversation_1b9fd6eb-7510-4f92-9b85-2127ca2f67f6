# Lesson API Integration Documentation

## Overview
This document describes the implementation of the lesson creation API integration with the admin dashboard UI.

## API Endpoint
- **URL**: `${NEXT_PUBLIC_BASE_URL}/api/modules/{moduleId}/lessons/`
- **Method**: POST
- **Content-Type**: multipart/form-data (for file uploads)

## Environment Variables
The base URL is configured in `.env`:
```
NEXT_PUBLIC_BASE_URL=https://ed.apexiq.ai
```

## API Service Structure

### Location
- **Service File**: `api-services/lessons/lessons.ts`
- **Index File**: `api-services/lessons/index.ts`
- **Component**: `components/admindashobard/AddLessionPage.tsx`

### Interfaces

#### CreateLessonPayload
```typescript
interface CreateLessonPayload {
  title: string;
  video_url: string;
  notes_md?: File[];
  quiz_files?: File[];
  project_description?: string;
  project_files?: File[];
}
```

#### CreateLessonResponse
```typescript
interface CreateLessonResponse {
  message: string;
  data: Lesson;
}
```

## Implementation Details

### 1. API Service (`api-services/lessons/lessons.ts`)

The service provides:
- `useCreateLesson()` - React Query hook for creating lessons
- `useGetLessons()` - React Query hook for fetching lessons
- `useUpdateLesson()` - React Query hook for updating lessons
- `useDeleteLesson()` - React Query hook for deleting lessons

### 2. File Upload Handling

The API handles multiple file types:
- **Notes**: `.md` files (markdown format)
- **Quiz**: `.json` files (quiz data)
- **Project Files**: `.pdf`, `.docx`, `.md` files

Files are sent as FormData with indexed field names:
- `notes_md_0`, `notes_md_1`, etc.
- `quiz_files_0`, `quiz_files_1`, etc.
- `project_files_0`, `project_files_1`, etc.

### 3. Component Integration (`AddLessionPage.tsx`)

#### Key Features:
- **Form Validation**: Validates required fields (title, video URL, notes)
- **File Management**: Handles multiple file uploads with preview
- **Loading States**: Shows loading indicator during submission
- **Error Handling**: Displays toast notifications for errors
- **Success Feedback**: Shows success message and navigates back

#### State Management:
```typescript
const [lessonTitle, setLessonTitle] = useState("");
const [videoLinks, setVideoLinks] = useState<string[]>([]);
const [notesFile, setNotesFile] = useState<UploadFile[]>([]);
const [quizFiles, setQuizFiles] = useState<UploadFile[]>([]);
const [pdfFiles, setPdfFiles] = useState<UploadFile[]>([]);
const [markdownInput, setMarkdownInput] = useState("");
const [isSubmitting, setIsSubmitting] = useState(false);
```

## Module Title Integration

The AddLessionPage component now fetches and displays the actual module title instead of a hardcoded value.

### Implementation:
- Uses `useGetModule` hook to fetch module details
- Requires both `courseId` and `moduleId` URL parameters
- Shows loading state while fetching module data
- Displays actual module title or fallback text

### URL Structure:
```
/Admin/dashboard/addlession?courseId={courseId}&moduleId={moduleId}
```

## Usage Example

### Creating a Lesson

```typescript
const createLessonMutation = useCreateLesson();

const handleSaveLesson = async () => {
  try {
    const payload = {
      title: "Introduction to Python",
      video_url: "https://vimeo.com/123456789",
      notes_md: [notesFile], // File objects
      quiz_files: [quizFile], // File objects
      project_description: "Learn Python basics",
      project_files: [pdfFile], // File objects
    };

    await createLessonMutation.mutateAsync({
      moduleId: "module-uuid",
      payload,
    });

    toast.success('Lesson created successfully!');
  } catch (error) {
    toast.error('Failed to create lesson');
  }
};
```

### Navigation Updates

The ViewCoursePage component now includes "Add Lesson" buttons for each selected module:

```typescript
// Each module card has its own Add Lesson button
<button
  onClick={() => router.push(`/Admin/dashboard/addlession?courseId=${courseId}&moduleId=${module.id}`)}
  className="border border-[#0A5224] text-[#0A5224] px-3 py-1 text-sm font-medium hover:bg-[#0A5224] hover:text-white rounded"
>
  Add Lesson
</button>
```

## API Request Format

### FormData Structure
```
title: "Introduction to Python"
video_url: "https://vimeo.com/123456789"
project_description: "Learn Python basics"
notes_md_0: [File object]
notes_md_1: [File object]
quiz_files_0: [File object]
project_files_0: [File object]
```

## Error Handling

The implementation includes comprehensive error handling:

1. **Client-side Validation**:
   - Required field validation
   - File type validation
   - File size limits

2. **API Error Handling**:
   - Network errors
   - Server validation errors
   - File upload errors

3. **User Feedback**:
   - Toast notifications for success/error
   - Loading states
   - Form validation messages

## File Upload Constraints

- **Notes Files**: `.md` format, 20MB per file, up to 10 files, 100MB total
- **Quiz Files**: `.json` format, 20MB per file, up to 20 files, 100MB total
- **Project Files**: `.pdf`, `.docx`, `.md` format, 20MB per file, up to 10 files, 100MB total

## Dependencies

- `@tanstack/react-query` - For API state management
- `react-hot-toast` - For notifications
- `next/navigation` - For routing

## Testing

To test the lesson creation:

1. Navigate to the Add Lesson page
2. Fill in the lesson title
3. Add a Vimeo video URL
4. Upload notes files (.md)
5. Optionally upload quiz files (.json) and project files
6. Click "Save Lesson"

The system will validate inputs, upload files, and create the lesson via the API.

## Future Enhancements

- Support for multiple video URLs
- Drag and drop file upload
- File preview functionality
- Progress indicators for large file uploads
- Batch lesson creation
