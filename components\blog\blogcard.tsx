"use client";
import React, { useState } from "react";
import { <PERSON>, <PERSON>UpR<PERSON>, ArrowDown } from "lucide-react";
import { useRouter } from "next/navigation";

export const cardsData = [
  {
    "id": 1,
    "title": "AI in Education",
    "category": "AI",
    "description": "Explore how artificial intelligence is revolutionizing classroom learning and empowering educators.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "<PERSON>. <PERSON>",
    "date": "Jan 25, 2025",
    "likes": 25
  },
  {
    "id": 2,
    "title": "Curriculum for the Future",
    "category": "Education",
    "description": "Discover the essential skills and subjects that will shape the next generation’s curriculum.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "<PERSON>",
    "date": "Feb 3, 2025",
    "likes": 18
  },
  {
    "id": 3,
    "title": "AI-Powered Classrooms",
    "category": "AI",
    "description": "Delve into how AI tools are transforming the dynamics between students and teachers.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "<PERSON>",
    "date": "Feb 7, 2025",
    "likes": 22
  },
  {
    "id": 4,
    "title": "Inclusive Learning",
    "category": "Education",
    "description": "Understand the impact of technology on accessibility and inclusivity in modern education.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "Ava Patel",
    "date": "Feb 10, 2025",
    "likes": 19
  },
  {
    "id": 5,
    "title": "The Ethics of AI in Schools",
    "category": "AI",
    "description": "Examine the ethical concerns of using AI to track student progress and behavior.track student progress and behavior.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "Noah Kim",
    "date": "Feb 14, 2025",
    "likes": 30
  },
  {
    "id": 6,
    "title": "Virtual Classrooms",
    "category": "Education",
    "description": "Learn how remote learning has reshaped education across different age groups of learn ",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "Emma Zhang",
    "date": "Feb 18, 2025",
    "likes": 17
  },
  {
    "id": 7,
    "title": "Smart Learning Assistants",
    "category": "AI",
    "description": "Find out how AI-driven bots are providing personalized tutoring for students AI-driven bots are providing",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "James O'Neil",
    "date": "Feb 20, 2025",
    "likes": 21
  },
  {
    "id": 8,
    "title": "Gamification in Education",
    "category": "Education",
    "description": "Explore how game-based learning improves student motivation and engagement.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "Isabella Ray",
    "date": "Feb 23, 2025",
    "likes": 24
  },
  {
    "id": 9,
    "title": "Predictive Analytics",
    "category": "AI",
    "description": "See how data and machine learning are predicting student outcomes and behaviors.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "William Park",
    "date": "Feb 27, 2025",
    "likes": 28
  },
  {
    "id": 10,
    "title": "Teacher Training of Tomorrow",
    "category": "Education",
    "description": "Explore modern strategies to upskill teachers for the digital and AI age.",
    "image": "/assets/about/ai.jpg",
    "profileImage": "/assets/about/profile.jpg",
    "author": "Mia Thomas",
    "date": "Mar 2, 2025",
    "likes": 16
  }
]


const CARDS_PER_PAGE = 9;

interface Card {
	id: number;
	title: string;
	category: string;
	description: string;
	image: string;
	profileImage: string;
	author: string;
	date: string;
	likes: number;
}

interface BlogcardProps {
	cardsData: Card[];
}

const Blogcard: React.FC<BlogcardProps> = ({ cardsData }) => {
	const [visibleCount, setVisibleCount] = useState(CARDS_PER_PAGE);
	const router = useRouter();

	const handleLoadMore = () => {
		setVisibleCount((prev) => prev + CARDS_PER_PAGE);
	};

	const handleCardClick = (cardId: number) => {
		router.push(`/blogs/blogDeatils?id=${cardId}`);
	};

	return (
		<div className="lg:px-6 py-24 px-1 md:px-4 bg-white">
			<section className=" container ">
				<div className=" grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
					{cardsData.slice(0, visibleCount).map((card) => (
						<div
							key={card.id}
							onClick={() => handleCardClick(card.id)}
							className="bg-white overflow-hidden transition shadow-[0px_4px_6px_-2px_#10182808,0px_12px_16px_-4px_#10182814] hover:shadow-xl cursor-pointer"
						>
							<div className="p-6">
								<img
									src={card.image}
									alt={card.title}
									className="w-full h-[240px] object-cover "
								/>
							</div>

							<div className="p-6 pt-0 ">
								<p className="font-poppins font-semibold text-[14px] leading-[20px] text-[#9F6CEC] py-1">
									{card.category}
								</p>
								<div className="flex items-center justify-between">
									<h3 className="text-[24px] font-Poppins leading-[24px] font-bold text-black py-1">
										{card.title}
									</h3>
									<ArrowUpRight
										className="w-6 h-6 text-gray-700 font-bold hover:text-[#9F6CEC] transition-colors"
										onClick={(e) => {
											e.stopPropagation();
											handleCardClick(card.id);
										}}
									/>
								</div>
								<p className="font-poppins font-normal text-[16px] leading-[24px] text-gray-500 pt-2">
									{card.description}
								</p>
								<div className="flex items-center justify-between pt-10">
									<div className="flex items-center space-x-2">
										<img
											src={card.profileImage}
											alt={card.author}
											className="w-10 h-10 rounded-full "
										/>
										<div className="flex flex-col">
											<span className="text-sm font-poppins font-semibold text-gray-900">
												{card.author}
											</span>
											<span className="text-xs text-gray-400">
												{card.date}
											</span>
										</div>
									</div>

									<div className="flex items-center space-x-1 text-md text-gray-600">
										<Heart className="w-6 h-6 text-red-500" />
										<span>{card.likes}</span>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{visibleCount < cardsData.length && (
					<div className="flex justify-center mt-8">
						<button
							onClick={handleLoadMore}
							className="flex items-center gap-2 px-6 py-2 bg-[#3D1975] text-white rounded-lg font-medium shadow hover:bg-[#2a1152] transition"
						>
							<ArrowDown className="w-5 h-5" />
							Load more
						</button>
					</div>
				)}
			</section>
		</div>
	);
};

export default Blogcard;
