"use client"; // Add this at the top if you are using Next.js 13+ App Router

import type React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation"; // Importing hook

export default function MainMenu() {
  return (
    <div className="flex-1 justify-center items-center">
      <div className="mt-[30px] mb-[20px]">
        <h3 className="font-poppins font-medium text-[20px] text-black">
          MAIN MENU
        </h3>
      </div>
      <nav className="space-y-3">
        <MenuItem imageSrc="/assets/images/dashboard1.png" label="My Dashboard" href="/dashboard" />
        <MenuItem imageSrc="/assets/images/learning1.png" label="My Courses" href="/dashboard/my-course" />
        <MenuItem imageSrc="/assets/images/premium-badge1.png" label="Performance" href="/dashboard/performance" />
        <MenuItem imageSrc="/assets/images/certificate2.png" label="Certificates" href="/dashboard/certificate" badge={1} />
        <MenuItem imageSrc="/assets/images/Maskgroup(5).png" label="Referred" href="/dashboard/referred" />
        <MenuItem imageSrc="/assets/images/Maskgroup(6).png" label="Notification" href="/dashboard/notification" badge={3} />
      </nav>
    </div>
  );
}

interface MenuItemProps {
  icon?: React.ReactNode;
  imageSrc?: string;
  label: string;
  href: string;
  badge?: number;
}

function MenuItem({ icon, imageSrc, label, href, badge }: MenuItemProps) {
  const pathname = usePathname(); // Get the current route
  const isActive = pathname === href; // Check if current path matches link

  const baseClasses =
    "flex items-center gap-2 px-4 pl-[40px] py-2 rounded-md text-sm font-poppins text-[20px] leading-[100%] tracking-[-0.01em]";
  const activeClasses = "border border-black font-semibold bg-white text-black lg:py-3"; // Active styles
  const hoverClasses = "hover:border hover:border-black hover:font-semibold hover:bg-white lg:py-3";

  return (
    <Link
      href={href}
      className={`${baseClasses} ${isActive ? activeClasses : "text-black"} ${hoverClasses}`}
    >
      <span className="text-gray-500 flex items-center justify-between">
        {imageSrc ? (
          <Image src={imageSrc} alt={label} className="w-[30px] h-[30px] justify-center" width={30} height={30} />
        ) : (
          icon
        )}
      </span>
      <span className="px-6">{label}</span>
      {badge && (
        <span className="bg-red-500 text-white text-sm rounded-full w-6 h-6 flex items-center justify-center">
          {badge}
        </span>
      )}
    </Link>
  );
}
