"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import InputField from "@/components/common/adminCommon/InputField";
import { useAdminLogin } from "../../api-services/adminlogin/AdminLogin";

const LoginForm: React.FC = () => {
  const router = useRouter();

  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  const { mutate: loginAdmin, isPending, error } = useAdminLogin();

  // Regex Patterns
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const phoneRegex = /^\d{10}$/;
  const usernameRegex = /^[a-zA-Z0-9]{4,}$/;
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;

  // Validate form
  useEffect(() => {
    const { username, password } = formData;

    const isUsernameValid =
      emailRegex.test(username) ||
      phoneRegex.test(username) ||
      usernameRegex.test(username);

    const isPasswordValid = strongPasswordRegex.test(password);

    setIsFormValid(isUsernameValid && isPasswordValid);
  }, [formData]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Submit handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginAdmin(formData, {
      onSuccess: (data) => {
        console.log("Admin login successful: rediecting to admin dashboard", data?.message || "Login successful");
        router.push("/Admin/dashboard");
      },
    });
  };

  return (
    <section className="py-[60px] px-4">
      <div className="flex flex-col items-center justify-center bg-white">
        <div className="bg-[#eeeeee] rounded-md shadow-md pt-9 py-10 lg:w-[914px]">
          {/* Logo */}
          <div className="flex justify-center">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={130}
              height={60}
              className="w-[120px] md:w-[181px]"
            />
          </div>

          {/* Description */}
          <div className="p-[30px] lg:px-[60px] pt-[10px]">
            <p className="text-center font-normal text-[18px] md:text-[21px] leading-[30px] font-[Poppins] text-black mb-1">
              This login is exclusively for the{" "}
              <span className="text-[#4b207a]">ADMIN</span> of the ApexIQ Family
              members, granting them control and management access.
            </p>
          </div>

          {/* Form */}
          <div className="p-[20px]">
            <form
              onSubmit={handleSubmit}
              className="space-y-4 w-full flex flex-col items-center max-w-[668px] mx-auto"
            >
              {/* Username */}
              <div className="w-full">
                <InputField
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleChange}
                  label="Email / Phone / Username"
                />
              </div>

              {/* Password */}
              <div className="w-full relative mb-0">
                <InputField
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  label="Password"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-[#4b207a]"
                  onClick={() => setShowPassword((prev) => !prev)}
                >
                  {showPassword ? <EyeOff size={22} /> : <Eye size={22} />}
                </button>

                {/* Password error message */}
                {!strongPasswordRegex.test(formData.password) &&
                  formData.password && (
                    <p className="text-red-600 text-sm mt-1">
                      Must be 8+ characters, include uppercase, lowercase,
                      number, and special character.
                    </p>
                  )}
              </div>

              {/* Forgot password link */}
              <div className="w-full flex justify-center md:justify-end">
                <Link href="/Admin/forgotpassword">
                  <span className="text-[#ff0000] text-sm font-poppins cursor-pointer">
                    Forgot Password?
                  </span>
                </Link>
              </div>

              {/* Login error */}
              {error && (
                <p className="text-red-600 text-sm text-center">
                  Login failed. Please check your credentials.
                </p>
              )}

              {/* Submit button */}
              <div className="w-full flex items-center justify-center relative py-2 pt-10">
                <button
                  type="submit"
                  disabled={!isFormValid || isPending}
                  className={`py-3 px-10 ${
                    isFormValid && !isPending
                      ? "bg-[#4b207a]"
                      : "bg-[#4b207a] opacity-50 cursor-not-allowed"
                  } text-white text-[24px] font-bold rounded-lg`}
                >
                  {isPending ? "Logging in..." : "Login"}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LoginForm;
