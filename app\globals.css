@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Karla:ital,wght@0,200..800;1,200..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poetsen+One&display=swap');
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-poppins: 'Poppins', sans-serif;
  --font-sans: 'Work Sans', sans-serif;
}


@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Remove this media query
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Poppins", sans-serif;
}
.bigcontainer{
  max-width: 1300px;
    margin: 0 auto;
    padding: 0 10px; 
}
.container {
    max-width: 1250px;
    margin: 0 auto;
    padding: 0 10px; 
  }
  .small-container {
    max-width: 1050px;
    margin: 0 auto; 
    padding: 0 10px; 
  }

  .slick-dots li button:before {
    color: #7c3aed !important;
    font-size: 12px !important;
    opacity: 0.5 !important;
  }
  .slick-dots li.slick-active button:before {
    color: #7c3aed !important;
    opacity: 1 !important;
  }
/* Custom Swiper Pagination */
.custom-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: #ddd;
  opacity: 1;
  margin: 0 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.custom-pagination .swiper-pagination-bullet-active {
  background-color: #9062f9;
  transform: scale(1.3);
}
.font-poppins{
  font-family: "Poppins", sans-serif;
}
.font-sans{
  font-family: 'Work Sans', sans-serif;
}
.font-karla{font-family: "Karla", sans-serif;}

.font-poetsen{ 
  font-family: 'Poetsen One', sans-serif;
}

.PhoneInputInput {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
}


/* Autofill background override */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px #eeeeee inset !important;
  -webkit-text-fill-color: #000 !important;
  transition: background-color 5000s ease-in-out 0s;
}
.react-tel-input .flag-dropdown {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  border-right: 1px solid black !important;
  border-radius: 0 !important;
}
.react-tel-input .form-control {
 padding: 29px 60px !important;
}
.react-tel-input .selected-flag {width: 50px !important;}

/* popup animation */
@keyframes borderGrow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

.animate-borderGrow {
  transform-origin: left;
  animation: borderGrow 1s ease-out forwards;
}
