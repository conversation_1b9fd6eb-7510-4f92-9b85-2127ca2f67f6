"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { FaTimes } from "react-icons/fa";
import { useGetLesson, useUpdateLesson } from "../../api-services/lessons";
import { useQueryClient } from "@tanstack/react-query";
import QuizUpload from "./QuizUpload";
import toast from "react-hot-toast";

type UploadFile = {
  name: string;
  size: string;
  file?: File;
};

const EditLessonPage = () => {
  const [lessonTitle, setLessonTitle] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [videoLinks, setVideoLinks] = useState<string[]>([]);
  const [videoLinkInput, setVideoLinkInput] = useState("");
  const [notesFile, setNotesFile] = useState<UploadFile[]>([]);
  const [quizFiles, setQuizFiles] = useState<UploadFile[]>([]);
  const [pdfFiles, setPdfFiles] = useState<UploadFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [markdownInput, setMarkdownInput] = useState("");

  const notesInputRef = useRef<HTMLInputElement | null>(null);
  const quizInputRef = useRef<HTMLInputElement | null>(null);
  const pdfInputRef = useRef<HTMLInputElement | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Get lessonId and courseId from URL params
  const lessonId = searchParams.get('lessonId') || '';
  const courseId = searchParams.get('courseId') || '';
  const moduleId = searchParams.get('moduleId') || '';

  // API hooks
  const { data: lessonResponse, isLoading: lessonLoading, error: lessonError } = useGetLesson(lessonId);
  const updateLessonMutation = useUpdateLesson();

  // Load lesson data when component mounts
  useEffect(() => {
    if (lessonResponse?.data) {
      const lesson = lessonResponse.data;
      setLessonTitle(lesson.title || "");
      setVideoLinks(lesson.video_url ? [lesson.video_url] : []);
      setMarkdownInput(""); // Will be loaded from notes_md if available
    }
  }, [lessonResponse]);

  const handleBack = () => router.back();

  const handleMultipleFileSelect = (
    e: React.ChangeEvent<HTMLInputElement>,
    setter: React.Dispatch<React.SetStateAction<UploadFile[]>>
  ) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;
    const validFiles: UploadFile[] = files.map((file) => ({
      name: file.name,
      size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
      file: file,
    }));
    setter((prev) => [...prev, ...validFiles]);
  };

  const handleAddVideoLink = () => {
    if (videoLinkInput.trim()) {
      setVideoLinks([videoLinkInput.trim()]);
      setVideoLinkInput("");
    }
  };

  const handleUpdateLesson = async () => {
    if (!lessonTitle.trim()) {
      toast.error('Lesson title is required');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        title: lessonTitle.trim(),
        video_url: videoLinks[0] || undefined,
        notes_md: notesFile.map(file => file.file).filter(Boolean) as File[],
        quiz_files: quizFiles.map(file => file.file).filter(Boolean) as File[],
        project_description: markdownInput.trim() || undefined,
        project_files: pdfFiles.map(file => file.file).filter(Boolean) as File[],
      };

      await updateLessonMutation.mutateAsync({
        lessonId,
        payload,
      });

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["lesson", lessonId] });
      queryClient.invalidateQueries({ queryKey: ["course-detail", courseId] });

      toast.success('Lesson updated successfully!');
      setTimeout(() => {
        router.back();
      }, 1000);

    } catch (error) {
      console.error('Error updating lesson:', error);
      toast.error('Failed to update lesson. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (lessonLoading) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (lessonError) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Lesson</h1>
          <p className="text-gray-600 mb-4">Failed to load lesson details.</p>
          <button
            onClick={handleBack}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white p-[10px] lg:p-8 font-poppins text-[#000] relative">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
        >
          <Image
            src="/assets/images/back-arrow.png"
            alt="Back"
            width={15}
            height={15}
          />
          Back
        </button>
      </div>

      <div className="max-w-[800px] w-full mx-auto">
        <p className="lg:text-[18px] text-[14px] font-medium mb-8">
          Edit Lesson: {lessonResponse?.data?.title || "Loading..."}
        </p>

        {/* Lesson Title */}
        <div className="mb-8 lg:flex block items-center gap-4">
          <label className="text-[18px] mb-[10px] lg:mb-0 block font-medium lg:whitespace-nowrap">
            Lesson Title :
          </label>
          <input
            type="text"
            value={lessonTitle}
            onChange={(e) => setLessonTitle(e.target.value)}
            placeholder="Enter lesson title here"
            className="flex-1 border border-black rounded-md px-4 lg:py-4 py-2 text-[14px] lg:text-[16px] outline-none focus:border-black placeholder-black w-full"
          />
        </div>

        {/* Upload Video */}
        <div className="mb-8">
          <label className="block text-base font-medium mb-3">
            Upload Video :
          </label>

          {/* Video URL Input */}
          <div className="mb-3">
            <div className="flex items-center border border-[#3D3D3D] rounded-md px-4 lg:py-4 py-2">
              <Image
                src="/assets/images/videoupload.png"
                alt="attachment icon"
                width={30}
                height={30}
                className="mr-3"
              />
              <input
                type="text"
                value={videoLinkInput}
                onChange={(e) => setVideoLinkInput(e.target.value)}
                placeholder="Upload Video URL here"
                className="flex-1 outline-none lg:text-[16px] text-[14px] text-[#3D3D3D] placeholder-black"
              />
            </div>
          </div>

          {/* Current Video */}
          {videoLinks.length > 0 && (
            <div className="mb-3 space-y-2">
              {videoLinks.map((link, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-gray-600 font-medium">{i + 1}.</span>
                    <span className="text-[#666666] text-[14px]">
                      <span className="underline text-[#0000EE] mr-2">Current Video</span>
                      (Video URL)
                    </span>
                  </div>
                  <button
                    onClick={() => setVideoLinks([])}
                    className="text-red-500 hover:text-red-700"
                  >
                    <FaTimes />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Upload Button */}
          <div className="flex justify-end">
            <button
              onClick={handleAddVideoLink}
              className="px-4 py-2 border border-[#063585] text-[#063585] rounded text-sm font-medium hover:bg-blue-50"
            >
              Update Video
            </button>
          </div>
        </div>

        {/* Upload Notes */}
        <div className="mb-8">
          <label className="block text-base font-medium mb-3">
            Upload Notes :
          </label>

          {/* Upload Area */}
          <div className="mb-3">
            <div
              onClick={() => notesInputRef.current?.click()}
              className="border border-gray-400 rounded-md p-8 cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex flex-col items-center justify-center text-center">
                <Image
                  src="/assets/images/notes.png"
                  alt="attachment icon"
                  width={40}
                  height={40}
                  className="w-[40px] h-[40px] mb-[10px]"
                />
                <input
                  ref={notesInputRef}
                  type="file"
                  accept=".md"
                  multiple
                  className="hidden"
                  onChange={(e) => handleMultipleFileSelect(e, setNotesFile)}
                />
                <p className="text-sm text-[#0065FF] underline mb-1">
                  Choose file or Drag here
                </p>
                <p className="text-xs text-gray-500">
                  Supported file type(s) : .md
                </p>
                <p className="text-xs text-gray-500">
                  Size limit 20MB per file, up to 10 file(s) with total file size not exceeding 100MB
                </p>
              </div>
            </div>
          </div>

          {/* Files List */}
          {notesFile.length > 0 && (
            <div className="mb-3">
              {notesFile.map((file, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3 w-full mb-2"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-gray-600 font-medium">{i + 1}.</span>
                    <span className="text-[#0065FF] underline text-sm">
                      {file.name} ({file.size})
                    </span>
                  </div>
                  <button
                    onClick={() =>
                      setNotesFile(notesFile.filter((_, idx) => idx !== i))
                    }
                    className="text-red-500 hover:text-red-700 text-lg"
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Upload Quiz - New Component */}
        <QuizUpload
          lessonId={lessonId}
          courseId={courseId}
          onUploadComplete={() => {
            // Refresh lesson data after quiz upload
            queryClient.invalidateQueries({ queryKey: ["lesson", lessonId] });
          }}
        />

        {/* Save Button */}
        <div className="flex justify-center mt-8">
          <button
            onClick={handleUpdateLesson}
            disabled={isSubmitting}
            className="bg-[#063585] text-white px-8 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Updating...' : 'Update Lesson'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditLessonPage;
