"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Menu, X } from "lucide-react";
import ProfileDropdown from "@/components/sidebar-dropdown/dropdown";

interface HeaderProps {
  showSidebarMobile: boolean;
  setShowSidebarMobile: React.Dispatch<React.SetStateAction<boolean>>;
}

const AdminHeader: React.FC<HeaderProps> = ({
  showSidebarMobile,
  setShowSidebarMobile,
}) => {
  return (
    <header className="border-b border-[#868686] flex flex-row items-center justify-between bg-[#fff] fixed top-0 w-full z-[99] py-[5px] px-[10px] lg:px-[40px]">
      <div className="flex items-center gap-2">
        {/* Mobile Menu Toggle */}
        <div className="block md:hidden">
          <button onClick={() => setShowSidebarMobile(!showSidebarMobile)}>
            {showSidebarMobile ? (
              <X className="h-6 w-6 text-black" />
            ) : (
              <Menu className="h-6 w-6 text-black" />
            )}
          </button>
        </div>

        {/* Logo and Title */}
        <div className="flex items-center">
          <Link href="/">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="w-[110px] sm:w-[110px] lg:w-[130px] cursor-pointer"
            />
          </Link>
          <p className="font-bold text-[16px] leading-[100%] tracking-[-0.01em] font-[Poppins] text-[#4b207a] pt-6">
            Partasala
          </p>
        </div>
      </div>

      {/* Profile Dropdown */}

         <div className="">
                <Image
                  src="/assets/images/image(9).png"
                  alt="Profile"
                  width={65}
                  height={65}
                  className="object-cover w-[40px] h-[40px] md:w-[65px] md:h-[65px] rounded-full cursor-pointer"
                />
                
              </div>
    
    </header>
  );
};

export default AdminHeader;
