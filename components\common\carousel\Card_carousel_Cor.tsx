"use client";
import React, { useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./carousel.css"; // Custom styles

interface CardData {
  title: string;
  points: string[];
  bgImage: string;
}

const CardCarouselCor: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 4000,
    centerMode: true, // Ensures the active card is always centered
    centerPadding: "0px", // No extra padding around the centered card
    beforeChange: (current: number, next: number) => setActiveIndex(next),
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          centerMode: true,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          centerMode: false, // Disable center mode for small screens
        },
      },
    ],
  };

  const cardData: CardData[] = [
    {
      title: "Preparation for Real-World Success",
      points: [
        "Apexiq Partasala's training is aligned with current industry needs.",
        "Courses equip students with practical abilities that match employer expectations.",
        "This targeted training prepares students for successful careers.",
      ],
      bgImage: "/assets/images/goal.png",
    },
    {
      title: "Quality Education by Industry Experts",
      points: [
        "Courses are crafted by experienced professionals, combining theory with real-world skills.",
        "The curriculum emphasizes hands-on learning to build expertise.",
        "Students gain essential skills to meet industry demands.",
      ],
      bgImage: "/assets/images/industry.png",
    },
    {
      title: "Hands-On Learning Approach",
      points: [
        "Apexiq Partasala emphasizes interactive learning, engaging students with the material.",
        "Hands-on activities and real-world scenarios help students gain experience.",
        "This approach builds confidence, preparing students to excel.",
      ],
      bgImage: "/assets/images/handson.png",
    },
  ];

  return (
    <div className="px-4 pb-8 sm:pt-16 relative overflow-hidden">
      <Slider {...settings}>
        {cardData.map((card, index) => (
          <div key={index} className="p-4">
            <div
              className={`w-full min-h-[290px] md:min-h-[420px] mx-auto rounded-lg shadow-lg flex flex-col justify-between p-6 md:p-8 bg-cover bg-center transition-all duration-300 ${
                activeIndex === index
                  ? "scale-110 opacity-100 transform transition-transform ease-in-out"
                  : "scale-90 opacity-60"
              }`}
              style={{
                backgroundImage: `url(${card.bgImage})`,
                backgroundSize: "70%",
                backgroundPosition: "center",
                backgroundColor: "#D9D9D9",
                backgroundRepeat: "no-repeat",
              }}
            >
              <h3 className="text-base md:text-2xl font-semibold mb-4 text-center text-black">
                {card.title}
              </h3>
              <ul className="space-y-2 md:space-y-3 flex-grow">
                {card.points.map((point, idx) => (
                  <li key={idx} className="flex items-start gap-2 text-black">
                    <span className="text-xs font-bold">•</span>
                    <span className="text-xs md:text-lg font-normal">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default CardCarouselCor;
