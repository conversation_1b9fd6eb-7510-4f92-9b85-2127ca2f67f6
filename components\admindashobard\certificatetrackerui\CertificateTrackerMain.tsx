"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { FiSearch } from "react-icons/fi";
import Image from "next/image";
import CertificateFilter from "./CertificateFilter";

interface StudentRow {
  no: string;
  name: string;
  course: string;
  certificate: string;
  progress: string;
  upcoming: string;
}

const studentData: StudentRow[] = [
  { no: "01", name: "Aditi S.", course: "Python", certificate: "Earned", progress: "100%", upcoming: "None" },
  { no: "02", name: "<PERSON><PERSON><PERSON>", course: "MySQL", certificate: "In progress", progress: "100%", upcoming: "None" },
  { no: "03", name: "<PERSON><PERSON>", course: "Data Science", certificate: "Upcoming", progress: "50%", upcoming: "5 Quiz" },
  { no: "04", name: "<PERSON><PERSON>", course: "Python", certificate: "Earned", progress: "100%", upcoming: "None" },
  { no: "05", name: "<PERSON><PERSON><PERSON>", course: "Python", certificate: "Earned", progress: "100%", upcoming: "None" },
  { no: "06", name: "Je<PERSON>", course: "Java", certificate: "Upcoming", progress: "70%", upcoming: "3 Quiz" },
];

export default function CertificateTrackerMain() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  const filteredData = studentData.filter((row) =>
    row.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="p-4 sm:p-6 md:p-8 lg:p-10">
      {/* Back Button */}
      <div className="mb-4">
        <button
          onClick={handleBack}
          className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
        >
          <Image src="/assets/images/back-arrow.png" alt="Back" width={15} height={15} />
          Back
        </button>
      </div>

      {/* Header & Search */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-[30px]">
        <h1 className="text-[20px] font-semibold text-[#59207C] underline whitespace-nowrap">
          Certificate Tracker
        </h1>

        <div className="relative w-full md:w-[453px] rounded bg-[#fafafa] shadow-md">
          <FiSearch className="absolute left-3 top-3 text-gray-500" size={18} />
          <input
            type="text"
            placeholder="Search by students"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 text-sm rounded border border-gray-200 bg-transparent focus:outline-none"
          />
        </div>
      </div>

      {/* Student Table */}
      <div className="w-full overflow-x-auto shadow-md rounded-md">
        <table className="min-w-[800px] w-full text-center">
          <thead className="bg-[#ededed]">
            <tr>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">No</th>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">Name</th>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">Course name</th>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">Certificate state</th>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">Progress</th>
              <th className="px-4 py-3 text-sm sm:text-base font-semibold text-black">Upcoming</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#C3C3C3] bg-white">
            {paginatedData.length > 0 ? (
              paginatedData.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50 text-sm sm:text-base">
                  <td className="px-4 py-4">{row.no}</td>
                  <td className="px-4 py-4 font-medium text-black">{row.name}</td>
                  <td className="px-4 py-4">{row.course}</td>
                  <td className="px-4 py-4">{row.certificate}</td>
                  <td className="px-4 py-4">{row.progress}</td>
                  <td className="px-4 py-4">{row.upcoming}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-6 text-gray-500">
                  No students found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-6">
        <div className="text-sm text-gray-500 text-center md:text-left">
          Showing{" "}
          <span className="font-medium text-[#438eff] px-1">
            {filteredData.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1} -{" "}
            {Math.min(currentPage * itemsPerPage, filteredData.length)}
          </span>{" "}
          of{" "}
          <span className="font-medium text-[#438eff] px-1">
            {filteredData.length}
          </span>{" "}
          rows
        </div>

        <div className="flex flex-wrap justify-center md:justify-end items-center gap-2">
          <button
            className="text-[#438eff] text-sm px-3 py-1"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </button>
          <button
            className="text-[#646464] text-sm px-3 py-1"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span className="bg-[#438eff] text-white px-4 py-1 text-sm rounded">
            {currentPage}
          </span>
          <button
            className="text-[#438eff] border border-[#438eff] px-3 py-1 text-sm rounded"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Next
          </button>
          <button
            className="text-[#438eff] text-sm px-3 py-1"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Last
          </button>
        </div>
      </div>
      <CertificateFilter/>
    </div>
  );
}
