"use client";

import React, { useState } from "react";
import Image from "next/image";
import { X, <PERSON>, EyeOff } from "lucide-react";
import WelcomeContent from "@/components/signupui/welcome";


interface ResetPasswordData {
  password: string;
  confirmPassword: string;
}

const calculatePasswordStrength = (password: string): {
  strength: number;
  text: string;
  color: string;
} => {
  let strength = 0;
  const checks = {
    length: password.length >= 8,
    hasNumber: /\d/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasUpperCase: /[A-Z]/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  strength += checks.length ? 20 : 0;
  strength += checks.hasNumber ? 20 : 0;
  strength += checks.hasLowerCase ? 20 : 0;
  strength += checks.hasUpperCase ? 20 : 0;
  strength += checks.hasSpecial ? 20 : 0;

  if (strength <= 20) return { strength, text: "Very Weak", color: "bg-red-500" };
  if (strength <= 40) return { strength, text: "Weak", color: "bg-orange-500" };
  if (strength <= 60) return { strength, text: "Good", color: "bg-yellow-500" };
  if (strength <= 80) return { strength, text: "Strong", color: "bg-blue-500" };
  return { strength, text: "Very Strong", color: "bg-green-500" };
};

const ResetPassword: React.FC = () => {
  const [email, setEmail] = useState("");
  const [formData, setFormData] = useState<ResetPasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [otp, setOTP] = useState("");
  const [isLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const passwordStrength = calculatePasswordStrength(formData.password);

  const handleSignIn = () => {
    window.location.href = "/login";
  };

  function handleClose(event: React.MouseEvent<HTMLButtonElement>): void {
    event.preventDefault();
    window.history.back();
  }

  function handleSubmit(event: React.FormEvent<HTMLFormElement>): void {
    event.preventDefault();

    if (!email || !otp || !formData.password || !formData.confirmPassword) {
      alert("Please fill in all fields.");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      alert("Passwords do not match.");
      return;
    }

    // Success: Open modal
    setIsModalOpen(true);
  }

  return (
    <>
      <header className="border-b border-[#868686] flex flex-row items-center justify-between">
        <div className="p-1">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={395}
            height={128}
            className="w-[80px] sm:w-[100px] lg:w-[130px] p-1 ml-0 sm:ml-4 md:ml-[60px] mb-2"
          />
        </div>
        <div className="flex items-center justify-between py-1 md:py-2 lg:py-2"></div>
      </header>

      <section className="flex flex-col">
        <div className="px-4 sm:px-3 xl:px-[120px] flex flex-col">
          <div className="flex flex-col md:flex-row items-center justify-between py-6 sm:py-8 md:py-10 lg:py-12 xl:py-16">
            <div className="bg-[#EBEBEB] p-4 sm:p-6 lg:p-8 xl:p-10 rounded-lg shadow-[4px_4px_4px_0px_rgba(0,0,0,0.25)] w-full md:w-1/2 max-w-[95%] sm:max-w-[500px] md:max-w-[450px] lg:max-w-[500px] xl:max-w-[600px] relative mb-6 md:mb-0">
              <button
                className="absolute top-3 right-3 md:top-4 md:right-4 text-gray-500 hover:text-gray-700 font-bold text-xl"
                onClick={handleClose}
              >
                <X size={24} />
              </button>

              <h2 className="font-poppins text-left text-[24px] sm:text-[28px] md:text-[32px] xl:text-[36px] font-[600] mb-2 font-poppins text-[#4B207A]">
                Reset Password{" "}
                <span className="text-[#0A5224] text-xs sm:text-sm xl:text-base font-poppins">
                  EduTech
                </span>
              </h2>

              <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                <div className="relative w-full">
                  <input
                    type="email"
                    id="email"
                    placeholder="Enter Your Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="peer w-full px-3 pt-6 pb-2 border border-[#000000] rounded focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-transparent"
                    required
                  />
                  <label
                    htmlFor="email"
                    className="absolute left-3 bottom-[34px] text-[#0065ff] text-sm transition-all duration-200 peer-placeholder-shown:bottom-[20px] peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:bottom-[35px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3"
                  >
                    Email
                  </label>
                </div>

                <div className="relative w-full">
                  <input
                    type="otp"
                    id="otp"
                    placeholder="Enter OTP"
                    value={otp}
                    onChange={(e) => setOTP(e.target.value)}
                    className="peer w-full px-3 pt-6 pb-2 border border-[#000000] rounded focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-transparent"
                    required
                  />
                  <label
                    htmlFor="otp"
                    className="absolute left-3 bottom-[34px] text-[#0065ff] text-sm transition-all duration-200 peer-placeholder-shown:bottom-[20px] peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:bottom-[35px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3"
                  >
                    Enter OTP
                  </label>
                </div>

                <div className="relative w-full">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder=" "
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="peer w-full px-3 pt-5 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff] mb-[-45px] "
                    required
                  />
                  <label className="absolute left-3 bottom-[23px] text-[#0065ff] text-sm transition-all duration-200 peer-placeholder-shown:top-[15px] peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:bottom-[24px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3">
                    Enter your new password
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-500"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>

                <div className="w-full h-2 rounded mt-2 ">
                  <div className={`h-full ${passwordStrength.color}`} style={{ width: `${passwordStrength.strength}%` }} />
                  <p className="text-xs mt-1 text-gray-700 font-medium">{passwordStrength.text}</p>
                </div>

                <div className="relative w-full ">
                  <input
                    type="password"
                    placeholder=" "
                    value={formData.confirmPassword}
                    onChange={(e) =>
                      setFormData({ ...formData, confirmPassword: e.target.value })
                    }
                    className="peer w-full px-3 mt-5 pt-5 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff] mb-[-45px] "
                    required
                  />
                  <label className="absolute left-3 bottom-[23px] text-[#0065ff] text-sm  transition-all duration-200 peer-placeholder-shown:top-[35px] peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-500 peer-focus:bottom-[24px] peer-focus:text-sm peer-focus:text-[#0065ff] text-[12px] font-[400] font-poppins leading-[150%] space-x-3">
                    Re Enter Your Password
                  </label>
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#0D47A1] text-white font-medium py-3 rounded-md transition-colors font-poppins mt-5 cursor-pointer"
                >
                  {isLoading ? "Resetting..." : "Reset Password"}
                </button>
              </form>
            </div>

            <div className="w-full md:w-1/2 items-center justify-center md:block hidden">
              <WelcomeContent />
            </div>
          </div>
        </div>
      </section>

      {/* Modal */}
      {isModalOpen && (
        <div className="inset-0  bg-black/50 bg-opacity-100 fixed flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
            >
              <X />
            </button>
            <div className="flex flex-col items-center justify-center">
            <div className="relative w-[75px] h-[75px] mb-6  inset-0 bg-green-950   rounded-full flex items-center justify-center">
               
                   <Image
                            src="/assets/images/tick.png"
                            alt="Sort"
                            width={20}
                            height={20}
                            className="w-[35px] sm:w-[40px] lg:w-[40px] cursor-pointer "
                          />
              
              </div>
              
              <h2 className="text-green-900 text-base font-semibold mb-8 text-center font-poppins">
                Password Changed Successfully!
              </h2>
              
              <button
                className="w-full bg-blue-900 text-white py-2 px-5 rounded-lg font-semibold"
                onClick={handleSignIn}
              >
                Go to login page
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ResetPassword;
