"use client";

import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Header<PERSON><PERSON> from "@/components/layout/HeaderPath";
import BlogDetailsHeader from "@/components/blog/blogdetails/BlogDetailsHeader";
import AIInEducation from "@/components/blog/blogdetails/hero";
import Articel from "@/components/blog/blogdetails/articel";
import ArticalContent from "@/components/blog/blogdetails/articelcontent";
import KeepReading from "@/components/blog/blogdetails/keepRead";
import AuthorCard from '@/components/blog/blogdetails/authorCard';

import { cardsData } from "@/components/blog/blogcard";

const Page = () => {
  const searchParams = useSearchParams();
  const blogId = searchParams.get('id');
  const [blogData, setBlogData] = useState<any>(null);

  useEffect(() => {
    if (blogId) {
      const blog = cardsData.find(card => card.id === parseInt(blogId));
      setBlogData(blog);
    }
  }, [blogId]);

  if (!blogData) {
    return (
      <>
        <HeaderPath />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold">Blog not found</h1>
            <p className="text-gray-600 mt-2">The blog you're looking for doesn't exist.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <HeaderPath />
      <BlogDetailsHeader blogData={blogData} />
      <AIInEducation />
      <Articel />
      <ArticalContent />
      <KeepReading />
      <AuthorCard />
    </>
  )
}

export default Page