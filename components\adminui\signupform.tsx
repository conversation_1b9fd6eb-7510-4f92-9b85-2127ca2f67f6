"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import InputField from '@/components/common/adminCommon/InputField';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { Eye, EyeOff } from "lucide-react"

const SignupForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    secretKey: '',
    password: '',
    confirmPassword: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // Regex patterns
  const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);
  const phoneRegex = React.useMemo(() => /^\+?[1-9]\d{1,14}$/, []);

  const strongPasswordRegex = React.useMemo(
    () => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
    []
  );

  // Form validation
  useEffect(() => {
    const { name, email, phone, secretKey, password, confirmPassword } =
      formData;

    const isValid =
      name.trim() !== '' &&
      emailRegex.test(email) &&
      phoneRegex.test(phone) &&
      secretKey.trim() !== '' &&
      strongPasswordRegex.test(password) &&
      password === confirmPassword;

    setIsFormValid(isValid);
  }, [formData, emailRegex, phoneRegex, strongPasswordRegex]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4 py-8">
      <div className="bg-[#eeeeee] rounded-md shadow-md pt-8 lg:w-[914px] md:w-[600px]">
        <div className="flex justify-center">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={130}
            height={60}
            className="h-[55px] w-auto"
          />
        </div>
        <div className="px-1 md:px-15">
          <p className="text-center font-normal text-[15px] md:text-[21px] leading-[30px] font-[Poppins] text-black mb-1">
            This SignUp is exclusively for the{' '}
            <span className="text-[#4b207a]">ADMIN</span> of the ApexIQ Family
            members, enabling them to create and secure their management access.
          </p>
        </div>

        <form
          onSubmit={handleSubmit}
          className="space-y-2 w-full flex flex-col items-center pt-3 py-5"
        >
          <div className="w-full md:w-[75%]">
            <InputField
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              label="Enter Name"
            />
          </div>
          <div className="w-full md:w-[75%]">
            <InputField
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              label="Enter Email"
            />
            {!emailRegex.test(formData.email) && formData.email !== '' && (
              <p className="text-red-600 text-sm mt-1">Invalid email format</p>
            )}
          </div>

          <div className="relative mb-6 w-full md:w-[75%]">
            <PhoneInput
              country={'in'}
              value={formData.phone}
              onChange={(phone) => setFormData({ ...formData, phone })}
              inputStyle={{
                width: '100%',
                backgroundColor: '#eeeeee',
                border: '1px solid black',
                borderRadius: '4px',
                height: '57px',
                paddingLeft: '48px',
                fontSize: '14px',
              }}
              buttonStyle={{
                backgroundColor: '#eeeeee',
                border: '1px solid black',
              }}
              containerClass="w-full"
              inputProps={{
                name: 'phone',
                required: true,
              }}
            />
            <label className="absolute left-3 px-5 top-[-10px] text-center font-medium text-[16px] leading-[30px] font-[Poppins] text-black bg-[#eeeeee]">
              Phone Number
            </label>
            {!phoneRegex.test(formData.phone) && formData.phone !== '' && (
              <p className="text-red-600 text-sm mt-1">Invalid phone number</p>
            )}
          </div>

          <div className="w-full md:w-[75%]">
            <InputField
              id="secretKey"
              name="secretKey"
              type="text"
              value={formData.secretKey}
              onChange={handleChange}
              label="Secret Key"
            />
          </div>

          <div className="w-full md:w-[75%] relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleChange}
              label="Password"
            />
            <button
      type="button"
      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#4b207a] cursor-pointer"
      onClick={() => setShowPassword(!showPassword)}
    >
      {showPassword ? <EyeOff size={22} /> : <Eye size={22} />}
    </button>
            {!strongPasswordRegex.test(formData.password) &&
              formData.password !== '' && (
                <p className="text-red-600 text-sm mt-1">
                  Must be 8+ characters, include uppercase, lowercase, number, special character
                </p>
              )}
          </div>

          <div className="w-full md:w-[75%] relative">
            <InputField
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleChange}
              label="Confirm Password"
            />
            <button
      type="button"
      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#4b207a]  cursor-pointer"
      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
    >
      {showConfirmPassword ? <EyeOff size={22} /> : <Eye size={22} />}
    </button>
            {formData.confirmPassword !== formData.password &&
              formData.confirmPassword !== '' && (
                <p className="text-red-600 text-sm mt-1">Passwords do not match</p>
              )}
          </div>

          <div className="w-full flex items-center justify-center relative">
            <button
              type="submit"
              disabled={!isFormValid}
              className={`py-3 px-10 ${
                isFormValid ? 'bg-[#4b207a] opacity-100 cursor-pointer ' : 'bg-[#4b207a] opacity-50 cursor-not-allowed'
              } text-white text-center font-bold text-[24px] leading-[30px] font-[Poppins] rounded-lg`}
            >
              Register
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignupForm;
