import Link from 'next/link'
import React from 'react'

const Getfree = () => {
  return (
    <div>
       <div className="bg-gradient-to-r from-[#4B207A] to-[#8A3AE0] w-full h-auto py-10 mt-10 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8">
        {/* <!-- Heading --> */}
        <h2 className="text-2xl sm:text-3xl md:text-[48px]  font-[700] font-sans text-white text-center">
        Enhance your skills today.
        </h2>
        {/* <!-- Subtitle --> */}
        <p className="text-base sm:text-lg md:text-[25px] font-medium font-sans  text-white text-center mt-3">
        Whether you are advancing or just beginning, discover your next move here.
        </p>
        {/* <!-- Buttons --> */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
       
          <Link
  href="/signup"
  className="px-5 py-2 bg-white text-[#4B207A] rounded-lg text-base sm:text-[22px] font-semibold font-sans hover:bg-opacity-70"
>
Get Started Free
</Link>
        </div>
      </div>
    </div>
  )
}

export default Getfree
