"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";



const stories = [
  {
    image: "/assets/images/Ellipse1.png",
    name: "<PERSON><PERSON><PERSON>",
    feedback:
      "As a senior engineer I have specially reviewed the System Design content and live classes of My_Tutor...",
    rating: 4,
    profileLink: "#",
  },
  {
    image: "/assets/images/Ellipse1.png",
    name: "<PERSON>",
    feedback:
      "As a senior engineer I have specially reviewed the System Design content and live classes of My_Tutor...",
    rating: 5,
    profileLink: "#",
  },
  {
    image: "/assets/images/Ellipse1.png",
    name: "<PERSON>",
    feedback:
      "As a senior engineer I have specially reviewed the System Design content and live classes of My_Tutor...",
    rating: 4,
    profileLink: "#",
  },
];

const StorySlider = () => {
  return (
    <div className="w-full flex flex-col items-center">
      <div
        className="bg-white p-4 md:p-6 rounded-lg w-full max-w-[308px] md:max-w-[634px] shadow-lg"
        style={{ boxShadow: "0px 4px 25px 0px #00000040" }}
      >
        <Swiper
          modules={[Pagination, Autoplay]}
          spaceBetween={20}
          slidesPerView={1}
          pagination={{ clickable: true, el: ".custom-pagination" }}
          autoplay={{ delay: 3000 }}
        >
          {stories.map((story, index) => (
            <SwiperSlide key={index}>
              <div className="bg-white rounded-lg p-4 md:p-6 shadow-lg">
                {/* Profile Section */}
                <div className="flex items-center justify-between flex-wrap">
                  <div className="flex items-center gap-3">
                    <img
                      src={story.image}
                      alt="Profile"
                      className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
                    />
                    <div>
                      <p className="text-sm md:text-lg font-bold text-purple-700">
                        {story.name}
                      </p>
                      <div className="flex">
                        {Array.from({ length: 5 }, (_, i) => (
                          <svg
                            key={i}
                            className={`w-3 h-3 md:w-4 md:h-4 ${
                              i < story.rating
                                ? "text-yellow-400"
                                : "text-gray-300"
                            } fill-current`}
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                  </div>
                  <a
                    href={story.profileLink}
                    className="text-blue-600 flex items-center gap-1 text-sm"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
                    </svg>
                    <span className="hidden sm:inline">View Profile</span>
                  </a>
                </div>

                {/* Feedback Text */}
                <div className="relative mt-4">
                  <span className="text-purple-700 text-2xl md:text-3xl">❝</span>
                  <p className="text-gray-600 text-xs sm:text-sm md:text-base leading-relaxed">
                    {story.feedback}
                  </p>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Pagination Dots Below Card */}
      <div className="custom-pagination flex justify-center mt-4"></div>
    </div>
  );
};

export default StorySlider;
