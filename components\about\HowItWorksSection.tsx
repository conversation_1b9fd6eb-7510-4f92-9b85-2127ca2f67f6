import Image from 'next/image';
import React from 'react';

const HowItWorksSection = () => {
  return (
    <section className="py-12 px-4 sm:px-6 lg:px-8 pt-0">
      <div className="container mx-auto flex flex-col lg:flex-row items-center md:gap-[80px] gap-[20px]">
        {/* Left Content */}
        <div className="w-full lg:w-2/3">
          <h2 className=" text-[20px] md:text-[36px] text-left font-poppins font-semibold text-black mb-10 mt-2">
            How ApexIQ Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-[60px] gap-y-[20px] md:gap-y-[45px] font-poppins">
            <div className="bg-[#F5F5F5] hover:bg-[#E1DAF7] p-6 rounded-xl shadow-sm transition">
              <h3 className="font-semibold mb-2 text-base sm:text-lg">Expert-Led Learning</h3>
              <p className="text-sm sm:text-base text-gray-700">
                Gain insights from top industry professionals.
              </p>
            </div>
            <div className="bg-[#F5F5F5] hover:bg-[#E1DAF7] p-6 rounded-xl shadow-sm transition">
              <h3 className="font-semibold mb-2 text-base sm:text-lg">Hands-On Projects</h3>
              <p className="text-sm sm:text-base text-gray-700">
                Apply concepts in real-world scenarios.
              </p>
            </div>
            <div className="bg-[#F5F5F5] hover:bg-[#E1DAF7] p-6 rounded-xl shadow-sm transition">
              <h3 className="font-semibold mb-2 text-base sm:text-lg">AI-Powered Personalization</h3>
              <p className="text-sm sm:text-base text-gray-700">
                Tailored learning recommendations for each student.
              </p>
            </div>
            <div className="bg-[#F5F5F5] hover:bg-[#E1DAF7] p-6 rounded-xl shadow-sm transition">
              <h3 className="font-semibold mb-2 text-base sm:text-lg">Community-Driven Learning</h3>
              <p className="text-sm sm:text-base text-gray-700">
                Engage with mentors and peers for a collaborative experience.
              </p>
            </div>
          </div>
        </div>

        {/* Right Image */}
        <div className=" w-full lg:w-1/3">
          <Image
            src="/assets/images/HowItWorks.png"
            alt="How it works"
            width={412}
            height={385}
            className="object-cover rounded-lg shadow-lg"
          />
        </div>
      </div>

      <div className="max-w-[1065px] mx-auto text-center ">
    <hr className="mt-8 sm:mt-20 border-black border-b-1 " />
    </div>
    </section>
  );
};

export default HowItWorksSection;
