
.swiper-slide-custom {
    width: 300px; /* Adjust the width as needed */
    height: 400px; /* Adjust the height as needed */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0; /* Background color for the slides */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Box shadow for the slides */
  }
  
  .swiper_container {
    height: 38rem;
    padding: 2rem 0;
    position: relative;
  }
  
  .swiper-slide {
    width: 16rem;
    height: 15rem;
    position: relative;
  }
  @media (max-width: 70px) {
    .swiper_container {
      justify-content: center;
      display: flex;
      position: relative;
      height: 35rem;
    }
    .swiper-slide {
  
      width: 30rem !important ;
      height: 45rem  !important  ;
    }
    .swiper-slide img {
      width: 28rem !important;
      height: 36rem !important;
    }
  }
 
  
  .swiper-slide-shadow-left,
  .swiper-slide-shadow-right {
    display:flex;
  }
  
  .slider-controler {
    position: relative;
    bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .slider-controler .swiper-button-next {
    left: 50% !important;
    transform: translateX(-50%) !important;
}
  
  @media (max-width: 990px) {
    .slider-controler .swiper-button-next {
      left: 70% !important;
      transform: translateX(-70%) !important;
    }
  }
  
  @media (max-width: 450px) {
    .slider-controler .swiper-button-next {
      left: 80% !important;
      transform: translateX(-80%) !important;
    }
  }
  
  @media (max-width: 990px) {
    .slider-controler .swiper-button-prev {
      left: 30% !important;
      transform: translateX(-30%) !important;
    }
  }
  
  @media (max-width: 450px) {
    .slider-controler .swiper-button-prev {
      left: 20% !important;
      transform: translateX(-20%) !important;
    }
  }
  
  .slider-controler .slider-arrow {
    background: var(--white);
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    left: 42%;
    transform: translateX(-42%);
    filter: drop-shadow(0px 8px 24px rgba(21, 38, 82, 0.1));
  }
  
  .slider-controler .slider-arrow ion-icon {
    font-size: 2rem;
    color: #1919c6;
  }
  
  .slider-controler .slider-arrow::after {
    content: '';
  }
  
  .swiper-pagination {
    position: relative;
    width: 15rem !important;
    bottom: 1rem;
  }
  
  .swiper-pagination .swiper-pagination-bullet {
    filter: drop-shadow(0px 8px 24px rgba(6, 18, 187, 0.1));
  }
  
  .swiper-pagination .swiper-pagination-bullet-active {
    background: var(--primary);
  }