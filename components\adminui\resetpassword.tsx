"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import InputField from "@/components/common/adminCommon/InputField";
import { Eye, EyeOff } from "lucide-react";

const ResetForm: React.FC = () => {
  const [formData, setFormData] = useState({
    email: "",
    otp: "",
    password: "",
    confirmPassword: "",
  });

  const [showPassword, setShowPassword] = useState(false);
   const [showOtp, setShowOtp] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);
  const strongPasswordRegex = React.useMemo(
    () => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
    []
  );

  useEffect(() => {
    const { email, password, confirmPassword } = formData;
    const isValid =
      emailRegex.test(email.trim()) &&
      strongPasswordRegex.test(password) &&
      password === confirmPassword;

    setIsFormValid(isValid);
  }, [formData, emailRegex, strongPasswordRegex]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
  };

  return (
    <div className="w-full flex items-center justify-center bg-white h-auto min-h-[90vh] px-[10px] py-10">
      <div className="w-full max-w-[788px] bg-[#eeeeee] rounded-md shadow-md p-6 lg:px-[70px] lg:py-[50px]">
        {/* Logo */}
        <div className="flex justify-center mb-4">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={181}
            height={60}
            className="lg:w-[181px] w-[100px]"
          />
        </div>

        {/* Heading */}
        <h2 className="text-center text-[#4b207a] font-[Poppins] font-semibold text-[22px] md:text-[32px] leading-tight mb-[20px] lg:mb-[40px]">
          Reset Password
        </h2>

        {/* Form */}
        <form
          onSubmit={handleSubmit}
          className="w-full flex flex-col items-center space-y-5"
        >
          <div className="w-full ">
            <InputField
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              label="Enter Email"
            />
          </div>

          <div className="w-full relative">
            <InputField
              id="otp"
              name="otp"
              type={showOtp ? "text" : "password"}
              value={formData.otp}
              onChange={handleChange}
              label="Enter OTP"
            />
               <button
              type="button"
              className="absolute right-3 top-[20px] transform text-[#4b207a]"
              onClick={() => setShowOtp(!showOtp)}
            >
              {showOtp ? <Eye size={22} /> : <EyeOff size={22} />}
            </button>
          </div>

          <div className="w-full  relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleChange}
              label="Password"
            />
            <button
              type="button"
              className="absolute right-3 top-[20px] transform text-[#4b207a]"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <Eye size={22} /> : <EyeOff size={22} />}
            </button>
            {!strongPasswordRegex.test(formData.password) &&
              formData.password !== "" && (
                <p className="text-red-600 text-sm mt-1">
                  Must be 8+ characters, include uppercase, lowercase, number,
                  and special character
                </p>
              )}
          </div>

          <div className="w-full  relative">
            <InputField
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={handleChange}
              label="Confirm Password"
            />
            <button
              type="button"
              className="absolute right-3 top-[20px] transform text-[#4b207a]"
              onClick={() =>
                setShowConfirmPassword(!showConfirmPassword)
              }
            >
              {showConfirmPassword ? <Eye size={22} /> : <EyeOff size={22} />}
            </button>
            {formData.confirmPassword !== formData.password &&
              formData.confirmPassword !== "" && (
                <p className="text-red-600 text-sm mt-1">
                  Passwords do not match
                </p>
              )}
          </div>

          {/* Submit Button */}
          <div className="w-full ">
            <button
              type="submit"
              disabled={!isFormValid}
              className={`w-full py-3 px-8 text-white text-[18px] md:text-[24px] font-bold font-[Poppins] rounded-lg transition-opacity ${
                isFormValid
                  ? "bg-[#4b207a] opacity-100 cursor-pointer"
                  : "bg-[#4b207a] opacity-50 cursor-not-allowed"
              }`}
            >
              Update Password
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ResetForm;
