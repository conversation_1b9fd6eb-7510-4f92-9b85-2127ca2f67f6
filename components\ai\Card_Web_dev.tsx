"use client"; // Ensure this component runs in the client

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { CardProps } from "@/types/types";



const Card_Web_dev: React.FC<CardProps> = ({ image, title, description }) => {
  const router = useRouter();

  const handleExploreClick = () => {
    // Navigate directly to /course1 when Explore button is clicked
    router.push('/course1');
  };

  return (
    <div
      className="
      h-full
        bg-[#D9D9D9] 
        rounded-lg 
        shadow-lg 
        overflow-hidden 
        border 
        border-gray-300 
        w-full          
        max-w-xs        
        sm:max-w-sm     
        flex 
        flex-col
      "
    >
      {/* Image Section */}
      <div className="w-full h-40 sm:h-64"> 
        {/* h-40 on mobile, h-64 on sm+ screens */}
        <Image
          src={image}
          alt={title}
          className="w-full h-full object-cover"
          width={500} 
          height={500} 
        />
      </div>

      {/* Text Content */}
      <div className="p-3 sm:p-4 flex-1 flex flex-col">
        <h3 className="text-lg sm:text-[23px] font-semibold mb-2 text-black font-poppins">
          {title}
        </h3>
        <p className="text-xs sm:text-[13px] font-medium text-[#393939] flex-grow">
          {description}
        </p>
      </div>

      {/* Buttons */}
      <div className="flex justify-between items-center gap-3 sm:gap-5 px-3 sm:px-5 mb-4 sm:mb-6">
        <button
          onClick={handleExploreClick}
          className="
            w-1/2 
            py-1 sm:py-2 
            border 
            border-[#0A5224] 
            text-[#0A5224] 
            rounded-md 
            text-sm sm:text-2xl 
            font-semibold 
            hover:bg-green-50 
            transition-all cursor-pointer
          "
        >
          Explore
        </button>
        <button
          className="
            w-1/2 
            py-1 sm:py-2 
            bg-[#0A5224] 
            text-white 
            rounded-md 
            text-sm sm:text-2xl 
            font-semibold 
            hover:bg-green-800 
            transition-all cursor-pointer
          "
        >
          Buy
        </button>
      </div>
    </div>
  );
};

export default Card_Web_dev;
