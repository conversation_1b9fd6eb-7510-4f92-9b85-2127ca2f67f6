"use client";

import React from "react";
import Slider, { Settings } from "react-slick";
import Image from "next/image";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { partners } from "@/utils/constant";

const Partner: React.FC = () => {


  const settings: Settings = {
    dots: false,
    infinite: true,
    speed: 1000,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    arrows: false,
    responsive: [
      {
        breakpoint: 1024,
        settings: { slidesToShow: 4 },
      },
      {
        breakpoint: 768,
        settings: { slidesToShow: 3 },
      },
      {
        breakpoint: 480,
        settings: { slidesToShow: 2 },
      },
    ],
  };

  return (
    <section className="py-10 pt-0">
      <div className="small-container">
        <h2 className="text-[22px] md:text-[40px] font-semibold text-[#1D1D1D] text-center mb-[50px] font-poppins">
          Trusted by over 800+ companies
        </h2>
        <Slider {...settings}>
          {partners.map((image, index) => (
            <div key={index} className="flex justify-center items-center">
              <div className="flex items-center justify-center w-[140px] h-[80px] sm:w-[180px] sm:h-[100px] relative">
                <Image
                  src={image}
                  alt={`Partner ${index + 1}`}
                  width={140}
                  height={80}
                  objectFit="contain"
                />
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default Partner;
