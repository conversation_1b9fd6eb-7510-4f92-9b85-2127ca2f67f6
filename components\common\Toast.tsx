// components/Toast.tsx
import { BsCheckCircleFill } from "react-icons/bs";

export const Toast = ({ message }: { message: string }) => {
  return (
    <div className="fixed top-[24px] right-[24px] w-[361px] bg-[#D1FADF] text-[#027A48] p-4 rounded-md shadow-lg flex items-start gap-3 z-50 border border-[#A6F4C5]">
      <BsCheckCircleFill size={24} className="mt-1" />
      <div>
        <p className="font-semibold text-base">{message}</p>
      </div>
      <div className="absolute bottom-0 left-0 h-[4px] bg-[#0A5224] animate-borderGrow" style={{ width: '100%' }}></div>
    </div>
  );
};
