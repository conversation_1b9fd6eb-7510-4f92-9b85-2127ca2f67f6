# Course API Integration Documentation

## Overview
This document describes the integration of the course creation API with the admin dashboard UI.

## API Endpoint
- **URL**: `https://ed.apexiq.ai/api/courses/`
- **Method**: POST
- **Content-Type**: multipart/form-data (for file uploads)

## Environment Variables
The base URL is configured in `.env`:
```
NEXT_PUBLIC_BASE_URL=https://ed.apexiq.ai
```

## API Service Structure

### Location
- **Service File**: `api-services/courses/courses.ts`
- **Index File**: `api-services/courses/index.ts`

### Interface
```typescript
interface CreateCoursePayload {
  name: string;
  price: string; // Optional - empty string becomes "0.00" for free course
  tags?: string[];
  thumbnail?: File;
  is_active?: boolean; // Default: true
  is_published?: boolean; // Default: true
}
```

### Response Format
```typescript
interface CreateCourseResponse {
  message: string;
  data: {
    id: string;
    name: string;
    tags: string[];
    thumbnail: string;
    price: string;
    is_active: boolean;
    is_published: boolean;
    created_by: {
      id: number;
      name: string;
      email: string;
    };
    created_at: string;
  };
}
```

## Component Integration

### AddCourseModal Component
- **Location**: `components/admindashobard/addnewcourse.tsx`
- **Features**:
  - Form validation
  - File upload handling
  - Loading states
  - Success/error feedback with toast notifications
  - Form reset on successful submission

### DashboardMain Component
- **Location**: `components/admindashobard/DashboardMain.tsx`
- **Features**:
  - Triggers course table refresh after successful course creation
  - Manages modal state

## Usage

### Creating a Course
1. Click "Add Courses" button in the admin dashboard
2. Fill in the course details:
   - **Course Name** (required)
   - **Price** (optional, numeric) - Leave empty for free course, enter amount for paid course
   - **Tag** (optional: New Update, Trending, Popular, Live)
   - **Cover Image** (optional: PNG, JPG, JPEG)
3. Click "Create Course" button
4. Course will be created with `is_active: true` and `is_published: true` by default
5. Success/error feedback will be displayed via toast notifications

### Course Status Logic
- **is_active: true** - Course is active and will appear in admin dashboard table
- **is_active: false** - Course is deactivated and won't appear in admin dashboard table
- **is_published: true** - Course is published and visible to students
- **is_published: false** - Course is unpublished and not visible to students

### Deactivating a Course (Soft Delete)
1. Click the "X" (delete) button next to any course in the table
2. Confirm deactivation in the modal
3. Course will be updated to `is_active: false` and `is_published: false`
4. Course will disappear from the admin table but data is preserved in database

### Form Validation
- Course name is required
- Price is optional - if entered, must be a valid number ≥ 0
- File uploads are validated for supported formats (PNG, JPG, JPEG)

### Course Pricing Logic
- **Free Course**: Leave price field empty → automatically sets price to "0.00"
- **Paid Course**: Enter any price > 0 → course becomes paid with that price

### Loading States
- Submit button shows "Creating..." during API call
- Submit button is disabled during submission
- Close button is disabled during submission

## Error Handling
- Network errors are caught and displayed via toast notifications
- Form validation errors are shown immediately
- API errors are logged to console and shown to user

## File Upload
- Cover images are uploaded as FormData
- Supported formats: PNG, JPG, JPEG
- File name is displayed after selection

## Integration with Existing Code
- Uses existing `makeRequest` utility from `api-services/utils.ts`
- Follows existing API service patterns
- Uses React Query for state management
- Integrates with existing toast notification system
