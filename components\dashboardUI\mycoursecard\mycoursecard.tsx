import Image from "next/image"
import { useRouter } from "next/navigation" // for Next.js 13+ (if you're using 'app' directory)

interface CourseCardProps {
  course: {
    id: number
    title: string
    instructor: string
    duration: string
    rating: number
    image: string
  }
}

export default function CourseCard({ course }: CourseCardProps) {
  const router = useRouter();

  const handleClick = () => {
    if (course.id === 1) { // check if it's the first card
      router.push("/my-purchase/purchasecourse");
    }
  };

  return (
    <div className="py-3">
      <div 
        className="bg-[#eaeaea] rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleClick}
      >
        <div className="flex flex-col sm:flex-row items-start p-3">
          <div className="sm:w-30 h-[268px]">
            <Image
              src={course.image}
              alt={course.title}
              width={100}
              height={100}
              className="w-full h-full"
            />
          </div>

          <div className="p-4 sm:p-6 flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4">
            <div className="space-y-1 md:flex-1 px-6">
              <h2 className="font-[Poppins] font-medium text-[24px] leading-[100%] tracking-[0%] text-black">{course.title}</h2>
              <p className="font-[Poppins] font-normal text-[16px] leading-[100%] tracking-[0%] text-black">By {course.instructor}</p>
            </div>

            <div className="flex items-center gap-6 md:gap-30">
              <div className="flex items-center">
                <div className="sm:w-12 h-12 p-2">
                  <Image
                    src="/assets/images/Vector(4).png"
                    alt={course.title}
                    width={100}
                    height={100}
                    className="w-full h-full"
                  />
                </div>
                <span className="text-black font-poppins font-normal text-base leading-[100%] tracking-[0%]">{course.duration}</span>
              </div>

              <div className="flex items-center justify-center mr-24">
                <div className="sm:w-12 h-12 p-2">
                  <Image
                    src="/assets/images/flame1.png"
                    alt={course.title}
                    width={200}
                    height={200}
                    className="w-full h-full"
                  />
                </div>
                <span className="text-black font-poppins font-medium text-base leading-[100%] tracking-[0%]">{course.rating}</span>
              </div>
            </div>

            <button className="bg-[#4b207a] text-white text-xl px-9 py-4 mr-4 rounded-md whitespace-nowrap cursor-pointer">
              View Course
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
