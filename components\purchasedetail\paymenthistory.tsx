export default function PaymentHistory() {
  const paymentData = [
    {
      courseName: "#prompt",
      date: "Mar 1, 2023",
      amount: "4000",
      status: "Success",
    },
  ];

  return (
<div className="mt-5 lg:mt-12 px-4 md:px-8 w-full">
  <h2 className="text-xl sm:text-2xl md:text-3xl mb-4 text-center md:text-left font-poppins font-medium">
    Payment History
  </h2>

  <div className="w-full overflow-x-auto">
    <div className="min-w-full inline-block align-middle">
      <table className="lg:min-w-[500px] w-full border-collapse text-sm md:text-base">
        <thead className="bg-gray-100">
          <tr>
            <th className="text-left py-3 px-4">Date</th>
            <th className="text-left py-3 px-4">Amount</th>
            <th className="text-left py-3 px-4">Status</th>
          </tr>
        </thead>
        <tbody>
          {paymentData.map((payment, index) => (
            <tr key={index} className="border-t border-gray-300 bg-[#f3f4f6] hover:bg-gray-100">
              <td className="py-3 px-4">{payment.date}</td>
              <td className="py-3 px-4">₹{payment.amount}</td>
              <td className="py-3 px-4">
                <span className="text-green-600 font-semibold">{payment.status}</span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
</div>

  );
}
