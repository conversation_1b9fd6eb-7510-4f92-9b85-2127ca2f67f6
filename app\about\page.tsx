import HeaderPath from "@/components/layout/HeaderPath";
import Image from "next/image";
import React from "react";
import Link from "next/link";
import StatsSection from "@/components/about/StatsSection";
import WhoWeAreSection from "@/components/about/WhoWeAreSection";
import HowItWorksSection from "@/components/about/HowItWorksSection";
import OfferingsSection from "@/components/about/OfferingsSection";
import WhyChooseApexIQ from "@/components/about/WhyChooseApexIQ";
import Testimonials from "@/components/about/Testimonials";
import Partner from "@/components/about/Partner";
import Getfree from "@/components/pathshala/Getfree";

const page = () => {
  return (
    <>
      <HeaderPath />

      {/* Hero Section */}
      <section className="bg-[#d2d2d2] shadow-lg md:pt-[50px] pt-[20px]">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 grid-cols-1 items-center sm:gap-20 gap-5">
            {/* Left Text Content */}
            <div className="w-full text-center lg:text-left">
              <h1 className="text-[20px] md:text-[40px] px-2 text-start mt-5 sm:mt-0 font-poppins font-semibold mb-4">
                Empowering Learners with{" "}
                <span className="text-[#631992]">Future-Ready Skills</span>
              </h1>
              <p className=" px-2 text-start text-[16px] font-poppins font-medium text-black mb-6 max-w-2xl mx-auto lg:mx-0">
                <span className="text-[#631992] font-[800]">ApexIQ</span> is
                dedicated to providing innovative learning experiences, helping
                you achieve limitless possibilities through expert-led courses
                and hands-on learning.
              </p>

              <Link
                href="/signup"
                className="bg-[#1B57C0] hover:bg-[#164aad] text-white px-4 sm:px-6 py-3 rounded-md sm:text-lg text-xs font-semibold transition-all duration-300 inline-block mx-2 text-start hover:shadow-[0px_1px_8px_4px_#164aad78]"
              >
                Join Us
              </Link>
            </div>

            {/* Right Image */}
            <div className="flex justify-center lg:justify-end">
              <Image
                src="/assets/images/main-student3.png"
                alt="Hero section"
                className="md:w-[384px] w-[280px]"
                width={384}
                height={495}
              />
            </div>
          </div>
        </div>
      </section>

      <StatsSection />
      <WhoWeAreSection />
      <HowItWorksSection/>
      <OfferingsSection/>
      <WhyChooseApexIQ />
      <Testimonials/>
      <Partner/>
      <Getfree/>
    </>
  );
};

export default page;
