"use client"

import * as React from "react"
import { ChevronDown, Menu, X } from "lucide-react"
import * as CollapsiblePrimitive from "@radix-ui/react-collapsible"
import { cn } from "@/api-services/utils"
import Image from "next/image"
import { modules } from "@/utils/constant"




const Collapsible = CollapsiblePrimitive.Root
const CollapsibleTrigger = CollapsiblePrimitive.Trigger

const CollapsibleContent = React.forwardRef<
  React.ElementRef<typeof CollapsiblePrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <CollapsiblePrimitive.Content
    ref={ref}
    className={cn(
      "data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down overflow-hidden transition-all",
      className,
    )}
    {...props}
  >
    {children}
  </CollapsiblePrimitive.Content>
))
CollapsibleContent.displayName = "CollapsibleContent"

export function CourseSidebar() {
  // State for mobile dropdown visibility
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false)

  // Sample data for modules

  // Track open state of modules
  const [openModules, setOpenModules] = React.useState<Record<string, boolean>>({
    "module-1": true, // Module 1 is open by default
  })

  const toggleModule = (moduleId: string) => {
    setOpenModules((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }))
  }

  // Function to render the appropriate icon based on item type
  const renderItemIcon = (type: string) => {
    switch (type) {
      case "lesson":
        return (
          <div className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7  md:w-[30px] md:h-[30px]  rounded-full border-3 border-[#7d2ea7] bg-white ">
            <div className="relative w-3 h-3 sm:w-4 sm:h-4  md:w-[15px] md:h-[15px]">
              <Image
                src="/assets/images/play-button1.png"
                alt="Play button"
                fill
                
              />
            </div>
          </div>
        )
      case "reading":
        return (
          <div className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 md:w-[30px] md:h-[30px] rounded-full border-3 border-[#d0c3c7] bg-white">
            <div className="relative w-3 h-3 sm:w-4 sm:h-4 md:w-[15px] md:h-[15px]">
              <Image
                src="/assets/images/play1.png"
                alt="Play icon"
                fill
               
              />
            </div>
          </div>
        )
      case "book":
        return (
          <div className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7  md:w-[30px] md:h-[30px]  rounded-full border-3 border-[#d0c3c7] bg-white">
            <div className="relative w-3 h-3 sm:w-4 sm:h-4  md:w-[15px] md:h-[15px]">
              <Image
                src="/assets/images/book2.png"
                alt="Book icon"
                fill
               
              />
            </div>
          </div>
        )
      case "assignment":
        return (
          <div className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7  md:w-[30px] md:h-[30px]  rounded-full border-3 border-[#D0C3D7] bg-white">
            <div className="relative w-3 h-3 sm:w-4 sm:h-4  md:w-[15px] md:h-[15px]">
              <Image
                src="/assets/images/Maskgroup(3).png"
                alt="Assignment icon"
                fill
               
              />
            </div>
          </div>
        )
      default:
        return null
    }
  }

  // The content of the sidebar, which will be used in both mobile and desktop views
  const sidebarContent = (
    <>
      {modules.map((module) => (
        <Collapsible
          key={module.id}
          open={openModules[module.id]}
          onOpenChange={() => toggleModule(module.id)}
          className="mb-1"
        >
          <CollapsibleTrigger
            className={cn(
              "h-[78px] gap-2 flex flex-col items-start justify-center w-full px-3 sm:px-4 py-2 sm:py-3 text-sm font-medium rounded-t-md cursor-pointer transition-colors duration-200",
              openModules[module.id] ? "bg-[#4b207f] text-white" : "bg-white text-gray-800 hover:bg-purple-50"
            )}
          >
            <div className="flex items-center justify-between w-full">
              <span className="text-sm sm:text-base font-semibold truncate">{module.title}</span>
              <ChevronDown
                className={cn("h-4 w-4 sm:h-5 sm:w-5 transition-transform duration-200 flex-shrink-0 ml-2", 
                  openModules[module.id] ? "rotate-180" : "")}
              />
            </div>
            {module.intro && <span className="text-xs sm:text-sm font-normal w-full text-left">{module.intro}</span>}
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className=" w-[100%] ">
            {module.items.map((item, index) => (
  <div
    key={item.id}
    className="relative pl-8 sm:pl-10 md:pl-12 py-4 sm:py-6 md:py-8 text-sm bg-white w-full"
  >
    {/* Top half of the line (except first item) */}
    {index !== 0 && (
      <div className="absolute left-[31px] md:left-[42px] top-0 h-[50%] border-l-2 border-dashed border-gray-400 z-0" />
    )}

    {/* Bottom half of the line (except last item) */}
    {index < module.items.length - 1 && (
      <div className="absolute left-[31px] md:left-[42px] top-1/2 h-[50%] border-l-2 border-dashed border-gray-400 z-0" />
    )}

    {/* Icon */}
    <div className="absolute left-0 sm:left-1 md:left-2 top-[22px] px-5 z-10">
      {renderItemIcon(item.type)}
    </div>

    {/* Content */}
    <div className="pr-2 sm:pr-4">
      <p
        className={`text-xs sm:text-sm md:text-base font-normal break-words md:mt-[-10] px-5 ${
          index === 0 ? 'text-black' : 'text-[#5B5B5B]'
        }`}
      >
        {item.title}
      </p>
    </div>
  </div>
))}

            </div>
             
          </CollapsibleContent>
        </Collapsible>
      ))}
    </>
  )
 
  return (
    <>
      {/* Mobile toggle button - only visible on small screens */}
      <div className="md:hidden w-full bg-[#e8e4fe] p-3 sticky top-0 z-10 shadow-sm">
        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="flex items-center justify-between w-full px-4 py-2 text-sm font-medium bg-white rounded-md text-[#4b207f] border border-purple-200"
        >
          <span className="font-medium">Course Modules</span>
          {mobileMenuOpen ? <X size={18} /> : <Menu size={18} />}
        </button>
        
        {/* Mobile dropdown content */}
        {mobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-[#e8e4fe] shadow-lg z-20 max-h-[80vh] overflow-y-auto">
            {sidebarContent}
          </div>
        )}
      </div>

      {/* Desktop sidebar - hidden on mobile */}
      <div className="hidden md:block w-full md:w-[288px] bg-[#e8e4fe] text-gray-800  flex-col overflow-x-hidden overflow-y-auto md:h-[1185px]">
        {sidebarContent}
      </div>
    </>
  )
}