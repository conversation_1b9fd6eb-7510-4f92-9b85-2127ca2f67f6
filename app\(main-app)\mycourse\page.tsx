"use client";
import Image from "next/image";
import { useState } from "react";
import { Search,  X } from "lucide-react";
import { courses } from "@/utils/constant";
import CourseCard from "@/components/UI/mycourseCard";
import PaidHeader from "@/components/layout/PaidHeader";
import Link from "next/link";

export default function MyCourses() {
  const [searchText, setSearchText] = useState("");

  const clearSearch = () => {
    setSearchText("");
  };

  // Filter courses based on search text
  const filteredCourses = courses.filter((course) => {
    if (searchText.trim() === "") return true; // Show all courses when search is empty
    
    const searchLower = searchText.toLowerCase();
    return course.title.toLowerCase().includes(searchLower);
  });

  return (
    <>
     
    <PaidHeader />
<main className="bg-white mt-4 md:px-[60px] pt-[60px]">
      <main className="container">
        <div className="px-2 sm:px-4 flex flex-col sm:flex-row items-center justify-between gap-4 w-full">
          <div className="flex-1 w-full max-w-md relative">
            <div className="relative mt-5 sm:w-[400px] md:w-[551px] lg:w-[661px] h-[55px] lg:h-[61px] shadow-md rounded-xl bg-[#ffffff] flex items-center border-none">
            <div className="bg-[#f1f1f1] w-[60px] h-full rounded-l-xl flex items-center justify-between px-6 text-black">
  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#4B207A] h-5 w-5 sm:h-7 sm:w-7 font-poppins " />
  <input
    type="text"
    value={searchText}
    onChange={(e) => setSearchText(e.target.value)}
    placeholder="Search your courses"
    className="pl-2 pr-10 py-5 flex-grow rounded-r-xl text-sm sm:text-base focus:outline-none text-black ml-[50px]"
  />
  {searchText.length > 0 && (
    <button
      className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors text-black"
      onClick={clearSearch}
    >
      <X className="h-5 w-5 sm:h-6 sm:w-6 cursor-pointer" />
    </button>
  )}
</div>
           
            </div>
          </div>
          <div className="mt-4 sm:mt-3 relative">
            <Link href="/paidcourse" className="flex flex-col items-center sm:items-start no-underline">
              <div className="relative">
                {/* Position the button first */}
                <button className="bg-[#4B207A] text-white font-medium px-4 py-2 sm:px-6 sm:py-3 rounded-lg shadow-lg transform transition-transform duration-200 hover:scale-105 flex items-center justify-center gap-2 text-sm sm:text-base w-full sm:w-auto font-poppins cursor-pointer mt-5">
                  Paid Courses
                </button>

                {/* Position the image to overlap the button */}
                <div className="absolute top-2 sm:-left-6 transform -translate-x-1/2 sm:translate-x-0">
                  <Image
                    src="/assets/images/certificatelogo.png"
                    alt="certificatelogo"
                    width={40}
                    height={40}
                    className="object-contain w-[30px] md:w-[40px]"
                  />
                </div>
              </div>
            </Link>
          </div>
        </div>
        <section className="px-4 py-5 md:py-8">
          <div className="flex items-center justify-between mb-6 mt-3 font-poppins">
            <h2 className="text-2xl font-bold font-poppins">My Courses</h2>
            
          </div>

          {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-[20px] lg:gap-[50px]">
  {filteredCourses.map((course) => (
    <CourseCard key={course.id} course={{ ...course, link: course.link ?? "" }} />
  ))}
</div>
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500 text-lg">No courses match your search criteria.</p>
              <button 
                onClick={clearSearch}
                className="mt-4 text-[#4B207A] font-medium hover:underline"
              >
                Clear search and show all courses
              </button>
            </div>
          )}
        </section>
      </main>
      </main>
    </>
  );
}