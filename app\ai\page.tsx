import Card_Web_dev from "@/components/ai/Card_Web_dev";
import CertificateSection from "@/components/ai/CertificateSection";
import KeyBenefits from "@/components/ai/keybenefits";
import HeaderPath from "@/components/layout/HeaderPath";
import { cardsData } from "@/utils/constant";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const page = () => {
  return (
    <>
      <HeaderPath />
      {/* <!-- hero section --> */}
      <section className="bg-gradient-to-r from-[#d2d2d2] to-[#a4a4a4] py-6 sm:py-10 md:py-[50px]">
        <div className="flex items-center">
          <div className="container px-4 mx-auto">
            <div className="flex flex-col lg:flex-row items-center justify-center gap-4 md:gap-8">
              <div className="w-full lg:w-1/2 text-center">
                <h1 className="font-poppins font-bold text-2xl sm:text-3xl md:text-[40px] leading-normal sm:leading-[27px] tracking-[-0.019em] text-[#000000] mb-2 md:mb-4">
                  Transform Your Future with AI
                </h1>
                <p className="font-poppins font-medium text-base sm:text-lg md:text-[20px] leading-normal sm:leading-[30px] tracking-[-0.019em] text-center text-[#000000] mb-2 md:mb-4">
                  Master AI skills and revolutionize industries with
                  cutting-edge tools, insights, and real-world applications.
                </p>
                <button className="bg-[#4B207A] text-white px-4 sm:px-8 py-2 sm:py-3 rounded-[8px] text-base sm:text-lg md:text-[22px] mt-2 md:mt-4 font-semibold">
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-6 sm:py-10 md:py-[50px]">
        <div className="container px-4 mx-auto">
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 items-start">
            {/* <!-- Text Content --> */}
            <div className="space-y-3 sm:space-y-6">
              <h1 className="font-poppins font-semibold text-2xl sm:text-3xl md:text-[32px] leading-normal sm:leading-[25px] tracking-[-0.01em] text-start text-black">
                What is Artificial Intelligence (AI)?
              </h1>

              <p className="font-poppins font-normal text-sm sm:text-base md:text-[16px] leading-normal sm:leading-[25px] tracking-[-0.01em] text-black">
                Artificial Intelligence (AI) refers to the development of
                computer systems that can perform tasks typically requiring
                human intelligence. These tasks include problem-solving,
                learning, and decision-making. AI foundations lie in concepts
                like algorithms, logic, and the history of its evolution from
                early computing.
              </p>

              <p className="font-poppins font-normal text-sm sm:text-base md:text-[16px] leading-normal sm:leading-[25px] tracking-[-0.01em] text-black">
                At the core of AI is neural networks and deep learning, which
                mimic the human brain&apos;s functioning to enable machines to learn
                from data and make predictions. These techniques are crucial for
                creating systems that can recognize patterns, understand speech,
                and perform complex tasks without human intervention.
              </p>

              <p className="font-poppins font-normal text-sm sm:text-base md:text-[16px] leading-normal sm:leading-[25px] tracking-[-0.01em] text-black">
                AI also raises important questions about ethics and governance.
                As AI systems increasingly impact industries and societies,
                ethical considerations like fairness, transparency, and
                accountability are essential. Today, AI applications are widely
                used in sectors like healthcare, finance, and transportation,
                revolutionizing how we live and work.
              </p>

              <Link href="/signup" className="max-w-max">
  <div className="bg-[#4b207a] max-w-max text-white px-4 py-2 sm:px-7 sm:py-3 rounded-md cursor-pointer font-semibold font-poppins text-xs sm:text-base md:text-[22px] text-center">
    Buy Now
  </div>
</Link>
            </div>

            {/* <!-- Image --> */}
            <div className="relative mt-6 lg:mt-0 lg:h-full">
              <Image
                src="/assets/images/undraw_robotics_kep01.png"
                alt="AI Business Illustration"
                className="w-full h-auto max-w-lg mx-auto lg:absolute lg:right-0 lg:top-20"
                width={500}
                height={500}
              />
            </div>
          </div>
        </div>
      </section>

      <div className="border-t border border-gray-500 mt-0 sm:mt-5 mx-auto w-11/12 sm:w-2/3"></div>
      
      {/* <!-- learning path --> */}
      <section className="bg-white py-6 sm:py-10 md:py-[50px]">
        <div className="container px-4 mx-auto">
          {/* <!-- Header Section --> */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4 lg:mb-2">
            <h2 className="font-poppins font-semibold text-xl sm:text-2xl md:text-[32px] leading-normal sm:leading-[25px] tracking-[-0.01em] text-center sm:text-left text-black mb-5 sm:mb-8">
              Web Development Learning Paths
            </h2>
          </div>
          
          {/* <!-- Toggle Buttons --> */}
          <div className="flex font-poppins gap-2 sm:gap-4 lg:gap-6 mb-6 justify-center sm:justify-start">
            <button className="px-3 py-2 md:px-12 md:py-4 bg-[#D9D9D9] rounded-md text-xs sm:text-sm md:text-xl font-semibold cursor-pointer">
              Paid Courses
            </button>
            <button className="px-3 py-2 md:px-12 md:py-4 bg-[#D9D9D9] rounded-md text-xs sm:text-sm md:text-xl font-semibold cursor-pointer">
              Free Courses
            </button>
          </div>
          
          {/* See All Topics Button */}
          <div className="flex justify-center sm:justify-end items-center mb-6">
            <button className="bg-[#4B207A] text-white text-sm sm:text-base md:text-xl font-medium px-4 py-1 sm:px-5 sm:py-2 rounded-md cursor-pointer">
              See All Topics
            </button>
          </div>
          
          {/* Cards Grid */}
          <div className="overflow-x-auto pb-4 sm:pb-0">
            {/* Use flex-nowrap for mobile scrolling and grid for larger screens */}
            <div className="flex flex-nowrap gap-4 lg:grid lg:grid-cols-2 xl:grid-cols-3">
              {cardsData.map((card, index) => (
                <div key={index} className="min-w-[250px] sm:min-w-[300px] max-w-sm flex-none">
                  <Card_Web_dev
                    image={card.image}
                    title={card.title}
                    description={card.description}
                    href={card.href}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <div className="border-t border border-gray-500 mt-2 sm:mt-10 mx-auto w-11/12 sm:w-2/3"></div>
      
      {/* <!-- certificate --> */}
      <CertificateSection />
      <KeyBenefits />
    </>
  );
};

export default page;