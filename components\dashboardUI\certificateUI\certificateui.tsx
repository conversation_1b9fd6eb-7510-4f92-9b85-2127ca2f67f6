"use client"

import { useState } from "react"

import {  Download } from "lucide-react"
import Image from "next/image"
import WelcomeHeader from "../main-container/welcome-header"

type FilterType = "completed" | "inprogress" | "upcoming"

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState<FilterType>("completed")

  const filters: { label: string; value: FilterType }[] = [
    { label: "Completed(1)", value: "completed" },
    { label: "In Progress(1)", value: "inprogress" },
    { label: "Upcoming(1)", value: "upcoming" },
  ]

  return (
    <div className="w-full justify-center items-center py-[70px] ">
      <div >
           <WelcomeHeader />

           <div className="flex justify-end w-full lg:px-4  ">
  <div className="flex border border-black rounded-[15px] overflow-hidden w-full max-w-md">
    {filters.map(({ label, value }, index) => (
      <button
        key={value}
        onClick={() => setActiveTab(value)}
        className={`flex-1 py-2 text-center text-[15px] md:text-base transition-colors ${
          activeTab === value ? "bg-[#DDDDDD] font-normal" : "bg-[#F3F3F3]"
        } ${index !== filters.length - 1 ? "border-r border-black" : ""}`}
      >
        {label.replace("Progress", "process").replace("Upcoming", "Up Coming")}
      </button>
    ))}
  </div>
</div>

        {/* Certificate Sections */}
        {activeTab === "completed" && (
          <Section title="Completed Courses" badgeImg>
            <CertificateCard type="completed" title="Python" subtitle="Certificate Available 🎉" />
          </Section>
        )}
        {activeTab === "inprogress" && (
          <Section title="Certificate in Progress">
            
            <CertificateCard 
              type="inprogress"
              title="Computer Vision"
              subtitle=" "
              timeRemaining={12}
            />
          </Section>
        )}
        {activeTab === "upcoming" && (
          <Section title="Upcoming Certificates">
            <CertificateCard
              type="upcoming"
              title="GitHub"
              subtitle="Score 80%+ to unlock this certificate"
              progress={75}
            />
          </Section>
        )}
      </div>
    </div>
  )
}

function Section({
  title,
  children,
  badgeImg = false,
}: {
  title: string
  children: React.ReactNode
  badgeImg?: boolean
}) {
  return (
    <div className="mt-6 lg:px-8  ">
      <h2 className="text-xl font-semibold mb-4 flex items-center gap-2 ">
        {title}
        {badgeImg && (
          <Image
            src="/assets/images/stars1.png"
            alt="stars"
            width={40}
            height={40}
            className="w-[45px] sm:w-[55px] lg:w-[66px] "
          />
        )}
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6  ">{children}</div>
    </div>
  )
}

interface CertificateCardProps {
  type: "completed" | "inprogress" | "upcoming"
  title: string
  subtitle: string
  timeRemaining?: number
  progress?: number
}

function CertificateCard({ type, title, subtitle, timeRemaining, progress }: CertificateCardProps) {
  return (
    <div>
      {/* Show this message only for inprogress certificates */}
      {type === "inprogress" && (
        <p className="text-[#ff8400] py-5 font-medium text-[15px] leading-[20px] tracking-[0]">
          Please wait {timeRemaining} hours for generation
        </p>
      )}

      <div className="relative bg-[#ededed]">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <Image
              src="/certificate.png"
              alt="Certificate"
              width={406}
              height={200}
              className={`max-h-[276px] ${type !== "completed" ? "opacity-25" : ""}`}
            />

            {type === "inprogress" && (
              <Overlay>
                <div className="flex flex-col items-center gap-2">
                  <Image
                    src="/assets/images/clock.png"
                    alt="Clock"
                    className="h-16 w-16 object-contain"
                    width={64}
                    height={64}
                  />
                  <span className="text-text-[#ff8400] font-bold text-lg">{timeRemaining}hrs</span>
                </div>
              </Overlay>
            )}

            {type === "upcoming" && (
              <Overlay>
                <ProgressCircle value={progress ?? 0} />
              </Overlay>
            )}
          </div>
        </div>

        <div className="p-3">
          <div className="flex justify-between items-center">
            <h3 className="font-poppins font-medium text-[24px] leading-[18px] tracking-[0] text-black pb-1">
              {title}
            </h3>
            <button
              className={type === "completed" ? "text-[#0a5224]" : "text-gray-400 cursor-not-allowed"}
            >
              <Download className="h-[40px] w-[40px]" />
            </button>
          </div>
          <p
            className={`font-normal text-[16px] leading-[20px] tracking-[0%] font-[Poppins] ${
              type === "completed"
                ? "text-blue-500"
                : type === "inprogress"
                ? "text-orange-500"
                : "text-blue-600"
            }`}
          >
            {subtitle}
          </p>
        </div>
      </div>
    </div>
  )
}


function Overlay({ children }: { children: React.ReactNode }) {
  return <div className="absolute inset-0 flex flex-col items-center justify-center">{children}</div>
}

function ProgressCircle({ value }: { value: number }) {
  return (
    <div className="relative h-16 w-16">
      <svg className="w-full h-full" viewBox="0 0 36 36">
        <circle cx="18" cy="18" r="16" fill="none" stroke="#e6e6e6" strokeWidth="2" />
        <circle
          cx="18"
          cy="18"
          r="16"
          fill="none"
          stroke="#4B207A"
          strokeWidth="2"
          strokeDasharray={`${value} 100`}
          strokeLinecap="round"
          transform="rotate(-90 18 18)"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center text-[#4B207A] font-bold">
        {value}%
      </div>
    </div>
  )
}
