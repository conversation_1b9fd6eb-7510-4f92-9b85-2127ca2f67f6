import React from 'react'
import CardTestimonial from '../common/CardTestimonial'
import TestimonialSlider from '../TestimonialSlider';
import StorySlider from './StorySlider';

const testimonials = [
    {
      image: "/assets/images/Ellipse1(1).png",
      name: "<PERSON>",
      designation: "Student",
      feedback: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      rating: 5,
    },
    {
      image: "/assets/images/Ellipse1(1).png",
      name: "<PERSON>",
      designation: "Student",
      feedback: "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
      rating: 4,
    },
    {
      image: "/assets/images/Ellipse1(1).png",
      name: "<PERSON>",
      designation: "Student",
      feedback: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.",
      rating: 5,
    },
   
  ];
  
const Testimonial = () => {
  return (
   <>
        {/* <!-- Testimonial Section --> */}

  
<div className="bg-white px-4 sm:px-6 lg:px-8">
  <div className="container mx-auto border-b-2 border-gray-500">
    <div className="grid gap-5 lg:grid-cols-2 lg:gap-12 pb-8 md:pb-[60px]">
      {/* Left Column */}
      <div className="space-y-4 text-center md:text-left">
        <h1 className="text-lg md:text-4xl font-bold font-poppins text-gray-900 md:leading-[50px]">
          Thousands of stories <br/> of growth
        </h1>
        <p className="text-gray-600 text-sm sm:text-base">
          Find out how our learners transformed their careers after learning with us
        </p>
      </div>

      {/* Right Column - Testimonial Slider */}
      <div className="w-full flex justify-center md:justify-end">
        <StorySlider />
      </div>
    </div>
  </div>

  <div className="container">
    <div className="text-center mb-12 pt-8 md:pt-[60px] pb-8">
      <h2 className="md:text-[32px] text-[20px] font-[600] font-poppins text-center mb-8">
        What our Clients and Students Say
      </h2>

      <div className="w-full">
        <TestimonialSlider testimonials={testimonials} />
      </div>
    </div>
  </div>
</div>

   </>
  )
}

export default Testimonial