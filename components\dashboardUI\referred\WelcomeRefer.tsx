'use client';

import { useEffect, useRef, useState } from 'react';
import { IoIosNotifications } from "react-icons/io";
import { IoWallet, IoClose } from "react-icons/io5";
import Image from 'next/image';
// import { BsDot } from "react-icons/bs";

export default function WelcomeRefer() {
  const [showNotification, setShowNotification] = useState(false);
  const [showWallet, setShowWallet] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const walletRef = useRef<HTMLDivElement>(null);

  const notifications = [
    { text: '+1 points for you New Invitee : Lathu', time: '2 min.', unread: true },
    { text: 'You just got a new referral request from <PERSON><PERSON><PERSON>!', time: '2 min.', unread: true },
    { text: 'The referral link you received from <PERSON><PERSON> is about to expire!', time: '5 min.', unread: true },
    { text: 'You just got a new referral request from <PERSON>!', time: '10 min.', unread: true },
    { text: 'You just got a new referral request from <PERSON><PERSON><PERSON>!', time: '2 Days ago', unread: false },
    { text: 'The referral link you received from <PERSON><PERSON><PERSON> is about to expire!', time: '2 Days ago', unread: false },
    { text: '03 Initial Free access', time: '3 Days ago', unread: false },
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotification(false);
      }
      if (walletRef.current && !walletRef.current.contains(event.target as Node)) {
        setShowWallet(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <>
      <div className="flex justify-between items-start pb-2 relative pt-[30px]">
        {/* Text Section */}
        <div className='max-w-[540px]'>
          <h1 className="font-poppins font-semibold text-[20px] md:text-[30px] text-[#4B207A] mb-1">
            Welcome !
          </h1>
          <p className="text-[#4F4F4F] font-poppins font-normal text-[18px] md:text-[20px]">
            Start growing your network today with ApexIQ Referrals.
            Invite friends to enroll in courses and earn rewards!
          </p>
        </div>

        {/* Icons Section */}
        <div className="flex gap-4">
          <div className="relative cursor-pointer" onClick={() => setShowNotification(!showNotification)}>
            <div className="border border-[#565656] p-1 rounded-[6px]">
              <IoIosNotifications size={30} className="text-[#565656]" />
            </div>
            <span className="absolute top-[-5px] right-[-5px] bg-[#FF0000] w-[20px] h-[20px] text-white text-[10px] font-poppins flex items-center justify-center px-1.5 rounded-full">
              1
            </span>
          </div>

          <div className="cursor-pointer" onClick={() => setShowWallet(!showWallet)}>
            <div className="border border-[#565656] p-1 rounded-[6px]">
              <IoWallet size={30} className="text-[#565656]" />
            </div>
          </div>
        </div>
      </div>

      {/* Notification Modal */}
      {showNotification && (
        <div className="fixed inset-0 bg-[#0000004f] z-50 flex justify-end pr-8 pt-20">
          <div
            ref={notificationRef}
            className="bg-[#F4F4F4] w-[352px] max-h-[552] overflow-y-auto rounded-md shadow-lg relative"
          >
            {/* Close Button */}
            <button
              onClick={() => setShowNotification(false)}
              className="absolute top-3 right-3 text-gray-600 hover:text-black"
            >
              <IoClose size={22} />
            </button>

            {/* Header with Icon */}
            <div className="flex items-center gap-2  p-4">
              <IoIosNotifications size={22} className="text-[#333]" />
              <h2 className="text-[20px] text-[black] font-semibold">NOTIFICATION</h2>
            </div>

            {/* Notification List */}
            {notifications.map((item, i) => (
              <div
                key={i}
                className={`flex items-start justify-between gap-2 mb-1 p-4 font-poppins text-sm ${
                  item.unread ? 'bg-[#E5E5E5]' : ''
                }`}
              >
                <div className="flex items-start gap-1">
                <div className="w-[10px] h-[10px] bg-[#333] rounded-full flex-shrink-0 self-start mt-1.5 mr-2" />
                  <p className="text-[black] text-[16px] font-[400]">{item.text}</p>
                </div>
                <span className="text-[14px] text-[#565656] whitespace-nowrap mt-1">
                  {item.time}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Wallet Modal */}
      {showWallet && (
  <div className="fixed inset-0 bg-[#0000004f] z-50 flex justify-center items-center px-4">
    <div
      ref={walletRef}
      className="bg-white w-full max-w-md sm:max-w-[646px] max-h-[90vh] rounded-lg p-4 sm:p-6 shadow-lg relative"
    >
      {/* Close Button */}
      <button
        onClick={() => setShowWallet(false)}
        className="absolute top-4 right-4 text-gray-600 hover:text-black"
      >
        <IoClose size={22} />
      </button>

      {/* Wallet Header */}
      <div className="flex items-center gap-2 mb-4">
        <IoWallet size={24} className="text-black" />
        <h2 className="text-lg sm:text-xl font-bold">My Wallet</h2>
      </div>

      {/* Points Summary */}
      <div className="flex flex-col sm:flex-row justify-evenly items-center gap-4 sm:gap-0 p-4 mb-6">
        <div>
          <p className="text-sm sm:text-[16px] text-[#00A13B]">Included 3 free, used</p>
          <div className="border border-[#0A5224] px-4 py-2 rounded text-green-700 text-center">
            <p className="text-sm sm:text-[18px] font-light">Total points</p>
            <p className="text-base sm:text-[20px] font-[500]">7 Points</p>
          </div>
        </div>

        <div className="border border-[#4C1388] px-4 py-2 rounded text-purple-700 text-center mt-2 sm:mt-[22px]">
          <p className="text-sm sm:text-[18px] font-light">Remaining points</p>
          <p className="text-base sm:text-[20px] font-[500]">1 Points</p>
        </div>
      </div>

      <h3 className="text-base sm:text-lg font-semibold mb-2">How I Earned Extra Points:</h3>

      {/* Table Wrapper */}
      <div className="border rounded overflow-hidden">
        <table className="w-full text-sm text-left border-collapse">
          <thead className="bg-[#4B207A] text-white sticky top-0 z-10">
            <tr>
              <th className="p-2 font-semibold">No</th>
              <th className="p-2 font-semibold">Date</th>
              <th className="p-2 font-semibold">Activity</th>
              <th className="p-2 font-semibold">Points</th>
            </tr>
          </thead>
        </table>

        {/* Scrollable Body */}
        <div className="max-h-[250px] overflow-y-auto">
          <table className="w-full text-sm text-left border-collapse">
            <tbody>
              {[
                {
                  no: '01',
                  date: '07-Apr-2025',
                  activity: 'John joined using your referral link',
                  points: '+1',
                  success: true,
                },
                {
                  no: '02',
                  date: '07-Apr-2025',
                  activity: 'John joined using your referral link',
                  points: '+1',
                  success: true,
                },
                {
                  no: '03',
                  date: '07-Apr-2025',
                  activity: 'John joined using your referral link',
                  points: '+1',
                  success: true,
                },
                {
                  no: '04',
                  date: '07-Apr-2025',
                  activity: 'DK missed your referral link.',
                  points: '+1',
                  success: false,
                },
                {
                  no: '05',
                  date: '07-Apr-2025',
                  activity: 'DK missed your referral link.',
                  points: '+1',
                  success: false,
                },
                {
                  no: '06',
                  date: '07-Apr-2025',
                  activity: 'DK missed your referral link.',
                  points: '+1',
                  success: false,
                },
                {
                  no: '07',
                  date: '07-Apr-2025',
                  activity: 'DK missed your referral link.',
                  points: '+1',
                  success: false,
                },
              ].map((entry, i) => (
                <tr
                  key={i}
                  className={entry.success ? 'text-black' : 'text-red-600 font-semibold'}
                >
                  <td className="p-2">{entry.no}</td>
                  <td className="p-2">{entry.date}</td>
                  <td className="p-2">{entry.activity}</td>
                  <td className="p-2 flex items-center gap-1">
                    {entry.points}
                    <Image
                      src={
                        entry.success
                          ? '/assets/images/tablecheck.png'
                          : '/assets/images/timmer.png'
                      }
                      alt="status"
                      width={20}
                      height={20}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
)}


    </>
  );
}
