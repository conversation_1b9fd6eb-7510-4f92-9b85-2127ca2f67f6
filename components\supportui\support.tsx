'use client';

import HelpCenter from '@/components/supportui/helpCenter';
import MyIssues from '@/components/supportui/myIssues';
import SendSuccessful from '@/components/supportui/send';
import Sidebar from '@/components/supportui/sidebar';
import { useState } from 'react';
import SupportTopBar from '@/components/supportui/supportTopBar';
import Link from "next/link";
import { FaAngleLeft} from "react-icons/fa";
const Tabcontent: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'help' | 'issues'>('help');
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <>
    <Link href="/" className="text-black hover:text-gray-800 text-[18px] font-normal flex gap-2 items-center px-4 pt-[80px]">
        <FaAngleLeft /> Back to Home Page
      </Link>
      {/* Only show search bar when activeTab is 'help' */}
      {activeTab === 'help' && <SupportTopBar onSearch={setSearchQuery} />}

      <div className="flex flex-col md:flex-row pt-[30px] py-8">
        <div className="w-full md:w-60 md:block flex flex-row md:flex-col gap-2 mb-4 md:mb-0">
          <Sidebar onSelect={setActiveTab} />

        </div>
        

        <div className="flex-1 md:px-[50px]">
          {searchQuery && activeTab === 'help' ? (
            <SendSuccessful />
          ) : activeTab === 'help' ? (
            <HelpCenter />
          ) : (
            <MyIssues />
          )}
        </div>
      </div>
    </>
  );
};

export default Tabcontent;
