import React from "react";
import Image from "next/image";
import ProfileDropdown from "@/components/sidebar-dropdown/dropdown";
import Link from "next/link";

// Define the props type
interface DashboardHeaderProps {
  isMobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  isMobileMenuOpen,
  toggleMobileMenu,
}) => {
  return (
    <header className="border-b border-[#868686] flex items-center justify-between bg-white fixed top-0 w-full z-[99] px-[10px] lg:px-[40px] h-[60px]">
      <div className="flex items-center gap-2">
        {/* Mobile Menu Toggle (only visible on mobile) */}
        <button
          className="md:hidden text-2xl text-purple-800"
          onClick={toggleMobileMenu}
        >
          {isMobileMenuOpen ? "✕" : "☰"}
        </button>

        {/* Logo */}
        <Link href="/">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={395}
            height={128}
            className="w-[110px] sm:w-[110px] lg:w-[130px] cursor-pointer"
          />
        </Link>
      </div>

      <div className="flex items-center justify-center cursor-pointer">
        <ProfileDropdown />
      </div>
    </header>
  );
};

export default DashboardHeader;
