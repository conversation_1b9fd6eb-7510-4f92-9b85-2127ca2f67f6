import { FaQuote<PERSON>eft, FaQuoteRight } from "react-icons/fa";

const QuoteSection: React.FC = () => {
  return (
    <div className="bg-[linear-gradient(90.3deg,#230A4D_-0.76%,#AEE08D_120.48%)] text-white py-8 px-4 text-center font-poppins">
      <div className="max-w-7xl mx-auto flex items-center text-center justify-center gap-0">
        <FaQuoteLeft className="text-[50px] text-white flex-none basis-[20px] md:basis-[30px]" />
        <p className="text-white text-lg sm:text-xl md:text-2xl  leading-relaxed px-4 md:px-10 font-semibold">
          Unlock limitless learning possibilities with
          ApexIQ. Gain in-demand skills,
          hands-on experience, and expert guidance to accelerate your career in
          AI and beyond.
        </p>
        <FaQuoteRight className="text-[50px] text-white flex-none basis-[20px]  md:basis-[30px]" />
      </div>
    </div>
  );
};

export default QuoteSection;
