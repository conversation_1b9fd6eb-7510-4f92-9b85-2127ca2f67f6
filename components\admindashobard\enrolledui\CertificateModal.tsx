"use client";

import { IoMdClose } from "react-icons/io";
import Image from "next/image";

interface CertificateModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CertificateModal({ isOpen, onClose }: CertificateModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 px-4">
      <div className="relative w-full max-w-[496px] bg-white rounded-xl shadow-lg p-6">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-black hover:text-gray-700"
        >
          <IoMdClose size={24} />
        </button>

        <div className="flex flex-col gap-4">
          {/* Profile */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <Image src="/assets/images/certificatemodaluser.png" alt="Avatar" width={40} height={40} />
            </div>
            <h2 className="text-lg font-semibold text-black">Ad<PERSON> Sharma</h2>
          </div>

          {/* Certificate Heading */}
          <div className="flex items-center gap-3">
            <div className="w-6 h-6">
              <Image src="/assets/images/certificateicon.png" alt="Certificate" width={24} height={24} />
            </div>
            <h3 className="text-lg font-semibold text-black">Certificates Summary</h3>
          </div>

          {/* Earned */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-green-600"></span>
              <span className="font-semibold text-black">Earned</span>
            </div>
            <ul className="pl-6 text-gray-800 text-[15px] list-disc">
              <li>Python (10/02/2025)</li>
              <li>MongoDB (22/03/2025)</li>
            </ul>
          </div>

          {/* In Progress */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-yellow-400"></span>
              <span className="font-semibold text-black">In Progress</span>
            </div>
            <ul className="pl-6 text-gray-800 text-[15px] list-disc">
              <li>Data Science (75%)</li>
            </ul>
          </div>

          {/* Upcoming */}
          <div>
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-blue-500"></span>
              <span className="font-semibold text-black">Upcoming</span>
            </div>
            <ul className="pl-6 text-gray-800 text-[15px] list-disc">
              <li>GitHub (2 Projects + Final Quiz)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
