'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from "framer-motion";
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Autoplay } from 'swiper/modules';


const images: string[] = [
  '/assets/images/Component4.png',
  '/assets/images/student2.png',
  '/assets/images/student3.png',
  '/assets/images/student4.png',
];

const HeroSection: React.FC = () => {
  const [textIndex, setTextIndex] = useState<number>(0);
  const words: string[] = ["possibilities.", "opportunities."]; 

  useEffect(() => {
    const textInterval = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % words.length);
    }, 2000);
    return () => clearInterval(textInterval);
  }, [words.length]);


  return (
    <section className="relative bg-[#d2d2d2] shadow-lg  overflow-hidden">
      <div className="">
      <div className=" grid grid-cols-1 lg:grid-cols-2 items-center md:pl-[100px] ">
        {/* Left Column - Text Content */}
        <div className="text-left xl:w-[783px] p-[10px] md:p-0">
          <h1 className="font-extrabold md:text-[60px] text-[30px] leading-[110%] tracking-[-0.04em] font-karla mb-6">
            Learning unlocks doors to  
            <AnimatePresence mode="wait">
              <motion.span
                key={textIndex}
                initial={{ opacity: 0, y: 10 }} // Start slightly below
                animate={{ opacity: 1, y: 0 }} // Move to position
                exit={{ opacity: 0, y: -10 }} // Move up and fade out
                transition={{ duration: 0.5 }}
                className="bg-gradient-to-r from-[#4B207A] to-[#0A5224] bg-clip-text text-transparent inline-block"
              >
                {words[textIndex]}
              </motion.span>
            </AnimatePresence>
          </h1>

          <p className="font-extrabold text-[18px] leading-[150%] tracking-[-0.04em] font-karla ">
            Elevate your learning journey with{" "}
            <span className="bg-gradient-to-r from-[#4B207A] to-[#0A5224] bg-clip-text text-transparent font-extrabold">
              ApexIQ
            </span>
            , where innovative tools, personalized learning, and expert guidance help you achieve limitless success.
          </p>
<div className='md:mt-[50px] mt-[30px]'>
<Link
  href="/signup"
  className="font-poppins mr-2 sm:mr-4 md:mr-[60px] xl:mr-[100px] px-4 sm:px-6 py-2 lg:py-2 bg-[#1b57c0] text-[16px] sm:text-[18px] md:text-[22px] text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer"
>
  Sign Up
</Link>
</div>
        
        </div>

        {/* Right Column - Image Slider with Background */}
        <div className="relative flex justify-center items-center w-full h-full">
          <div className="absolute inset-0 bg-contain bg-no-repeat bg-right-bottom   lg:bg-right-top bottom-[-7px]" style={{ backgroundImage: `url('/assets/images/bgdesign.png')` }}></div>
          <Swiper
            modules={[Autoplay]}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            slidesPerView={1}
            loop={true}
            className="w-[80%] max-w-[440px] relative"
          >
            {images.map((src, index) => (
              <SwiperSlide key={index}>
                <div className="relative md:h-[495px] h-[350px]  ml-20">
                <Image
                  src={src}
                  alt="Slider Image"
                  layout="fill"
                  className="w-full h-full"
                
                />
                </div>
             
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
      </div>
      
    </section>
  );
};

export default HeroSection;
