'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

const SendSuccessful = () => {
  return (
    <div className="flex flex-col items-center min-h-screen text-center p-10">
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-[#0A5224] text-[26px] font-medium flex items-center gap-2"
      >
        <Image
          src="/assets/images/check.png"
          alt="Success Checkmark"
          width={28}
          height={28}
        />
        <span>Send Successfully</span>
      </motion.div>
      <p className="text-[#00A13B] text-[16px] font-medium mt-2">
        We will connect you back with the response
      </p>
    </div>
  );
};

export default SendSuccessful;
