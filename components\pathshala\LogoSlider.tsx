"use client";

import React from 'react';
import Slider from 'react-slick';
import Image from 'next/image';
import { tools } from '@/utils/constant';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const LogoSlider: React.FC = () => {
  const settings = {
    infinite: true,
    speed: 500, // Animation speed
    slidesToShow: 8,
    slidesToScroll: 1, // Slide one logo at a time
    autoplay: true,
    autoplaySpeed: 2000, // Delay between slides
    cssEase: "ease", // Standard easing
    arrows: false,
    dots: false,
    pauseOnHover: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };

  return (
    <section className="md:pt-[50px] pt-[30px] mt-8 overflow-hidden">
      <div className="container mx-auto px-4">
        <h2 className="lg:text-[36px] sm:text-2xl font-semibold text-black font-poppins text-center">
          Tools and Technologies
        </h2>

        <div className="py-[60px] overflow-hidden">
          <Slider {...settings}>
            {tools.map((tool, index) => (
              <div key={index} className="text-center">
                <Image
                  src={tool.src}
                  alt={tool.alt}
                  width={50}
                  height={50}
                  className="w-12 h-12 object-contain mx-auto"
                />
              </div>
            ))}
          </Slider>
        </div>

        <hr className="w-full max-w-[954px] mx-auto border-t-1 border-black" />
      </div>
    </section>
  );
};

export default LogoSlider;
