'use client';

import { testimonials } from "@/utils/constant";
import { useState } from "react";
import { ImQuotesLeft, ImQuotesRight } from "react-icons/im";




const Testimonials: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  return (
    <div className="w-full bg-white text-center">
      <div className="sm:mt-6 bg-[#4B207A] py-12 px-4 sm:py-16 sm:px-6 relative">
        <ImQuotesLeft className="absolute top-5 left-4 sm:top-10 sm:left-40 text-white text-2xl sm:text-8xl" />
        <div className="max-w-xl mx-6 sm:mx-auto text-white p-4 sm:p-6 rounded-xl border-2 border-white shadow-lg relative">
          <p className="text-[10px] sm:text-xl lg:mx-10 lg:px-5 lg:mt-5 text-start font-poppins font-bold">
            {testimonials[activeIndex].text}
          </p>
          <p className="mt-2 sm:mt-4 text-[9px] sm:text-lg lg:mx-10 lg:px-5 lg:mb-5 text-start font-normal">
            {testimonials[activeIndex].author}
          </p>
        </div>
        <ImQuotesRight className="absolute bottom-8 right-4 sm:bottom-10 sm:right-40 text-white text-2xl sm:text-8xl" />

        <div className="mt-6 flex justify-center space-x-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveIndex(index)}
              className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${
                activeIndex === index ? "bg-white" : "bg-gray-400"
              } transition-all`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Testimonials;
