import React from "react";

const AuthorCard = () => {
  return (
    <div className="max-w-4xl mx-auto mt-6 md:mt-10 px-4 py-6 md:py-10 font-poppins">
      <div
        className="flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-6 bg-purple-50 p-4 md:p-6 rounded-lg"
        style={{ boxShadow: '0px 4px 4px 0px #00000040' }}
      >
        <img
          src="/assets/about/profile.jpg"
          alt="Olivia Rhye"
          className="w-36 h-36 md:w-24 md:h-24 rounded-full object-cover flex-shrink-0 mt-4"
        />
        <div className="text-center md:text-left">
          <h3 className="font-semibold text-black text-lg md:text-[24px] mb-2">Written by <PERSON></h3>
          <p className="text-black text-sm md:text-[18px] leading-relaxed">
            I share my thoughts, experiences, and projects on technology, design, and everyday inspiration. Whether you're a fellow developer, a design enthusiast, or just curious, you'll find something to spark your interest.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthorCard;
