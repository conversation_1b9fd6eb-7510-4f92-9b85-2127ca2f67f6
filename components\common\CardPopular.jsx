// CardPopular.jsx
import React from 'react';
import Link from 'next/link';

const CardPopular = ({ icon, title, href }) => {
  return (
    <Link href={href}>
      <div
        className="bg-gray-200 text-center rounded-md p-3 flex items-center gap-5 
                   hover:bg-[linear-gradient(90deg,_#D5B5FF_0%,_#B9FFD3_68.1%)] 
                   transition-all duration-300"
      >
        <img src={icon} alt={title} className="w-8 h-8" />
        <h3 className="font-[500] sm:text-[24px] font-sans text-black">{title}</h3>
      </div>
    </Link>
  );
};

export default CardPopular;
