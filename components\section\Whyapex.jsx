import React from "react";
import { GoDotFill } from "react-icons/go"; // Importing React Icon

const Whyapex = () => {
  return (
    <section className=" px-6 pt-[30px] md:pt-[60px]">
    
      <div className="small-container text-center  border-b-2   border-gray-500 flex justify-center items-center flex-col">
      <div className="pb-[30px] md:pb-[60px]">
      <h2 className="text-[22px] md:text-[36px] font-semibold text-black mb-6">Why ApexIQ?</h2>
        <div className="space-y-4 text-left">
          {[
            "ApexIQ training aligns with current industry needs, focusing on relevant, in-demand skills.",
            "Our courses equip students with practical skills that directly meet employer expectations.",
            "This targeted training prepares students for successful careers in competitive fields."
          ].map((text, index) => (
            <div key={index} className="flex items-start space-x-3">
            <GoDotFill className="text-black w-4 h-4 mt-1"/>
            
              <p className="text-black text-[20] font-poppins font-[400] mb-0">{text}</p>
            </div>
          ))}
        </div>
        <div className="mt-10">
          <button className="bg-[#4B207A] text-white px-6 py-2 rounded-md text-lg font-medium hover:bg-purple-800">
            More
          </button>
        </div>
      </div>
       
        
      </div>
    
      
    </section>
  );
};

export default Whyapex;
