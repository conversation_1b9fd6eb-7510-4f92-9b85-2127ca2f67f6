"use client"

import Image from "next/image"
import ProgressCircle from "@/components/dashboardUI/my-course-main/progress-circle"

export interface Course {
  id: string
  name: string
  image: string
  startDate: string
  startTime: string
  progress: number
  status: "In progress" | "Completed"
}

export const courses: Course[] = [
  {
    id: "python",
    name: "Python",
    image: "/assets/images/Untitleddesign(1).png",
    startDate: "01-Apr-2023",
    startTime: "08:00AM",
    progress: 50,
    status: "In progress",
  },
  {
    id: "github",
    name: "GitH<PERSON>",
    image: "/assets/images/Untitleddesign(18).png",
    startDate: "01-Apr-2023",
    startTime: "10:00AM",
    progress: 33,
    status: "In progress",
  },
  {
    id: "machine-learning",
    name: "Machine Learning",
    image: "/assets/images/Untitleddesign(14).png",
    startDate: "01-Apr-2023",
    startTime: "08:00AM",
    progress: 20,
    status: "In progress",
  },
  {
    id: "computer-vision",
    name: "Computer Vision",
    image: "/assets/images/Untitleddesign(3).png",
    startDate: "01-Apr-2023",
    startTime: "08:00AM",
    progress: 100,
    status: "Completed",
  },
  {
    id: "mysql",
    name: "MySQL",
    image: "/assets/images/Untitleddesign(2).png",
    startDate: "01-Apr-2023",
    startTime: "10:00AM",
    progress: 96,
    status: "In progress",
  },
  {
    id: "statistics",
    name: "Statistics",
    image: "/assets/images/Untitleddesign(15).png",
    startDate: "01-Apr-2023",
    startTime: "03:00PM",
    progress: 87,
    status: "In progress",
  },
]

interface CourseCardProps {
  course: Course
}

export default function CourseCard({ course }: CourseCardProps) {
  return (
    <div className="bg-[#e5e5e5]">
    <div className="p-4 flex flex-col md:flex-row items-center md:items-start justify-between space-y-4 md:space-y-0 md:space-x-4">
      {/* Top section (Image and Progress) */}
      <div className="flex w-full justify-between md:justify-start md:w-auto items-center">
        <div className="relative">
          <Image
            src={course.image}
            alt={course.name}
            width={82}
            height={82}
            className="object-cover w-[98px] h-[98px]"
          />
        </div>
        <div className="md:hidden">
          <ProgressCircle progress={course.progress} />
        </div>
      </div>

      {/* Bottom section (Text and Progress) */}
      <div className="flex flex-col md:flex-row md:items-center justify-between w-full">
        <div className="mt-2 md:mt-0">
          <h3 className="font-poppins font-semibold text-[16px] md:text-[20px] leading-[22px] tracking-[-0.02em] text-start text-black">
            {course.name}
          </h3>
          <div className="font-poppins font-normal text-[13px] leading-[22px] tracking-[-0.02em] text-start text-black mt-1">
            <div>Start Date: {course.startDate}</div>
            <div>Start Time: {course.startTime}</div>
          </div>
        </div>

        {/* Progress section for desktop */}
        <div className="hidden md:flex md:items-center md:space-x-4 ml-auto">
          <div className="flex flex-col items-center text-sm text-black">
            <div>{course.status}</div>
            <button className="h-10 w-10 bg-gray-200 rounded-full items-center justify-center flex mt-1">
              <Image
                src="/assets/images/playdashboard.png"
                alt="Profile"
                width={30}
                height={30}
                className="rounded-full object-cover"
              />
            </button>
          </div>
          <ProgressCircle progress={course.progress} />
        </div>
      </div>
    </div>
  </div>
  )
}
