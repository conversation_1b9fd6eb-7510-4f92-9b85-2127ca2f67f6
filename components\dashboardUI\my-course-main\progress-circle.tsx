"use client";

interface ProgressCircleProps {
  progress: number;
}

export default function ProgressCircle({ progress }: ProgressCircleProps) {
  const radius = 36;
  const stroke = 6;
  const normalizedRadius = radius - stroke / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative w-20 h-20">
      <svg height="80" width="80" className="transform -rotate-90">
        {/* Background Circle */}
        <circle
          stroke="rgba(76, 76, 76, 0.2)" // #4C4C4C with 20% opacity
          fill="transparent"
          strokeWidth={stroke}
          r={normalizedRadius}
          cx="40"
          cy="40"
        />
        {/* Progress Circle */}
        <circle
          stroke="#4B207A"
          fill="transparent"
          strokeWidth={stroke}
          strokeLinecap="round"
          strokeDasharray={`${circumference} ${circumference}`}
          style={{ strokeDashoffset, transition: "stroke-dashoffset 0.6s ease" }}
          r={normalizedRadius}
          cx="40"
          cy="40"
        />
      </svg>

      {/* Center Percentage Text */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="font-figtree font-bold text-[16px] text-[#4B207A]">
          {progress}%
        </span>
      </div>
    </div>
  );
}
