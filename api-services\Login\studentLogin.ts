// api-service/login.ts
import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils";
import Cookies from 'js-cookie'

// ================== LOGIN INTERFACES ==================
interface StudentLoginPayload extends Record<string, unknown> {
  username: string | number;
  password: string;
}

interface StudentLoginResponse {
  status: string;
  message: string;
  user_id: number;
  email: string;
  mobile_number: string;
}

// ================== LOGIN API ==================
async function studentLogin(payload: StudentLoginPayload): Promise<StudentLoginResponse> {
  const requestData: Record<string, unknown> = {
    username: payload.username,
    password: payload.password
  };

  const response = await makeRequest({
    endpoint: "/accounts/login/student/",
    method: "POST",
    data: requestData,
  });

  // Extract tokens from cookies
  const accessToken = Cookies.get('access');
  const refreshToken = Cookies.get('refresh_token');

  console.log("hiiiii:",accessToken);
  

  // Store tokens in localStorage if they exist
  if (accessToken) {
    localStorage.setItem("token", accessToken);
  }
  if (refreshToken) {
    localStorage.setItem("refresh_token", refreshToken);
  }

  return response;
}

// ================== REACT QUERY HOOK ==================
const useStudentLogin = () => {
  return useMutation<StudentLoginResponse, Error, StudentLoginPayload>({
    mutationFn: studentLogin,
    onError: (error) => {
      console.error("Login error:", error);
    },
  });
};

export { useStudentLogin };