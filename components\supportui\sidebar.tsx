'use client';

import { useState } from 'react';

interface SidebarProps {
  onSelect: (tab: 'help' | 'issues') => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onSelect }) => {
  const [activeTab, setActiveTab] = useState<'help' | 'issues'>('help');

  const handleTabClick = (tab: 'help' | 'issues') => {
    setActiveTab(tab);
    onSelect(tab);
  };

  return (
    <div className="w-full flex flex-row md:flex-col gap-2 md:gap-0 items-center md:w-60 px-2 md:px-0 ">
      <button
        className={`w-auto md:w-full p-2 md:p-3 rounded-[5px] text-[12px] md:text-[20px]  leading-[27px] font-poppins font-medium text-center cursor-pointer ${
          activeTab === 'help' ? 'bg-[#CEBAE8] text-[#3F0D75]' : 'border-[2px] border-[#CEBAE8]'
        }`}
        onClick={() => handleTabClick('help')}
      >
        1 Help Center
      </button>
      <button
        className={`w-auto md:w-full text-center p-2 md:p-3 text-[12px] md:text-[20px]  leading-[27px] font-poppins rounded-[5px] font-medium  cursor-pointer ${
          activeTab === 'issues' ? 'bg-[#CEBAE8] text-[#3F0D75]' : 'border-[2px] border-[#CEBAE8]'
        }`}
        onClick={() => handleTabClick('issues')}
      >
       2 My Issues
      </button>
    </div>
  );
};

export default Sidebar;
