"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { HiMenu, HiX } from "react-icons/hi";
import { usePathname } from "next/navigation";

const HeaderPath: React.FC = () => {
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen((prev) => !prev);

  const menuItems = [
    { name: "About", href: "/about" },
    { name: "Courses", href: "/mycourse" },
    { name: "Blogs", href: "/blogs" },
    { name: "Talk to PDF", href: "/" },
    { name: "Log in", href: "/login" },
  ];

  return (
    <nav className="bg-white shadow-sm border-b-1 border-[#868686] py-[6px]">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        {/* Logo */}
        <Link href="/">
          <Image
            src="/assets/images/logo.png"
            alt="Logo"
            width={142}
            height={62}
            className="w-[142px]"
          />
        </Link>

        {/* Desktop Menu */}
        <div className="hidden lg:flex items-center space-x-10 text-black text-[20px] font-[400]">
          {menuItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className="relative group text-black"
              >
                <span
                  className={`transition-colors duration-300 ${
                    isActive ? "text-[#4B207A]" : "group-hover:text-[#4B207A]"
                  }`}
                >
                  {item.name}
                </span>
                <span
                 className={`absolute bottom-0 h-0.5 bg-[#4B207A] transition-all duration-300 ${
                  isActive
                    ? "w-full left-0"
                    : "w-0 left-1/2 group-hover:w-full group-hover:left-0"
                }`}
                ></span>
              </Link>
            );
          })}
          <Link
            href="/signup"
            className="bg-[#1B57C0] text-white px-4 py-[6px] rounded-md hover:bg-blue-800"
          >
            Sign Up
          </Link>
        </div>

        {/* Mobile Menu Toggle */}
        <div className="lg:hidden">
          <button
            onClick={toggleMenu}
            className="text-gray-700 focus:outline-none"
          >
            {isMenuOpen ? <HiX className="h-6 w-6" /> : <HiMenu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden px-4 pb-4 space-y-2">
          {menuItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`block text-base ${
                  isActive ? "text-[#4B207A]" : "text-black hover:text-purple-600"
                }`}
              >
                {item.name}
              </Link>
            );
          })}
          <Link
            href="/signup"
            className="inline-block bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-800"
          >
            Sign Up
          </Link>
        </div>
      )}
    </nav>
  );
};

export default HeaderPath;
