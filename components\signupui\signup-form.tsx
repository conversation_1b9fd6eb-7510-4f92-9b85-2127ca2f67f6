"use client"

import { useState } from "react"
import { <PERSON>, EyeOff, X } from "lucide-react"
import PhoneInput from "react-phone-input-2"
import "react-phone-input-2/lib/style.css"
import { useRouter } from "next/navigation"
import { useStudentRegister } from "../../api-services/Registration/register"
import { Toast } from "../common/Toast"

export default function SignupForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showToast, setShowToast] = useState(false)
  const [phone, setPhone] = useState("")
  const [errors, setErrors] = useState<any>({})

  const [form, setForm] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  })
  const router = useRouter()
  const { mutate: registerStudent, status } = useStudentRegister()
  const isLoading = status === "pending"

  function handleClose(event: React.MouseEvent<HTMLButtonElement>): void {
    event.preventDefault()
    window.history.back()
  }

  function validateField(id: string, value: string) {
    let message = ""

    if (id === "name" && !value.trim()) {
      message = "Name is required"
    }

    if (id === "email") {
      if (!value.trim()) {
        message = "Email is required"
      } else if (!/\S+@\S+\.\S+/.test(value)) {
        message = "Invalid email address"
      }
    }

    if (id === "password") {
      if (!value) {
        message = "Password is required"
      } else if (value.length < 8) {
        message = "Password must be at least 8 characters"
      }
    }

    if (id === "confirmPassword") {
      if (!value) {
        message = "Confirm password is required"
      } else if (value !== form.password) {
        message = "Passwords do not match"
      }
    }

    setErrors((prev: any) => ({ ...prev, [id]: message }))
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { id, value } = e.target
    setForm((prev) => ({ ...prev, [id]: value }))
    validateField(id, value)
  }

  function validatePhone() {
    const digitsOnly = phone.replace(/\D/g, "")
    const localNumber = digitsOnly.slice(-10)

    if (localNumber.length !== 10) {
      setErrors((prev: any) => ({
        ...prev,
        phone: "Please enter a valid 10-digit mobile number",
      }))
      return false
    } else {
      setErrors((prev: any) => ({ ...prev, phone: "" }))
      return true
    }
  }

  function handleSubmit(event: React.FormEvent<HTMLFormElement>): void {
    event.preventDefault()
    const isPhoneValid = validatePhone()

    Object.entries(form).forEach(([key, value]) => validateField(key, value))

    const hasErrors = Object.values(errors).some((msg) => msg)
    if (hasErrors || !isPhoneValid) return

    const localPhone = phone.replace(/\D/g, "").slice(-10)

    registerStudent(
      {
        email: form.email,
        name: form.name,
        mobile_number: localPhone,
        password: form.password,
        confirm_password: form.confirmPassword,
      },
      {
        onSuccess: () => {
          setShowToast(true)
          setTimeout(() => {
            router.push("/otp")
          }, 1500)
        },
      }
    )
  }

  return (
    <>
      {showToast && <Toast message="OTP sent successfully" />}
      <div className="w-full bg-[#EBEBEB] p-[20px] lg:p-[70px] rounded-lg shadow-md relative">
        <button
          className="absolute top-3 right-3 md:top-4 md:right-4 text-black font-bold text-xl"
          onClick={handleClose}
        >
          <X size={24} />
        </button>

        <h1 className="text-[20px] lg:text-[32px] font-bold font-poppins text-purple-800 mb-[40px]">
          Signup for <span className="text-purple-900">ApexIQ</span>
          <span className="text-[20px] font-[600] text-[#0a5224] ml-2">EduTech</span>
        </h1>

        <form className="space-y-6" onSubmit={handleSubmit}>
          {/* Username */}
          <div className="relative w-full">
            <input
              type="text"
              id="name"
              value={form.name}
              onChange={handleChange}
              placeholder=" "
              className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
            />
            <label
              htmlFor="name"
              className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
            >
              User Name
            </label>
            {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
          </div>

          {/* Email */}
          <div className="relative w-full">
            <input
              type="email"
              id="email"
              value={form.email}
              onChange={handleChange}
              placeholder=" "
              className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
            />
            <label
              htmlFor="email"
              className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
            >
              Email
            </label>
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
          </div>

          {/* Phone Input */}
          <div className="w-full border border-black rounded-md">
            <PhoneInput
              country={"us"}
              value={phone}
              onChange={(value) => {
                setPhone(value)
                validatePhone()
              }}
              inputStyle={{
                backgroundColor: "transparent",
                width: "100%",
                border: "none",
                fontSize: "1rem",
              }}
              containerStyle={{ backgroundColor: "transparent", width: "100%" }}
              buttonStyle={{ backgroundColor: "transparent" }}
              placeholder="Phone Number"
            />
          </div>
          {errors.phone && <p className="text-red-600 text-sm mt-1">{errors.phone}</p>}

          {/* Password */}
          <div className="relative w-full">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              value={form.password}
              onChange={handleChange}
              placeholder=" "
              className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
            />
            <label
              htmlFor="password"
              className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
            >
              Password
            </label>
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-black"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
          </div>

          {/* Confirm Password */}
          <div className="relative w-full">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              value={form.confirmPassword}
              onChange={handleChange}
              placeholder=" "
              className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
            />
            <label
              htmlFor="confirmPassword"
              className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all 
                peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
            >
              Confirm Password
            </label>
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-black"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
            {errors.confirmPassword && (
              <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full py-2 px-4 bg-[#063585] text-white font-medium rounded-md transition-colors font-poppins cursor-pointer"
            disabled={isLoading}
          >
            {isLoading ? "Registering..." : "Register"}
          </button>
        </form>
      </div>
    </>
  )
}
