'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from "framer-motion";
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Autoplay } from 'swiper/modules';

const images = [
  '/assets/images/main-student.png',
  '/assets/images/main-student2.png',
  '/assets/images/main-student3.png',
  '/assets/images/main-student4.png',
];

const Hero = () => {
  const [textIndex, setTextIndex] = useState(0);
  const words = ["possibilities.", "opportunities."];

  useEffect(() => {
    const textInterval = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % words.length);
    }, 2000);
    return () => clearInterval(textInterval);
  }, []);

  return (
    <section className="relative bg-[#d2d2d2] shadow-lg h-auto min-h-[519px] overflow-hidden">
      <div className=" grid grid-cols-1 lg:grid-cols-2 items-center  px-3 pt-10  lg:pt-0 lg:pr-0  lg:pl-[50px] xl:pl-[100px]">
        {/* Left Column - Text Content */}
        <div className="text-left">
          <h1 className="2xl:text-[60px] xl:text-[45px] font-[800] text-[24px] mb-4 leading-[38px] lg:leading-[69px]">
            Transform Your Career with <span className="bg-gradient-to-r from-[#4B207A] to-[#0A5224] bg-clip-text text-transparent inline-block"> ApexIQ </span>
          </h1>

          <p className="text-[16px] font-karla font-[600] lg:my-3 sm:text-lg text-black lg:mt-8 mb-8 max-w-2xl">
          Master in-demand skills through expert-led training in AI, data science, cloud computing, and more. Empower your workforce and stay ahead in the digital age.
          </p>
          <h4 className='mb-8 text-[20px] font-[700]'>Get Started Learning Today!</h4>

          <button className="bg-[#5C3C7F] text-white px-8 py-2 rounded-md text-[18px] font-[500] font-poppins transition-colors">
            Sign Up
          </button>
        </div>

        {/* Right Column - Image Slider with Background */}
        <div className="relative flex justify-center items-center w-full h-full">
          <div className="absolute inset-0 bg-contain bg-no-repeat bg-right-bottom bottom-0 lg:bg-right-top" style={{ backgroundImage: `url('/assets/images/bgdesign.png')` }}></div>
          <Swiper
            modules={[Autoplay]}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            slidesPerView={1}
            loop={true}
            className="w-[80%] max-w-[440px] relative"
          >
            {images.map((src, index) => (
              <SwiperSlide key={index}>
                <div className="relative h-[495px] ml-20">
                  <Image
                    src={src}
                    alt="Slider Image"
                    layout="fill"
                    //objectFit="cover"
                    className="w-full h-full"
                  />
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </section>
  );
};

export default Hero;
