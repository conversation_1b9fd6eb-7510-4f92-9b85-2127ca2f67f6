"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import InputField from "@/components/common/adminCommon/InputField";
import "react-phone-input-2/lib/style.css";

const ResetForm: React.FC = () => {
  const [formData, setFormData] = useState({ email: "" });
  const [isFormValid, setIsFormValid] = useState(false);

  const emailRegex = React.useMemo(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/, []);

  useEffect(() => {
    const { email } = formData;
    setIsFormValid(emailRegex.test(email));
  }, [formData, emailRegex]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
  };

  return (
    <div className="w-full flex items-center justify-center bg-white h-[80vh] px-[10px]">
      <div className="w-full max-w-[788px] bg-[#eeeeee] rounded-md shadow-md p-6 lg:px-[70px] lg:py-[50px]">
        {/* Logo */}
        <div className="flex justify-center mb-4">
          <Image
            src="/assets/images/logobig.png"
            alt="logo"
            width={181}
            height={60}
            className="lg:w-[181px] w-[100px]"
          />
        </div>

        {/* Heading */}
        <h2 className="text-center text-[#4b207a] font-[Poppins] font-semibold text-[22px] md:text-[32px] leading-tight mb-[20px] lg:mb-[40px]">
          Forgot Password
        </h2>

        {/* Form */}
        <form
          onSubmit={handleSubmit}
          className="w-full flex flex-col items-center"
        >
          <div className="w-full  mb-[20px] lg:mb-[40px]">
            <InputField
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              label="Enter Email"
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!isFormValid}
            className={`w-full md:w-auto py-3 px-8 text-white text-[18px] md:text-[24px] font-bold font-[Poppins] rounded-lg transition-opacity ${
              isFormValid
                ? "bg-[#4b207a] opacity-100 cursor-pointer"
                : "bg-[#4b207a] opacity-50 cursor-not-allowed"
            }`}
          >
            Send Reset Code
          </button>
        </form>
      </div>
    </div>
  );
};

export default ResetForm;
