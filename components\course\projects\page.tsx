"use client"
import Image from "next/image"
import { useState } from "react"

interface Project {
  id: string
  title: string
  description: string
  pdfUrl: string
  projectLink: string
  isUploaded: boolean
}

export default function ProjectSubmissionForm() {
  const [projects, setProjects] = useState<Project[]>([
    {
      id: "resume-builder",
      title: "AI-Powered Resume Builder",
      description: "Uses input basic details and AI generates a professional resume using optimized prompts.",
      pdfUrl: "/project_1_description.pdf",
      projectLink: "",
      isUploaded: false,
    },
    {
      id: "study-assistant",
      title: "AI Study Assistant",
      description: "Users ask questions, and AI provides summarized answers using well-structured prompts.",
      pdfUrl: "/project_1_description.pdf",
      projectLink: "",
      isUploaded: false,
    },
  ])

  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleProjectLinkChange = (id: string, value: string) => {
    setProjects(projects.map((project) => (project.id === id ? { ...project, projectLink: value } : project)))
  }

  const handleUpload = (id: string) => {
    setProjects(projects.map((project) => (project.id === id ? { ...project, isUploaded: true } : project)))
  }

  const handleSubmit = () => {
    // Here you would typically send the data to your backend
    setIsSubmitted(true)
  }

  const hasAtLeastOneUpload = projects.some((project) => project.isUploaded)

  if (isSubmitted) {
    return (
      <div className="max-w-3xl w-full  space-y-5 relative min-h-[400px] ">
        <h1 className="font-medium text-xl sm:text-2xl md:text-[24px] leading-[100%] tracking-[-0.01em] font-[Poppins] text-[#000000]">Projects:</h1>

        <div className="flex flex-col items-center justify-center space-y-2 py-12">
          <div className="flex items-center text-green-600">
            <Image
              src="/assets/images/tick.png"
              alt="logo"
              width={24}
              height={24}
              className="w-[18px] sm:w-[20px] lg:w-[24px]"
            />
            <span className="font-medium text-lg sm:text-xl md:text-[26px] leading-[27px] tracking-[-0.019em] font-[Poppins] text-[#00a13b] ml-2">Send Successfully</span>
          </div>
          <p className="font-medium text-sm sm:text-base md:text-[16px] leading-[27px] tracking-[-0.019em] font-[Poppins] text-[#00a13b] text-center">Our team will review soon.</p>

          <div className="absolute bottom-0 right-0 md:right-[-50px] lg:right-[-100px] flex flex-col items-end pb-4">
  <button className="w-full sm:w-[200px] h-[45px] md:w-[305px] md:h-[64px] bg-green-600 text-white rounded-md font-semibold text-[16px] md:text-[20px] text-center font-poppins transition-colors duration-300 ease-in-out hover:bg-green-700 cursor-pointer">
    Go to Next Lesson
  </button>
  <p className="text-sm text-gray-600 mt-2 font-poppins">Next - Basics of prompt engineering</p>
</div>

        
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-3xl w-full  space-y-6 p-[10px]">
      <div className="space-y-2">
        <h1 className="font-medium text-xl sm:text-2xl md:text-[24px] leading-[100%] tracking-[-0.01em] font-[Poppins] text-[#000000]">Projects:</h1>
        
        <p className="font-normal text-sm sm:text-base md:text-[16px] leading-[130%] sm:leading-[100%] tracking-[-0.01em] font-[Poppins] text-[#e31317] mt-5">
          <strong>NOTE:</strong> Please upload both projects first. Once completed, you&apos;ll be able to submit for review.
        </p>
      </div>
      <p className="font-normal text-sm sm:text-base md:text-[16px] leading-[130%] sm:leading-[100%] tracking-[-0.01em] font-[Poppins] text-[#000000]">
        Instructions: Read each prompt carefully before responding.
      </p>

      <div className="space-y-8">
        {projects.map((project, index) => (
          <div key={project.id} className="space-y-3">
            <div className="flex items-baseline gap-2">
              <span className="font-medium">{index + 1}.</span>
              <h2 className="font-medium">{project.title}</h2>
            </div>
        
            <p className="text-sm ml-4 sm:ml-6 text-[#000000] font-[Poppins]">{project.description}</p>
       
            <div className="ml-2 sm:ml-6 border rounded-md p-3 bg-white flex items-center gap-3 w-full max-w-[361px] h-auto min-h-[72px]">
              <div className="rounded-md p-1 sm:p-2 flex items-center justify-center">
                <Image
                  src="/assets/images/pdf.png"
                  alt="logo"
                  width={40}
                  height={40}
                  className="w-[18px] sm:w-[30px] lg:w-[40px]"
                />
              </div>
              <div>
                <p className="text-xs sm:text-sm font-medium">project_1_description.pdf</p>
                <a href={project.pdfUrl} className="text-xs text-blue-600 hover:underline">
                  Download
                </a>
              </div>
            </div>
    
            <div className="ml-2 sm:ml-6 flex flex-col sm:flex-row gap-2">
              <div className="flex-1 flex items-center gap-2 border rounded-md px-2 sm:px-3 py-2 bg-white cursor-pointer">
                <Image
                  src="/assets/images/link.png"
                  alt="logo"
                  width={40}
                  height={40}
                  className="w-[18px] sm:w-[24px] lg:w-[34px] flex-shrink-0"
                />
                <input
                  value={project.projectLink}
                  onChange={(e) => handleProjectLinkChange(project.id, e.target.value)}
                  placeholder="Paste your Google Drive or GitHub project link here."
                  className="border-none focus-visible:ring-none outline-none flex-1 text-xs sm:text-sm text-[#000000] font-[Poppins] ml-2 sm:ml-4 w-[1000%]"
                />
              </div>
         
              {project.isUploaded ? (
                <button 
                  className="w-full sm:w-[150px] h-[45px] md:w-[186px] md:h-[54px] bg-[#687bfb] text-white rounded-md font-semibold text-base sm:text-lg md:text-[20px] leading-[100%] tracking-[0%] text-center font-poppins transition-colors cursor-pointer sm:ml-2 md:ml-9 mt-2 sm:mt-0"
                  disabled
                >
                  Uploaded
                </button>
              ) : (
                <button
                  className="w-full sm:w-[150px] h-[45px] md:w-[186px] md:h-[54px] bg-[#687bfb] text-white rounded-md font-semibold text-base sm:text-lg md:text-[20px] leading-[100%] tracking-[0%] text-center font-poppins transition-colors cursor-pointer sm:ml-2 md:ml-9 mt-2 sm:mt-0"
                  onClick={() => handleUpload(project.id)}
                  disabled={!project.projectLink}
                >
                  Upload
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-center sm:justify-end pt-4 pb-6">
        <button
          className={`${
            hasAtLeastOneUpload 
              ? "w-full sm:w-[150px] h-[45px] md:w-[186px] md:h-[54px] bg-[#8b4fcc] text-white rounded-md font-semibold text-base sm:text-lg md:text-[20px] leading-[100%] tracking-[0%] text-center font-poppins transition-colors cursor-pointer" 
              : "bg-purple-600 w-full sm:w-[150px] h-[45px] md:w-[186px] md:h-[54px] text-white rounded-md font-semibold text-base sm:text-lg md:text-[20px] leading-[100%] tracking-[0%] text-center font-poppins transition-colors cursor-pointer"
          } text-white px-4 sm:px-8 py-2 rounded-md`}
          disabled={!hasAtLeastOneUpload}
          onClick={handleSubmit}
          style={{ opacity: hasAtLeastOneUpload ? 1 : 0.3 }}
        >
          Submit
        </button>
      </div>
    </div>
  )
}