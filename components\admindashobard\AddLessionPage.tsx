"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import {
  FaTimes,
} from "react-icons/fa";
import { BsCheckCircleFill } from "react-icons/bs";
import { useCreateLesson } from "../../api-services/lessons";
import { useGetModuleById } from "../../api-services/modules/modules";
import { useCreateQuiz } from "../../api-services/quiz";

import toast from "react-hot-toast";

type UploadFile = {
  name: string;
  size: string;
  file?: File;
};

const AddLessionPage = () => {
  const [lessonTitle, setLessonTitle] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [videoLinks, setVideoLinks] = useState<string[]>([]);
  const [videoLinkInput, setVideoLinkInput] = useState("");
  const [notesFile, setNotesFile] = useState<UploadFile[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [pdfFiles, setPdfFiles] = useState<UploadFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedQuizFiles, setSelectedQuizFiles] = useState<File[]>([]);

  const notesInputRef = useRef<HTMLInputElement | null>(null);
  const quizInputRef = useRef<HTMLInputElement | null>(null);
  const editorRef = useRef<HTMLDivElement | null>(null);
  // const pdfInputRef = useRef<HTMLInputElement | null>(null);
  const [markdownInput, setMarkdownInput] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get courseId and moduleId from URL params
  const courseId = searchParams.get('courseId') || '';
  const moduleId = searchParams.get('moduleId') || 'f9551eae-11ba-4bee-80a8-a7255a6662f4';

  // API hooks
  const createLessonMutation = useCreateLesson();
  const createQuizMutation = useCreateQuiz();
  const { data: moduleResponse, isLoading: moduleLoading, error: moduleError } = useGetModuleById(moduleId);

  // Debug logging
  useEffect(() => {
    console.log('AddLessionPage Debug Info:');
    console.log('courseId:', courseId);
    console.log('moduleId:', moduleId);
    console.log('moduleLoading:', moduleLoading);
    console.log('moduleResponse:', moduleResponse);
    console.log('moduleError:', moduleError);
    if (moduleError) {
      console.error('Module fetch error details:', moduleError);
    }

    // Check authentication status
    const token = localStorage.getItem("token");
    const refreshToken = localStorage.getItem("refresh_token");
    console.log('Auth status:', {
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'No token',
      hasRefreshToken: !!refreshToken
    });
  }, [courseId, moduleId, moduleLoading, moduleResponse, moduleError]);

  const handleBack = () => router.back();
  const handleHome = () => router.push("/dashboard");

  // Ensure text direction is fixed on component mount
  useEffect(() => {
    if (editorRef.current) {
      fixTextDirection();
    }
  }, [projectDescription]);

  const handleMultipleFileSelect = (
    e: React.ChangeEvent<HTMLInputElement>,
    setter: React.Dispatch<React.SetStateAction<UploadFile[]>>
  ) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;
    const validFiles: UploadFile[] = files.map((file) => ({
      name: file.name,
      size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
      file: file, // Store the actual file object
    }));
    setter((prev) => [...prev, ...validFiles]);
  };

  const handleAddVideoLink = () => {
    if (videoLinkInput.trim()) {
      setVideoLinks([...videoLinks, videoLinkInput.trim()]);
      setVideoLinkInput("");
    }
  };

  const handleSaveSession = async () => {
    if (!lessonTitle.trim()) {
      toast.error('Lesson title is required');
      return;
    }

    if (videoLinks.length === 0) {
      toast.error('Please add at least one video URL');
      return;
    }

    if (notesFile.length === 0) {
      toast.error('Please upload at least one notes file');
      return;
    }

    setIsSubmitting(true);

    try {
      // Step 1: Create the lesson
      const lessonToastId = toast.loading('Creating lesson...');

      const lessonPayload = {
        title: lessonTitle.trim(),
        video_url: videoLinks[0], // Use the first video URL
        notes_md: notesFile.map(file => file.file).filter(Boolean) as File[],
        project_description: markdownInput.trim() || undefined,
        project_files: pdfFiles.map(file => file.file).filter(Boolean) as File[],
      };

      console.log('Creating lesson with payload:', lessonPayload);
      console.log('Module ID:', moduleId);

      const lessonResponse = await createLessonMutation.mutateAsync({
        moduleId,
        payload: lessonPayload,
      });

      const lessonId = lessonResponse?.data?.id;
      if (!lessonId) {
        throw new Error('Failed to get lesson ID from response');
      }

      console.log('Lesson created successfully with ID:', lessonId);
      toast.dismiss(lessonToastId);
      toast.success('Lesson created successfully!');

      // Step 2: Upload quiz files if any are selected
      if (selectedQuizFiles.length > 0) {
        // Check authentication before quiz upload
        const token = localStorage.getItem("token");
        if (!token) {
          toast.error("Authentication token not found. Please log in again.");
          return;
        }

        const quizToastId = toast.loading(`Uploading ${selectedQuizFiles.length} quiz file(s)...`);

        let successCount = 0;
        let errorCount = 0;

        for (const quizFile of selectedQuizFiles) {
          try {
            console.log(`Uploading quiz file: ${quizFile.name} to lesson: ${lessonId}`);

            await createQuizMutation.mutateAsync({
              lessonId,
              payload: { quiz_file: quizFile },
            });

            successCount++;
            console.log(`Successfully uploaded: ${quizFile.name}`);
          } catch (quizError) {
            console.error(`Failed to upload quiz file ${quizFile.name}:`, quizError);
            errorCount++;

            // Show specific error for each failed file
            if (quizError instanceof Error) {
              if (quizError.message.includes("permission") || quizError.message.includes("403") || quizError.message.includes("Server error occurred")) {
                toast.error(`Permission denied for ${quizFile.name}. Please check your authentication or try logging in again.`);
              } else if (quizError.message.includes("Invalid response format")) {
                toast.error(`Server error while uploading ${quizFile.name}. Please try again later.`);
              } else {
                toast.error(`Failed to upload ${quizFile.name}: ${quizError.message}`);
              }
            }
          }
        }

        // Show quiz upload summary
        toast.dismiss(quizToastId);
        if (successCount > 0) {
          toast.success(`${successCount} quiz file(s) uploaded successfully!`);
        }
        if (errorCount > 0) {
          console.log(`${errorCount} quiz file(s) failed to upload. See specific errors above.`);
        }
      } else {
        console.log('No quiz files selected, skipping quiz upload');
      }

      // Step 3: Show success and navigate back
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        toast.success('Lesson creation completed!');
        router.back(); // Navigate back after everything is done
      }, 2000);

    } catch (error) {
      console.error('Error in lesson creation process:', error);
      // Dismiss any loading toasts
      toast.dismiss();
      toast.error('Failed to create lesson. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Rich text editor functions
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    // Update state with new content and fix direction
    setTimeout(() => {
      if (editorRef.current) {
        setProjectDescription(editorRef.current.innerHTML);
        fixTextDirection();
      }
    }, 10);
  };

  const fixTextDirection = () => {
    if (editorRef.current) {
      const editor = editorRef.current;
      editor.style.direction = "ltr";
      editor.style.textAlign = "left";
      editor.style.unicodeBidi = "bidi-override";
      editor.style.writingMode = "horizontal-tb";

      // Fix direction for all child elements
      const allElements = editor.querySelectorAll("*");
      allElements.forEach((el: any) => {
        el.style.direction = "ltr";
        el.style.unicodeBidi = "bidi-override";
        el.style.textAlign = "left";
      });
    }
  };

  const handleFormatBlock = (tag: string) => {
    if (tag) {
      execCommand("formatBlock", `<${tag}>`);
    } else {
      execCommand("formatBlock", "<div>");
    }
  };



  return (
    <>
      <div className="min-h-screen bg-white p-[10px] lg:p-8 font-poppins text-[#000] relative">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center gap-2 border px-4 py-2 text-[15px] font-medium hover:bg-gray-100"
          >
            <Image
              src="/assets/images/back-arrow.png"
              alt="Back"
              width={15}
              height={15}
            />
            Back
          </button>
          <button
            onClick={handleHome}
            className="flex items-center justify-center border w-[31px] h-[31px]"
          >
            <Image
              src="/assets/images/home.png"
              alt="Home"
              width={20}
              height={20}
            />
          </button>
        </div>

        <div className="max-w-[800px] w-full mx-auto">
          <p className="lg:text-[18px] text-[14px] font-medium mb-8">
            {moduleLoading ? (
              <span className="animate-pulse bg-gray-200 h-6 w-64 rounded block"></span>
            ) : moduleResponse?.data?.title ? (
              `${moduleResponse.data.title}`
            ) : (
              "Module Title"
            )}
          </p>

          {/* Lesson Title */}
          <div className="mb-8 lg:flex block items-center  gap-4">
            <label className="text-[18px] mb-[10px] lg:mb-0 block font-medium lg:whitespace-nowrap">
              Lesson Title :
            </label>
            <input
              type="text"
              value={lessonTitle}
              onChange={(e) => setLessonTitle(e.target.value)}
              placeholder="Enter lesson title here"
              className="flex-1 border border-black rounded-md px-4 lg:py-4 py-2 text-[14px] lg:text-[16px] outline-none focus:border-black placeholder-black w-full"
            />
          </div>

          {/* Upload Video */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Video :
            </label>

          {/* Video URL Input */}
<div className="mb-3">
  <div className="flex items-center border border-[#3D3D3D] rounded-md px-4 lg:py-4 py-2">
    {/* Replace icon with an image */}
    <Image
      src="/assets/images/videoupload.png"
      alt="attachment icon"
      width={30}
      height={30}
      className="mr-3"
    />
    <input
      type="text"
      value={videoLinkInput}
      onChange={(e) => setVideoLinkInput(e.target.value)}
      placeholder="Upload Video URL here"
      className="flex-1 outline-none lg:text-[16px] text-[14px] text-[#3D3D3D] placeholder-black" // placeholder color set to black
    />
  </div>
</div>

            {/* Video Files List */}
            {videoLinks.length > 0 && (
              <div className="mb-3 space-y-2">
                {videoLinks.map((link, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{i + 1}.</span>
                    <span className="text-[#666666] text-[14px]"> <span className="underline text-[#0000EE] mr-2">  Overview_video.mp4</span>
 ( 356 GB )
</span>
                    </div>
                    <button
                      onClick={() =>
                        setVideoLinks(videoLinks.filter((_, idx) => idx !== i))
                      }
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-end">
              <button
                onClick={handleAddVideoLink}
                className="px-4 py-2 border border-[#063585] text-[#063585] rounded text-sm font-medium hover:bg-blue-50"
              >
                Upload
              </button>
            </div>
          </div>

          {/* Upload Notes */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Notes :
            </label>

            {/* Upload Area */}
            <div className="mb-3">
              <div
                onClick={() => notesInputRef.current?.click()}
                className="border border-gray-400 rounded-md p-8 cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <div className="flex flex-col items-center justify-center text-center">
                    <Image
      src="/assets/images/notes.png" // Change this to your image path
      alt="attachment icon"
      width={40}
      height={40}
      className=" w-[40px] h-[40px] mb-[10px]"
    />
                  <input
                    ref={notesInputRef}
                    type="file"
                    accept=".md"
                    multiple
                    className="hidden"
                    onChange={(e) => handleMultipleFileSelect(e, setNotesFile)}
                  />
                  <p className="text-sm text-[#0065FF] underline mb-1">
                    Choose file or Drag here
                  </p>
                  <p className="text-xs text-gray-500">
                    Supported file type(s) : .md
                  </p>
                  <p className="text-xs text-gray-500">
                    Size limit 20MB per file, up to 10 file(s) with total file size not exceeding 100MB
                  </p>
                </div>
              </div>
            </div>

            {/* Files List */}
            {notesFile.length > 0 && (
              <div className="mb-3">
                {notesFile.map((file, i) => (
                  <div
                    key={i}
                    className="flex items-center justify-between border border-gray-400 rounded-md px-4 py-3 w-full mb-2"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{i + 1}.</span>
                      <span className="text-[#0065FF] underline text-sm">
                        {file.name} ({file.size})
                      </span>
                    </div>
                    <button
                      onClick={() =>
                        setNotesFile(notesFile.filter((_, idx) => idx !== i))
                      }
                      className="text-red-500 hover:text-red-700 text-lg"
                    >
                      ✕
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Upload Button */}
            <div className="flex justify-end mt-4">
              <button
                className="px-6 py-2 border border-[#063585]text-[#063585] rounded text-sm font-medium hover:bg-blue-50"
              >
                Upload
              </button>
            </div>
          </div>



          {/* Upload Quiz (Optional) */}
          <div className="mb-8">
            <label className="block text-base font-medium mb-3">
              Upload Quiz <span className="text-gray-500 text-sm font-normal">(Optional):</span>
            </label>

            {/* Upload Area */}
            <div className="mb-3">
              <div
                onClick={() => quizInputRef.current?.click()}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="w-12 h-12 mb-4 flex items-center justify-center bg-gray-100 rounded">
                    <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M5,3H7V5H5V10A2,2 0 0,1 3,8V6A2,2 0 0,1 5,4V3M19,3V4A2,2 0 0,1 21,6V8A2,2 0 0,1 19,10V5H17V3H19M5,21V20A2,2 0 0,1 3,18V16A2,2 0 0,1 5,14V19H7V21H5M19,21H17V19H19V14A2,2 0 0,1 21,16V18A2,2 0 0,1 19,20V21Z" />
                    </svg>
                  </div>
                  <input
                    ref={quizInputRef}
                    type="file"
                    accept=".json,application/json"
                    multiple
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files) {
                        setSelectedQuizFiles(Array.from(e.target.files));
                      }
                    }}
                  />
                  <p className="text-sm text-blue-600 underline mb-1 cursor-pointer">
                    Choose file or Drag here
                  </p>
                  <p className="text-xs text-gray-500 mb-1">
                    Supported file type(s) : .JSON
                  </p>
                  <p className="text-xs text-gray-500">
                    Size limit 20MB per file, up to 20 file(s) with total file size not exceeding 100MB
                  </p>
                </div>
              </div>
            </div>

            {/* Selected Quiz Files List */}
            {selectedQuizFiles.length > 0 && (
              <div className="mb-3 space-y-2">
                {selectedQuizFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between border border-gray-300 rounded-md px-4 py-3"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 font-medium">{index + 1}.</span>
                      <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                        <span className="text-blue-600 underline text-sm">
                          {file.name}
                        </span>
                        <span className="text-gray-500 text-sm">( {(file.size / (1024 * 1024)).toFixed(2)} MB )</span>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedQuizFiles(prev => prev.filter((_, i) => i !== index));
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>





          {/* Save Button */}
          <div className="flex justify-end mt-12">
            <button
              onClick={handleSaveSession}
              disabled={
                isSubmitting ||
                !lessonTitle ||
                !videoLinks.length ||
                !notesFile.length
              }
              className={`text-white font-medium text-lg px-12 py-3 rounded-md transition-colors ${
                !isSubmitting &&
                lessonTitle &&
                videoLinks.length &&
                notesFile.length
                  ? "bg-[#0A5224] hover:bg-green-700"
                  : "bg-[#0A522480] cursor-not-allowed"
              }`}
            >
{isSubmitting ? "Saving..." : selectedQuizFiles.length > 0 ? "Save Lesson & Upload Quiz" : "Save Lesson"}
            </button>
          </div>
        </div>

        {/* Success Popup */}
        {showSuccess && (
          <div className="fixed top-[24px] right-[24px] w-[361px] bg-[#D1FADF] text-[#027A48] p-4 rounded-md shadow-lg flex items-start gap-3 z-50 border border-[#A6F4C5]">
            <BsCheckCircleFill size={24} className="mt-1" />
            <div>
              <p className="font-semibold text-base">Successfully Saved</p>
            </div>
            {/* Animated bottom border */}
            <div
              className="absolute bottom-0 left-0 h-[4px] bg-[#0A5224] animate-borderGrow"
              style={{ width: "100%" }}
            ></div>
          </div>
        )}
      </div>
      <style jsx>{`
        @keyframes borderGrow {
          0% {
            transform: scaleX(0);
          }
          100% {
            transform: scaleX(1);
          }
        }

        .animate-borderGrow {
          transform-origin: left;
          animation: borderGrow 1s ease-out forwards;
        }

        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #999;
          pointer-events: none;
        }

        .rich-text-editor {
          direction: ltr !important;
          text-align: left !important;
          unicode-bidi: bidi-override !important;
          writing-mode: horizontal-tb !important;
        }

        .rich-text-editor * {
          direction: ltr !important;
          unicode-bidi: bidi-override !important;
          text-align: left !important;
        }

        .rich-text-editor p,
        .rich-text-editor div,
        .rich-text-editor span,
        .rich-text-editor strong,
        .rich-text-editor em,
        .rich-text-editor u {
          direction: ltr !important;
          unicode-bidi: bidi-override !important;
          text-align: left !important;
        }
      `}</style>
    </>
  );
};

export default AddLessionPage;
