"use client";

import { useState, KeyboardEvent, ChangeEvent } from "react";

import {  FaQuestion , FaTimes, FaSearch } from "react-icons/fa";

interface SupportTopBarProps {
  onSearch: (query: string) => void;
}

const SupportTopBar = ({ onSearch }: SupportTopBarProps) => {
  const [searchText, setSearchText] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const handleSearch = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && searchText.trim()) {
      onSearch(searchText);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-3 justify-end items-center py-4 px-1 sm:px-3 xl:px-8 bg-white">
     

      {/* Search Bar */}
      <div
        className={`relative flex items-center border border-gray-300 rounded-lg overflow-hidden transition-all duration-300  xl:right-10 ${
          isExpanded ? "w-full md:max-w-2xl xl:max-w-3xl" : "w-full max-w-lg"
        }`}
        style={{ boxShadow: "0px 4px 4px 0px #00000040" }}
      >
        {/* Search Input */}
        <input
          type="text"
          value={searchText}
          onFocus={() => setIsExpanded(true)}
          onBlur={() => !searchText && setIsExpanded(false)}
          onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchText(e.target.value)}
          onKeyDown={handleSearch}
          placeholder="Feel Free to ask your Questions?"
          className="w-full px-4 py-3 bg-white text-[#656565] text-sm focus:outline-none transition-all duration-300"
        />

        {/* Icon Button */}
        <button
          className="px-4 py-3 bg-[#cebae8] text-[#4B207A] flex items-center justify-center cursor-pointer transition-all duration-300"
          onClick={() => {
            setSearchText("");
            onSearch("");
          }}
          type="button"
        >
          {searchText ? <FaTimes size={20} /> : isExpanded ? <FaSearch size={20} /> : <FaQuestion size={20} />}
        </button>
      </div>
    </div>
  );
};

export default SupportTopBar;
