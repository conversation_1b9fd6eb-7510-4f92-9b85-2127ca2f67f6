'use client'

import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { useState } from "react"
import Image from "next/image"
// import Link from "next/link"
import Sidebar from "@/components/dashboardUI/dashboardsidebar/dashboardsidebar"
import WelcomeHeader from "@/components/dashboardUI/main-container/welcome-header"
import DashboardHeader from "@/components/layout/DashboardHeader";
import Link from "next/link"; // ✅ Import the Link component


export default function Achievement() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
  };

  return (
    <div className="flex flex-col bg-white">
        {/* Header */}
           <DashboardHeader
             isMobileMenuOpen={isMobileMenuOpen}
             toggleMobileMenu={toggleMobileMenu}
           />

      <div className="flex flex-1">
        {/* Sidebar */}
     <div
             className={`${
               isMobileMenuOpen ? "fixed inset-0 z-40 block" : "hidden"
             } w-full md:w-[370px] md:relative md:block`}
             style={{
               background: "linear-gradient(0deg, #F2F2F2, #F2F2F2)",
               borderRight: "1px solid black",
             }}
           >
             <Sidebar />
           </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
    

          <main className="px-[10px] md:px-[40px]">
            <div className="w-full justify-center items-center py-[70px]">
              <div className="mb-6">
      <WelcomeHeader />

                {/* Replaced button with Link */}
                <Link
                  href="/dashboard/performance" // Link to the performance page
                  passHref
                >
                  <button className="mb-6 mt-5 flex text-sm px-4 py-2 rounded bg-[#373737] text-white cursor-pointer">
                    <ArrowLeft className="w-4 h-4 mr-2" /> Back
                  </button>
                </Link>
              </div>

              <div className="space-y-8  justify-center items-center lg:px-[40px]">
                <AchievementCard date="12-March-2025" badgeName="'Unstoppable' badge in Programming" streakDays={14} />
                <AchievementCard date="12-April-2025" badgeName="'Unstoppable' badge in GitHub Programming" streakDays={14} />
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

interface AchievementCardProps {
  date: string
  badgeName: string
  streakDays: number
}

function AchievementCard({ date, badgeName, streakDays }: AchievementCardProps) {
  return (
    <div className=" rounded-md overflow-hidden shadow-sm justify-center items-center   ">
       <div className="bg-[#f1f1f1] py-6 relative ">
      <div className="bg-gradient-to-r from-purple-800 to-green-800 h-18 py-3"></div>
     
        <div className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-2 border-4 border-white shadow-md shadow-gray-500">
          <Image src="/assets/images/fire2.png" alt="Achievement" width={40} height={40} className="h-auto" />
        </div>

        <div className="text-right mb-4">
          <p className="font-[Poppins] font-medium text-[15px] leading-[18px] tracking-[0%] text-right text-black p-4">{date}</p>
        </div>

        <div className="text-center space-y-2">
          <h2 className="font-[Poppins] font-medium text-[24px] leading-[20px] tracking-[0%] text-black mb-4">Congratulations !</h2>
          <p className="font-[Poppins] font-medium text-[18px] leading-[24px] tracking-[0%] text-center text-black px-[10px] lg:px-16">
            You have earned the {badgeName} for successfully maintaining a {streakDays}-day streak! 🎉
          </p>

          </div>
          <div className="mt-10">
          <p className="text-sm text-left  px-4 font-medium text-black">Let&apos;s share and celebrate this achievement!</p>
          </div>
            {/* Social Media Icons */}
                   <div className="max-w-6xl -mb-[14px] ">
                     <div className="flex gap-4 p-3  px-8">
                       <Image className="w-[33px] h-[33px]" src="/assets/images/facebook3.png" alt="Facebook" width={33} height={33} />
                       <Image className="w-[33px] h-[33px]" src="/assets/images/linkedin1.png" alt="LinkedIn" width={33} height={33} />
                     
                       <Image className="w-[33px] h-[33px]" src="/assets/images/instagram1.png" alt="Instagram" width={33} height={33} />
                     </div>
                   </div>
          
        </div>
      </div>
  
  )
}
