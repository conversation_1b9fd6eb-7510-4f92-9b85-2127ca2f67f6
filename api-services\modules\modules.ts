import { makeRequest } from "../utils";
import { useQuery, useMutation } from "@tanstack/react-query";

// ================== MODULE INTERFACES ==================
interface Module {
  id: string;
  title: string;
  overview_md: string;
  course: string;
  created_by: number;
  created_at: string;
}

interface GetModulesResponse {
  message: string;
  data: Module[];
}

interface CreateModulePayload {
  title: string;
  overview_md?: string;
}

interface CreateModuleResponse {
  message: string;
  data: Module;
}

interface UpdateModulePayload {
  title?: string;
  overview_md?: string;
}

interface UpdateModuleResponse {
  message: string;
  data: Module;
}

interface DeleteModuleResponse {
  message: string;
}

interface GetModuleResponse {
  message: string;
  data: Module;
}

// ================== GET MODULES API ==================
async function getModules(courseId: string): Promise<GetModulesResponse> {
  console.log("Calling GET modules API for course:", courseId);

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/`,
    method: "GET",
  });

  console.log("GET modules API response:", response);
  return response;
}

// ================== GET SINGLE MODULE API ==================
async function getModule(courseId: string, moduleId: string): Promise<GetModuleResponse> {
  console.log("Calling GET module API for course:", courseId, "module:", moduleId);

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/${moduleId}/`,
    method: "GET",
  });

  console.log("GET module API response:", response);
  return response;
}

// ================== GET SINGLE MODULE BY ID ONLY ==================
async function getModuleById(moduleId: string): Promise<GetModuleResponse> {
  console.log("Calling GET module API for module:", moduleId);

  const response = await makeRequest({
    endpoint: `/api/modules/${moduleId}/`,
    method: "GET",
  });

  console.log("GET module by ID API response:", response);
  return response;
}

// ================== CREATE MODULE API ==================
async function createModule(courseId: string, payload: CreateModulePayload | FormData): Promise<CreateModuleResponse> {
  console.log("Calling CREATE module API for course:", courseId, "with payload:", payload);

  try {
    const isFormData = payload instanceof FormData;

    const response = await makeRequest({
      endpoint: `/api/courses/${courseId}/modules/`,
      method: "POST",
      data: isFormData ? payload : (payload as Record<string, unknown>),
      isFileUpload: isFormData,
    });

    console.log("CREATE module API response:", response);
    return response;
  } catch (error) {
    console.error("CREATE module API error:", error);
    console.error("Payload that failed:", payload);
    console.error("CourseId:", courseId);
    throw error;
  }
}

// ================== UPDATE MODULE API ==================
async function updateModule(courseId: string, moduleId: string, payload: UpdateModulePayload): Promise<UpdateModuleResponse> {
  console.log("Calling UPDATE module API for course:", courseId, "module:", moduleId, "with payload:", payload);

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/${moduleId}/`,
    method: "PUT",
    data: payload as Record<string, unknown>,
  });

  console.log("UPDATE module API response:", response);
  return response;
}

// ================== DELETE MODULE API ==================
async function deleteModule(courseId: string, moduleId: string): Promise<DeleteModuleResponse> {
  console.log("Calling DELETE module API for course:", courseId, "module:", moduleId);

  const response = await makeRequest({
    endpoint: `/api/courses/${courseId}/modules/${moduleId}/`,
    method: "DELETE",
  });

  console.log("DELETE module API response:", response);
  return response;
}

// ================== REACT QUERY HOOKS ==================
const useGetModules = (courseId: string) => {
  return useQuery<GetModulesResponse, Error>({
    queryKey: ["modules", courseId],
    queryFn: () => getModules(courseId),
    enabled: !!courseId, // Only run query if courseId exists
  });
};

const useGetModule = (courseId: string, moduleId: string) => {
  return useQuery<GetModuleResponse, Error>({
    queryKey: ["module", courseId, moduleId],
    queryFn: () => getModule(courseId, moduleId),
    enabled: !!courseId && !!moduleId, // Only run query if both IDs exist
  });
};

const useGetModuleById = (moduleId: string) => {
  return useQuery<GetModuleResponse, Error>({
    queryKey: ["module", moduleId],
    queryFn: () => getModuleById(moduleId),
    enabled: !!moduleId, // Only run query if moduleId exists
  });
};

const useCreateModule = () => {
  return useMutation<CreateModuleResponse, Error, { courseId: string; payload: CreateModulePayload | FormData }>({
    mutationFn: ({ courseId, payload }) => createModule(courseId, payload),
  });
};

const useUpdateModule = () => {
  return useMutation<UpdateModuleResponse, Error, { courseId: string; moduleId: string; payload: UpdateModulePayload }>({
    mutationFn: ({ courseId, moduleId, payload }) => updateModule(courseId, moduleId, payload),
  });
};

const useDeleteModule = () => {
  return useMutation<DeleteModuleResponse, Error, { courseId: string; moduleId: string }>({
    mutationFn: ({ courseId, moduleId }) => deleteModule(courseId, moduleId),
  });
};

export {
  useGetModules,
  useGetModule,
  useGetModuleById,
  useCreateModule,
  useUpdateModule,
  useDeleteModule
};

export type {
  Module,
  GetModulesResponse,
  GetModuleResponse,
  CreateModulePayload,
  CreateModuleResponse,
  UpdateModulePayload,
  UpdateModuleResponse,
  DeleteModuleResponse
};
