"use client";

import { useState } from "react";

export default function CertificateFilter() {
  const [courseName, setCourseName] = useState("");
  const [certificateState, setCertificateState] = useState("");
  const [filterApplied, setFilterApplied] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<FilterField | null>(null);

  const courseOptions = ["Python", "JavaScript", "React", "Next.js", "Node.js"];
  const certificateOptions = ["Earned", "Not Earned", "In Progress"];

  const handleApplyFilter = () => {
    if (courseName && certificateState) {
      setFilterApplied(true);
    } else {
      alert("Please select all filters before applying.");
    }
  };

  type FilterField = "course" | "state";

  const handleDropdownToggle = (dropdown: FilterField | null) => {
    setOpenDropdown(prev => (prev === dropdown ? null : dropdown));
  };

  const handleSelect = (field: FilterField, value: string) => {
    if (field === "course") setCourseName(value);
    if (field === "state") setCertificateState(value);
    setOpenDropdown(null);
  };

  const filteredData = [
    {
      no: "01",
      name: "Aditi S.",
      course: "Python",
      state: "Earned",
      progress: "100%",
      upcoming: "None",
    },
    {
      no: "02",
      name: "Kugan",
      course: "Python",
      state: "Earned",
      progress: "100%",
      upcoming: "None",
    },
    {
      no: "03",
      name: "Sakshi",
      course: "Python",
      state: "Earned",
      progress: "100%",
      upcoming: "None",
    },
  ];

  return (
    <div className="py-4 sm:py-6 space-y-6 mx-auto">
      <h2 className="text-[20px] font-semibold text-[#59207C] underline mb-3">
        Filter Options
      </h2>

      {/* Filter Box */}
      <div className="bg-white rounded-md shadow-md w-full space-y-4 max-w-[600px]">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-medium bg-[#EDEDED]">

          {/* Course Name */}
          <div className="relative">
            <div
              onClick={() => handleDropdownToggle("course")}
              className="cursor-pointer font-bold flex justify-between items-center bg-[#EDEDED] px-4 py-2 "
            >
              Course Name
              <span className={`ml-2 transition-transform ${openDropdown === "course" ? "rotate-180" : ""}`}>
                ▼
              </span>
            </div>
            {openDropdown === "course" && (
              <div className="absolute z-10 mt-2 bg-white border w-full">
                {courseOptions.map((option, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSelect("course", option)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Certificate State */}
          <div className="relative">
            <div
              onClick={() => handleDropdownToggle("state")}
              className="cursor-pointer font-bold flex justify-between items-center bg-[#EDEDED] px-4 py-2 "
            >
              Certificate state
              <span className={`ml-2 transition-transform ${openDropdown === "state" ? "rotate-180" : ""}`}>
                ▼
              </span>
            </div>
            {openDropdown === "state" && (
              <div className="absolute z-10 mt-2 bg-white border w-full">
                {certificateOptions.map((option, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSelect("state", option)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Selected values preview */}
        <div className="p-[20px] mt-[10px]">
          <div className="grid grid-cols-2 gap-[30px]">
            <div className="text-center mt-2 font-normal">
              {courseName && <div>{courseName}</div>}
              <div className="border-b border-dashed mt-1" />
            </div>
            <div className="text-center mt-2 font-normal">
              {certificateState && <div>{certificateState}</div>}
              <div className="border-b border-dashed mt-1" />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-4 pt-4 justify-between">
            <button
              onClick={() => {
                setCourseName("");
                setCertificateState("");
                setFilterApplied(false);
                setOpenDropdown(null);
              }}
              className="border px-6 py-2 text-sm bg-white shadow"
            >
              Reset
            </button>
            <button
              onClick={handleApplyFilter}
              className="border px-6 py-2 text-sm bg-white shadow "
            >
              Apply
            </button>
          </div>
        </div>
      </div>

      {/* Certificate Table */}
      {filterApplied && (
        <div className="overflow-x-auto bg-white rounded-md shadow mt-[30px]">
          <table className="min-w-[800px] w-full text-left text-sm">
            <thead className="bg-gray-200 text-black font-semibold">
              <tr>
                <th className="px-4 py-3">No</th>
                <th className="px-4 py-3">Name</th>
                <th className="px-4 py-3">Course name</th>
                <th className="px-4 py-3">Certificate state</th>
                <th className="px-4 py-3">Progress</th>
                <th className="px-4 py-3">Upcoming</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-300">
              {filteredData.map((row, i) => (
                <tr key={i}>
                  <td className="px-4 py-3">{row.no}</td>
                  <td className="px-4 py-3 font-medium">{row.name}</td>
                  <td className="px-4 py-3">{row.course}</td>
                  <td className="px-4 py-3">{row.state}</td>
                  <td className="px-4 py-3">{row.progress}</td>
                  <td className="px-4 py-3">{row.upcoming}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
