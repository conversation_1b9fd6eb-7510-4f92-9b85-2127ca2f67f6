"use client";

import { useState } from "react";

export default function BadgeTrackerFilter() {
  const [badgeCategory, setBadgeCategory] = useState("");
  const [courseName, setCourseName] = useState("");
  const [completionStatus, setCompletionStatus] = useState("");
  const [filterApplied, setFilterApplied] = useState(false);
 const [openDropdown, setOpenDropdown] = useState<FilterField | null>(null);


  const badgeOptions = [
    "Streak & Behavior",
    "Course Completion",
    "Quiz Performance",
    "Project Submission",
    "Referral-Based",
    "Social & Fun",
    "None",
  ];
  const courseOptions = ["Python", "JavaScript", "React", "Next.js", "Node.js"];
  const statusOptions = ["Earned", "Not Earned", "In Progress"];

  const handleApplyFilter = () => {
    if (badgeCategory && courseName && completionStatus) {
      setFilterApplied(true);
    } else {
      alert("Please select all filters before applying.");
    }
  };

type FilterField = "badge" | "course" | "status"; // Adjust based on your actual options


const handleDropdownToggle = (dropdown: FilterField | null) => {
  setOpenDropdown(prev => (prev === dropdown ? null : dropdown));
};



const handleSelect = (field: FilterField, value: string) => {
  if (field === "badge") setBadgeCategory(value);
  if (field === "course") setCourseName(value);
  if (field === "status") setCompletionStatus(value);
  setOpenDropdown(null);
};


  const filteredData = [
    {
      no: "01",
      name: "Aditi",
      category: "Streak & Behavior",
      earned: "Yes",
      course: "Python",
      status: "Earned",
      date: "10/02/2025",
    },
    {
      no: "02",
      name: "Sonal",
      category: "Streak & Behavior",
      earned: "Yes",
      course: "Python",
      status: "Earned",
      date: "20/02/2025",
    },
  ];

  return (
    <div className="py-4 sm:py-6 space-y-6  mx-auto">
      <h2 className="text-[20px] font-semibold text-[#59207C] underline mb-3">
        Filter Options
      </h2>

      {/* Filter Box */}
      <div className="bg-white rounded-md shadow-md w-full space-y-4 max-w-[600px]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm font-medium bg-[#EDEDED]">

          {/* Badge Category */}
          <div className="relative">
            <div
              onClick={() => handleDropdownToggle("badge")}
              className="cursor-pointer font-bold flex justify-between items-center bg-[#EDEDED] px-4 py-2 "
            >
              Badge Category
              <span className={`ml-2 transition-transform ${openDropdown === "badge" ? "rotate-180" : ""}`}>
                ▼
              </span>
            </div>
         
            {openDropdown === "badge" && (
              <div className="absolute z-10 mt-2 bg-white border  w-full">
                {badgeOptions.map((option, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSelect("badge", option)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Course Name */}
          <div className="relative" >
            <div
              onClick={() => handleDropdownToggle("course")}
              className="cursor-pointer font-bold flex justify-between items-center bg-[#EDEDED] px-4 py-2 "
            >
              Course Name
              <span className={`ml-2 transition-transform ${openDropdown === "course" ? "rotate-180" : ""}`}>
                ▼
              </span>
            </div>
         
            {openDropdown === "course" && (
              <div className="absolute z-10 mt-2 bg-white border rounded shadow w-full">
                {courseOptions.map((option, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSelect("course", option)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Completion Status */}
          <div className="relative">
            <div
              onClick={() => handleDropdownToggle("status")}
              className="cursor-pointer font-bold flex justify-between items-center bg-[#EDEDED] px-4 py-2 "
            >
              Completion Status
              <span className={`ml-2 transition-transform ${openDropdown === "status" ? "rotate-180" : ""}`}>
                ▼
              </span>
            </div>
        
            {openDropdown === "status" && (
              <div className="absolute z-10 mt-2 bg-white border rounded shadow w-full">
                {statusOptions.map((option, idx) => (
                  <div
                    key={idx}
                    onClick={() => handleSelect("status", option)}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>

        </div>
        <div className="p-[20px] mt-[10px]">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-[30px] ">
   <div className="text-center mt-2 font-normal">
              {badgeCategory && <div>{badgeCategory}</div>}
              <div className="border-b border-dashed mt-1" />
            </div>
               <div className="text-center mt-2 font-normal">
              {courseName && <div>{courseName}</div>}
              <div className="border-b border-dashed mt-1" />
            </div>
               <div className="text-center mt-2 font-normal">
              {completionStatus && <div>{completionStatus}</div>}
              <div className="border-b border-dashed mt-1" />
            </div>
</div>
        {/* Buttons */}
        <div className="flex gap-4 pt-4 justify-between">
          <button
            onClick={() => {
              setBadgeCategory("");
              setCourseName("");
              setCompletionStatus("");
              setFilterApplied(false);
              setOpenDropdown(null);
            }}
            className="border px-6 py-2 text-sm bg-white shadow"
          >
            Reset
          </button>
          <button
            onClick={handleApplyFilter}
            className="border px-6 py-2 text-sm bg-white shadow "
          >
            Apply Filters
          </button>
        </div>
        </div>

      </div>

      {/* Filtered Table */}
      {filterApplied && (
        <div className="overflow-x-auto bg-white rounded-md shadow mt-[30px]">
          <table className="min-w-[800px] w-full text-left text-sm">
            <thead className="bg-gray-200 text-black font-semibold">
              <tr>
                <th className="px-4 py-3">No</th>
                <th className="px-4 py-3">Student name</th>
                <th className="px-4 py-3">Badge category</th>
                <th className="px-4 py-3">Badge earned</th>
                <th className="px-4 py-3">Course name</th>
                <th className="px-4 py-3">Complete status</th>
                <th className="px-4 py-3">Date earned</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-300">
              {filteredData.map((row, i) => (
                <tr key={i}>
                  <td className="px-4 py-3">{row.no}</td>
                  <td className="px-4 py-3 font-medium">{row.name}</td>
                  <td className="px-4 py-3">{row.category}</td>
                  <td className="px-4 py-3">{row.earned}</td>
                  <td className="px-4 py-3">{row.course}</td>
                  <td className="px-4 py-3">{row.status}</td>
                  <td className="px-4 py-3">{row.date}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
