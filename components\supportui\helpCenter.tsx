'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FiChevronDown, FiChevronUp, FiThumbsUp, FiThumbsDown } from 'react-icons/fi';

interface FAQ {
  id: number;
  question: string;
  answer: string;
}

const faqs: FAQ[] = [
  { 
    id: 1, 
    question: "I've tried many times to make a payment, but it hasn't been processed?", 
    answer: `We understand your disappointment. We request trying again after some time and considering changing the payment method. 
    You can seek assistance from your friends or family to complete the payment.

    You can also take the help of any cyber cafe in your locality to complete the payment and make sure to take the print of the invoice.

    If the problem persists or if you have any other difficulties in this regard, please do not hesitate to raise a ticket mentioning the issue clearly. 
    We are here to assist you and find a solution to ensure a smooth payment process.

    Thank you for your patience and understanding. Your satisfaction is our priority, and we are committed to resolving any issues you encounter.`
  },
  { id: 2, question: "I have been charged twice against my payment. Can you please check?", answer: "If you've been charged twice, please check your transaction history. If the issue persists, contact support..." },
  { id: 3, question: "Where do I find the practice questions/assignments and their solutions?", answer: "You can find practice questions and their solutions in the resources section of your account." },
  { id: 4, question: "How can I provide feedback for my teacher or lecture?", answer: "You can provide feedback through the feedback section in your profile settings." }
];

const additionalFaqs: FAQ[] = [
  { id: 5, question: "How do I reset my password?", answer: "To reset your password, go to account settings and click on 'Reset Password'." },
  { id: 6, question: "What should I do if I encounter a technical issue?", answer: "If you face a technical issue, please reach out to our support team with a screenshot of the problem." }
];

const HelpCenter = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [showMore, setShowMore] = useState<boolean>(false);
  const [feedback, setFeedback] = useState<Record<number, 'like' | 'dislike' | null>>({});

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const handleFeedback = (id: number, type: 'like' | 'dislike') => {
    setFeedback((prev) => ({
      ...prev,
      [id]: prev[id] === type ? null : type,
    }));
  };

  const displayedFaqs = showMore ? [...faqs, ...additionalFaqs] : faqs;

  return (
    <div className="p-6 pt-0">
      {displayedFaqs.map((faq, index) => (
        <div key={faq.id} className="border-[1px] border-[#656565] rounded-[12px] mb-6 overflow-hidden">
          <button
            className="w-full text-left p-[5px] md:p-2 flex justify-between items-center focus:outline-none transition-all duration-300"
            onClick={() => toggleAccordion(index)}
          >
            {/* Numbering Style */}
            <div className="flex items-center">
              <span className="w-[25px] h-[25px] md:w-[40px] md:h-[40px] text-[13px] md:text-[20px] flex items-center justify-center bg-[#4B207A] text-white rounded-full mr-3 p-[10px]">
                {faq.id}
              </span>
              <span className="font-normal text-[13px] md:text-[18px]">{faq.question}</span>
            </div>
            {/* Toggle Icon */}
            <div>
              {openIndex === index ? <FiChevronUp size={20} /> : <FiChevronDown size={20} />}
            </div>
          </button>

          {/* Animated Answer */}
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={openIndex === index ? { height: 'auto', opacity: 1 } : { height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden bg-[#E8E8E8] p-0"
          >
            <div className="p-6 text-[14px] font-medium leading-[27px]">
              {faq.answer}
              {/* Like & Dislike Icons */}
              <div className="flex items-center mt-4 justify-end">
                <button
                  className={`flex items-center text-gray-600 hover:text-blue-600 mr-4 ${
                    feedback[faq.id] === 'like' ? 'text-blue-600' : ''
                  }`}
                  onClick={() => handleFeedback(faq.id, 'like')}
                >
                  <FiThumbsUp size={18} className="mr-1" />
                </button>
                <button
                  className={`flex items-center text-gray-600 hover:text-red-600 ${
                    feedback[faq.id] === 'dislike' ? 'text-red-600' : ''
                  }`}
                  onClick={() => handleFeedback(faq.id, 'dislike')}
                >
                  <FiThumbsDown size={18} className="mr-1" />
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      ))}

      {/* See More Button with Line */}
      {!showMore && (
        <div className="flex items-center justify-center mt-4">
          <hr className="flex-grow border-gray-300" />
          <button onClick={() => setShowMore(true)} className="mx-3 text-black flex items-center cursor-pointer">
            See more <FiChevronDown className="ml-1" />
          </button>
          <hr className="flex-grow border-gray-300" />
        </div>
      )}
    </div>
  );
};

export default HelpCenter;
