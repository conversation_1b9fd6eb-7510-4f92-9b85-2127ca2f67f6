import React from 'react';

const AIInEducation: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto p-4 md:p-6">
      <h1 className="text-[22px] md:text-3xl lg:text-[46px] font-semibold font-poppins text-gray-900 mb-3 md:mb-4 leading-tight">
        What I Learned About Learning and Technology Using AI in Education
      </h1>
      <p className="text-sm md:text-base text-gray-500 mb-4 md:mb-6">29 Jan 2025</p>
      <img
        src="/assets/about/ai.jpg"
        alt="AI in classroom"
        className="w-full h-48 md:h-64 lg:h-72 rounded-md mb-4 md:mb-6 shadow-lg"
        style={{ boxShadow: '0px 5px 10px 0px #00000080' }}
      />
      <p className="text-[#434343] text-[20px] md:text-lg lg:text-[26px] leading-[150%] md:leading-[160%] font-poppins">
        Education is changing, and artificial intelligence is at the heart of it. From personalized lessons to AI tutors, we’re witnessing a shift in how students learn and how teachers teach. My journey into exploring AI's role in classrooms taught me some surprising lessons—about learning, technology, and the future of human connection in education.
      </p>
    </div>
  );
};

export default AIInEducation;
