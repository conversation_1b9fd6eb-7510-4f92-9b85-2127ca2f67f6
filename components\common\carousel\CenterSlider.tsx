"use client"

import { useState, useEffect } from "react"
import { cn } from "@/api-services/utils"

interface CardData {
  id: number
  title: string
  points: string[]
  bgImage: string
}

const cardData: CardData[] = [
  {
    id: 1,
    title: "Preparation for Real-World Success",
    points: [
      "Apexiq Partasala's training is aligned with current industry needs.",
      "Courses equip students with practical abilities that match employer expectations.",
      "This targeted training prepares students for successful careers.",
    ],
    bgImage: "/assets/images/goal.png",
  },
  {
    id: 2,
    title: "Quality Education by Industry Experts",
    points: [
      "Courses are crafted by experienced professionals, combining theory with real-world skills.",
      "The curriculum emphasizes hands-on learning to build expertise.",
      "Students gain essential skills to meet industry demands.",
    ],
    bgImage: "/assets/images/industry.png",
  },
  {
    id: 3,
    title: "Hands-On Learning Approach",
    points: [
      "Apexiq Partasala emphasizes interactive learning, engaging students with the material.",
      "Hands-on activities and real-world scenarios help students gain experience.",
      "This approach builds confidence, preparing students to excel.",
    ],
    bgImage: "/assets/images/handson.png",
  },
]

export default function CenterSlider() {
  const [activeIndex, setActiveIndex] = useState<number>(0)
  const [isMobile, setIsMobile] = useState(false)
  const totalCards = cardData.length

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % totalCards)
    }, 4000)
    return () => clearInterval(interval)
  }, [totalCards])

  const getVisibleCards = () => {
    if (isMobile) {
      return [cardData[activeIndex]]
    }

    const leftIndex = (activeIndex - 1 + totalCards) % totalCards
    const rightIndex = (activeIndex + 1) % totalCards

    return [cardData[leftIndex], cardData[activeIndex], cardData[rightIndex]]
  }

  return (
    <div className="px-4 pb-8 sm:pt-16 relative">
      {/* Desktop View */}
      <div className="hidden lg:flex justify-center gap-4 relative w-full">
        {getVisibleCards().map((card, idx) => (
          <div className="w-1/3" key={card.id}>
            <div
              className={cn(
                "shadow-[4px_4px_4px_0px_#00000040] p-6 transition-all duration-300 flex flex-col min-h-[290px] md:min-h-[420px] w-full",
                idx === 1 ? "scale-110 opacity-100 transform transition-transform ease-in-out" : "scale-90 opacity-60",
              )}
              style={{
                backgroundImage: `url(${card.bgImage})`,
                backgroundSize: "70%",
                backgroundPosition: "center",
                backgroundColor: "#D9D9D9",
                backgroundRepeat: "no-repeat",
              }}
            >
              <h3 className="font-semibold text-[24px] leading-[30px] text-center font-[Poppins] text-[#000000] mb-10">
                {card.title}
              </h3>
              <ul className="space-y-2 md:space-y-3 flex-grow flex flex-col gap-3">
                {card.points.map((point, pIdx) => (
                  <li key={pIdx} className="flex items-start gap-2 text-black font-poppins">
                    <span className="text-xs font-bold font-poppins mt-2">•</span>
                    <span className="text-xs md:text-lg font-normal font-poppins">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>

      {/* Mobile View */}
      <div className="lg:hidden min-h-[290px] sm:min-h-[420px]">
        <div
          className="rounded-none shadow-[4px_4px_4px_0px_#00000040] p-6 transition-all duration-300 flex flex-col min-h-[290px] sm:min-h-[420px]"
          style={{
            backgroundImage: `url(${cardData[activeIndex].bgImage})`,
            backgroundSize: "70%",
            backgroundPosition: "center",
            backgroundColor: "#D9D9D9",
            backgroundRepeat: "no-repeat",
          }}
        >
          <h2 className="font-semibold text-[20px] leading-[100%] tracking-[0%] text-center font-[Poppins] text-[#000000] mb-10">
            {cardData[activeIndex].title}
          </h2>
          <ul className="space-y-2 flex-grow flex flex-col gap-3">
            {cardData[activeIndex].points.map((item, i) => (
              <li key={i} className="flex items-start gap-2 text-black font-poppins">
                <span className="text-xs font-bold font-poppins">•</span>
                <span className="text-xs md:text-lg font-normal font-poppins">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Indicator Dots */}
      <div className="flex justify-center mt-8 space-x-2">
        {cardData.map((_, idx) => (
          <button
            key={idx}
            onClick={() => setActiveIndex(idx)}
            className={cn(
              "w-3 h-3 rounded-full transition-all",
              activeIndex === idx ? "bg-purple-700" : "bg-gray-300"
            )}
            aria-label={`Go to slide ${idx + 1}`}
          />
        ))}
      </div>
    </div>
  )
}
