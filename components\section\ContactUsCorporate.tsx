"use client";
import React, { useState, ChangeEvent, FormEvent } from 'react';
import InputField from '../common/InputField';
import TextArea from '../common/TextArea';
import SubmitButton from '../common/SubmitButton';
import { Errors, FormData } from '@/types/types';
// import Image from 'next/image';
// Define the structure of the form data and errors




const ContactUsCorporate: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    aboutUs: '',
  });

  const [errors, setErrors] = useState<Errors>({
    name: '',
    email: '',
    phone: '',
    aboutUs: '',
  });

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const validate = (): boolean => {
    let valid = true;
    const errors: Errors = { name: '', email: '', phone: '', aboutUs: '' }; // changed 'let' to 'const'
  
    if (!formData.name) {
      errors.name = 'Name is required';
      valid = false;
    }
  
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email || !emailPattern.test(formData.email)) {
      errors.email = 'Valid email is required';
      valid = false;
    }
  
    const phonePattern = /^[0-9]{10}$/;
    if (!formData.phone || !phonePattern.test(formData.phone)) {
      errors.phone = 'Valid 10-digit phone number is required';
      valid = false;
    }
  
    if (!formData.aboutUs) {
      errors.aboutUs = 'Message is required';
      valid = false;
    }
  
    setErrors(errors);
    return valid;
  };
  

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (validate()) {
      // Handle form submission (e.g., send form data to the backend)
      console.log('Form submitted', formData);
    }
  };

  return (
    <>
      {/* <!-- contact us --> */}
      <section className="sm:py-12 px-4 sm:px-6 lg:px-8 lg:h-[800px] relative overflow-hidden bg-white">
        <div className="container mx-auto px-4 md:px-0 lg:px-8 mb-0">
          <h2 className="sm:text-[36px] text-xl font-poppins font-semibold text-center mb-2 mt-2">Contact Us</h2>
          <div className="relative py-8 md:py-16 min-h-[200px] flex justify-center items-center">
            {/* <!-- Image Section --> */}
            {/* <div className="hidden lg:block absolute z-10 top-20 bottom-10 left-0 ml-10 mt-20 px-0 w-full sm:w-2/3 md:w-2/3 max-w-sm">
              <Image src="/assets/images/Group55.png" alt="" className="object-contain object-top w-[281px] h-[403px]" />
            </div> */}

            {/* <!-- Form Section --> */}
            <form id="contactForm" onSubmit={handleSubmit} className="relative w-full sm:w-3/5 md:w-3/4 lg:w-2/3 xl:w-1/2 min-h-fit rounded-3xl p-3 sm:p-3 md:p-6 lg:p-5 -mt-5 bg-[#cdcdcd]">
              <div className="relative z-50 w-full rounded-3xl items-center p-4 sm:p-3 md:p-4 lg:p-6">
                <div className="grid sm:grid-row md:grid-rows-1 gap-4 sm:gap-6 md:gap-8 lg:gap-12">
                  {/* <!-- Left Section - Three Input Fields --> */}
                  <div className="space-y-4 sm:space-y-5">
                    <InputField
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleChange}
                      label="Name"
                      error={errors.name}
                    />
                    <InputField
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      label="Email"
                      error={errors.email}
                    />
                    <InputField
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      label="Contact-No"
                      error={errors.phone}
                    />
                  </div>

                  {/* <!-- Right Section - Message Area --> */}
                  <TextArea
                    id="aboutUs"
                    name="aboutUs"
                    value={formData.aboutUs}
                    onChange={handleChange}
                    label="Message"
                    error={errors.aboutUs}
                  />
                </div>

                {/* <!-- Submit Button --> */}
                <SubmitButton label="Submit" />
              </div>
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactUsCorporate;
