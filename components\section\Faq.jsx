"use client";
import React, { useState } from "react";
import { FiChevronUp, FiChevronDown } from "react-icons/fi";
import { motion } from "framer-motion";

const faqs = [
  {
    id: 1,
    question: "What is the difference between ApexIQ and other platforms?",
    answer:
      "There are many variations of passages available, but the majority have suffered alteration in some form, by injected humour, or randomized words which don’t look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn’t anything embarrassing hidden.",
  },
  {
    id: 2,
    question: " I have a technical issue that needs to be resolved. Who should I contact?",
    answer:
      "If you have a technical issue, please contact our support <NAME_EMAIL>. We aim to respond within 24 hours to assist you with your query.",
  },
  {
    id: 3,
    question: "What other services is ApexIQ compatible with?",
    answer:
      "Apexiq integrates seamlessly with various third-party services, including CRM platforms, payment gateways, and marketing tools. Check our documentation for a full list of compatible services.",
  },
  {
    id: 4,
    question: "How secure is my data on ApexIQ?",
    answer:
      "We take security very seriously. All user data is encrypted and stored using industry-leading security protocols to ensure maximum protection.",
  },
];

const Faq = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-[30px] md:py-[60px]">
      <div className="small-container ">
      <h2 className="text-[22px] md:text-[32px] font-semibold text-[#1D1D1D] text-center mb-[30px] md:mb-[60px] font-poppins">
      Frequently Asked Questions
        </h2>
        {faqs.map((faq, index) => (
          <div key={faq.id} className="border-[1px] border-[#656565] hover:border-[2px] hover:border-[#4B207A] rounded-[12px] mb-4 overflow-hidden">
            <button
              className="w-full text-left p-[5px] md:p-2 flex justify-between items-center focus:outline-none transition-all duration-300"
              onClick={() => toggleAccordion(index)}
            >
              {/* Numbering Style */}
              <div className="flex items-center">
                <span className="w-[25px] h-[25px] md:w-[40px] md:h-[40px] text-[13px] md:text-[20px] flex items-center justify-center bg-[#4B207A] text-white rounded-full mr-3 p-[10px]">
                  {faq.id}
                </span>
                <span className="font-normal text-[13px] md:text-[18px]">{faq.question}</span>
              </div>
              {/* Toggle Icon */}
              <div>
                {openIndex === index ? <FiChevronUp size={20} /> : <FiChevronDown size={20} />}
              </div>
            </button>

            {/* Animated Answer */}
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={openIndex === index ? { height: "auto", opacity: 1 } : { height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden bg-[#E8E8E8] p-0"
            >
              <div className="p-6 text-[14px] font-medium leading-[27px]">{faq.answer}</div>
            </motion.div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Faq;
