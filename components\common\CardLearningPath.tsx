import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface CardLearningPathProps {
  image: string;
  title: string;
  link: string;
}

const CardLearningPath: React.FC<CardLearningPathProps> = ({ image, title, link }) => {
  return (
    <div className="bg-[#d9d9d9] rounded-lg shadow-sm flex flex-col h-full hover:shadow-md transition-shadow">
      <div className="aspect-w-16 aspect-h-9 relative">
        <Image 
          src={image} 
          alt={title}
          fill
          className="w-full h-[266px] object-cover object-top"
        />
      </div>
      <div className="p-4 flex flex-col flex-grow">
        <h3 className="font-medium text-[24px] font-poppins mt-5 mb-[30px]">{title}</h3>
        <div className="flex-grow"></div>
        <Link
          href={link}
          className="text-[18px] text-[#063585] underline mt-2 font-medium"
        >
          See learn path
        </Link>
      </div>
    </div>
  );
};

export default CardLearningPath;
