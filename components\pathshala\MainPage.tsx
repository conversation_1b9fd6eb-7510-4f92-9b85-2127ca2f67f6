import React from "react";
import HeaderPath from "../layout/HeaderPath";
import HeroSection from "./HeroSection";
import QuoteSection from "./QuoteSection";
// import CardCarouselCor from "../common/carousel/Card_carousel_Cor";
import PopularTopic from "../common/PopularTopic";

import CardLearningPath from "../common/CardLearningPath";
import ContactUsCorporate from "../section/ContactUsCorporate";

import { learningPaths } from "@/utils/constant";

import CenterSlider from "../common/carousel/CenterSlider";
import LogoSlider from "./LogoSlider";
import Getfree from "./Getfree";


const MainPage: React.FC = () => {
  return (
    <>
      {/* Add your content here */}
      <HeaderPath />
      <HeroSection />
      <QuoteSection />

      <section className="md:pt-[80px] pt-[30px]">
        <div className="container align-center mt-4 ">
          <h2 className="lg:text-[36px] sm:text-2xl font-semibold text-black font-poppins mb-5">
            {" "}
            Why Choose Apexiq Partasala for Education{" "}
          </h2>
<CenterSlider/>
          {/* <CardCarouselCor /> */}
        
        </div>
      </section>
      <PopularTopic />
      <section className="md:pt-[50px] pt-[30px]">
        <div className="container">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="lg:text-[36px] sm:text-2xl font-semibold text-black font-poppins">
              Popular Learning Paths
            </h2>
            <button className="bg-[#4B207A] md:text-[20px] text-[10px] font-medium text-white px-4 py-2 rounded-md transition-colors font-poppins">
              See All Topics
            </button>
          </div>

          {/* Scrollable on Mobile, Grid on Desktop */}
          <div
            className="
        flex space-x-8 overflow-x-auto snap-x snap-mandatory py-4
        md:grid md:grid-cols-3 md:gap-[50px] md:overflow-x-visible md:snap-none md:space-x-0
      "
          >
            {learningPaths?.map((path, index) => (
              <div
                key={index}
                className="snap-start shrink-0 min-w-[250px] md:min-w-0"
              >
                <CardLearningPath
                  image={path.image}
                  title={path.title}
                  link={path.link}
                />
              </div>
            ))}
          </div>
        </div>
      </section>
   <LogoSlider/>
      <ContactUsCorporate />
<Getfree/>
     
    </>
  );
};

export default MainPage;
