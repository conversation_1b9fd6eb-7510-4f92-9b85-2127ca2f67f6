import Image from "next/image"
import Link from 'next/link';
import {
  Flame, // For "Trending"
  Trophy // For "Popular"
} from "lucide-react"
import { CourseCardProps } from "@/types/types";



export default function CourseCard({ course }: CourseCardProps) {
  const getBadgeColor = (color?: string) => {
    switch (color) {
      case "green":
        return "bg-green-500 text-white"
      case "red":
        return "bg-red-500 text-white"
      case "blue":
        return "bg-blue-500 text-white"
      case "yellow":
        return "bg-yellow-500 text-red-500"
      default:
        return "bg-purple-500 text-white"
    }
  }
  
  // Function to render the appropriate icon based on badge text
  const getBadgeIcon = (badgeText?: string) => {
    switch (badgeText) {
      case "New Updated":
        return <Image
        src="/assets/images/Maskgroup(2).png"
        alt={course.badge || "Badge"}
        width={16}
        height={16}
        className="h-5 w-5 object-contain"
      />
      case "LIVE":
        return <Image
        src="/assets/images/Ellipse213.png"
        alt={course.badge || "Badge"}
        width={5}
        height={5}
        className="h-2 w-2 object-contain mr-2"
      />
      case "Trending":
        return <Flame size={16} className="mr-1" />
      case "Popular":
        return <Trophy size={16} className="mr-1" />
      default:
        return null
    }
  }
  
  return (
    <div className="bg-[#d9d9d9] rounded-lg shadow-sm hover:shadow-md transition-shadow  w-[226px] md:w-[268px] lg:w-[363px]  sm:h-[400px] md:h-[450px] flex flex-col shrink-0">
      <div className="relative">
        <Image
          src={course.image}
          alt={course.title}
          width={363}
          height={266}
          className="w-full h-auto sm:h-[200px] md:h-[266px] object-cover object-top rounded-t-lg"
        />
        {(course.badge || course.badgeImage) && (
          <div
            className={`absolute top-2 right-2 px-2 py-1 rounded font-poppins ${
              course.badgeImage ? 'bg-white p-1' : getBadgeColor(course.badgeColor)
            } flex items-center`}
          >
            {course.badgeImage ? (
              <Image
                src={course.badgeImage}
                alt={course.badge || "Badge"}
                width={24}
                height={24}
                className="h-5 w-5 object-contain"
              />
            ) : (
              <>
                {getBadgeIcon(course.badge)}
                <span className="text-xs font-semibold">{course.badge}</span>
              </>
            )}
          </div>
        )}
      </div>
      <div className="px-3 sm:px-4 py-5 flex flex-col flex-grow">
        <h3 className="font-poppins font-medium text-base sm:text-[16px] md:text-[20px] leading-tight sm:leading-[25px] py-4 flex-grow ">{course.title}</h3>
        <div className="  flex flex-row items-center justify-between flex-grow">
          <Link href="/" passHref>
            <button className="w-[90px] sm:w-[100px] md:w-[135px] h-[38px] sm:h-[42px] md:h-[51px] flex justify-center items-center gap-1 sm:gap-2 rounded-md border border-[#4caf50] text-green-600 hover:bg-[#4caf50] hover:text-white font-poppins font-bold text-lg sm:text-[20px] md:text-[25px] leading-[30px] cursor-pointer">
              paid
            </button>
          </Link>
          <div className="font-poppins font-semibold text-lg sm:text-[20px] md:text-[25px] leading-[30px] text-[#ff8400]">₹{course.price}</div>
        </div>
      </div>
    </div>
  )
}