'use client';
import Link from "next/link";

import WelcomeContent from "@/components/signupui/welcome";
import Image from "next/image";
import OtpForm from "@/components/signupui/OtpForm";


export default function Signup() {

  

  return (
    <>
      <header className="border-b border-[#868686]">
    

        <div className="px-4 flex flex-col">
          {/* Header with logo */}
          <div className="p-1">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="w-[150px]   cursor-pointer"
            />
          </div>

        </div>
      </header>
    <section className="md:py-[40px] py-[30px]">
      <main className="container">

    <div className="w-full flex flex-col-reverse lg:flex-row items-center justify-center gap-6 px-4 md:px-8">
  <div className="w-full lg:max-w-[773px]">
    <OtpForm/>
  </div>

  <div className="w-full md:max-w-[500px] hidden md:flex flex-col items-center text-center justify-center px-4">
    <WelcomeContent />
  
  </div>
</div>

      </main>
      </section>
    </>
  );
}
