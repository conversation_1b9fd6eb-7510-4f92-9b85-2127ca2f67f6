export function Notes() {
    return (
      <div className="space-y-4 font-poppins text-[#000000] font-normal text-[16px] leading-[28px] tracking-[-1%] p-[10px]">
        <p>
        Prompt engineering is the art of crafting precise and effective inputs to guide AI models toward generating the desired outputs. It&apos;s akin to giving a chef detailed instructions for a recipe; the more specific and clear you are, the better the final dish will turn out.
Understanding the Basics
        </p>
  
        <p>
          
         AI models, particularly large language models (LLMs), are trained on massive datasets of text and code. 
         They learn to predict the next word or symbol in a sequence, enabling them to generate text, translate languages, 
        write different kinds of creative content, and answer your questions in an informative way. However, the 1 quality of
          the output heavily relies on the quality of the input, which is where prompt engineering comes into play.
        </p>
        <h3 className=" mt-4 mb-2 font-[Poppins] font-normal text-[16px] leading-[28px] tracking-[-1%] text-[#11031F]">Key Principles:</h3>
        <ul className="list-disc pl-5 space-y-2 ml-4 font-[Poppins] font-normal text-[16px] leading-[28px] tracking-[-1%] text-[#11031F]">
          <li>
            Specificity: The more specific your prompt, the more tailored the response will be. Instead
            of asking &quot;Tell me about Paris,&quot; try &quot;Describe the Eiffel Tower&apos;s architecture in detail.&quot;
          </li>
          <li>
           Context:Providing context helps the AI understand the nuances of your request. For example,
            if you&apos;re writing a poem, specifying the theme, tone, and poetic form will significantly improve the result.
          </li>
          <li>
           Instructions: Explicit instructions guide the AI&apos;s behavior. You can tell it to &quot;Write a
            short story in the style of Edgar Allan Poe&quot; or &quot;Summarize this article in five bullet points.&quot;
          </li>
          <li>
           Examples: Demonstrating the desired output through examples can be highly effective. If you
            want the AI to generate code, providing a sample of the expected code structure can be beneficial.
          </li>
          <li>
            Iteration: Prompt engineering is an iterative process. Start with a basic prompt, then refine
            it based on the initial output. Experiment with different wordings and structures to achieve the best results.
          </li>
        </ul>
        <h3 className=" mt-4 mb-2 font-[Poppins] font-normal text-[16px] leading-[28px] tracking-[-1%] text-[#11031F]">Key Principles:</h3>
        <ul className="list-disc pl-5 space-y-2 ml-4 font-[Poppins] font-normal text-[16px] leading-[28px] tracking-[-1%] text-[#11031F]">
          <li>
            Specificity: The more specific your prompt, the more tailored the response will be. Instead
            of asking &quot;Tell me about Paris,&quot; try &quot;Describe the Eiffel Tower&apos;s architecture in detail.&quot;
          </li>
          <li>
           Context:Providing context helps the AI understand the nuances of your request. For example,
            if you&apos;re writing a poem, specifying the theme, tone, and poetic form will significantly improve the result.
          </li>
          <li>
           Instructions: Explicit instructions guide the AI&apos;s behavior. You can tell it to &quot;Write a
            short story in the style of Edgar Allan Poe&quot; or &quot;Summarize this article in five bullet points.&quot;
          </li>
          <li>
           Examples: Demonstrating the desired output through examples can be highly effective. If you
            want the AI to generate code, providing a sample of the expected code structure can be beneficial.
          </li>
          <li>
            Iteration: Prompt engineering is an iterative process. Start with a basic prompt, then refine
            it based on the initial output. Experiment with different wordings and structures to achieve the best results.
          </li>
        </ul>
      </div>
    )
  }
  