"use client";

import { useState } from "react";
import Sidebar from "@/components/dashboardUI/dashboardsidebar/dashboardsidebar";
import MainContent from "@/components/dashboardUI/main-container/main-container";
import DashboardHeader from "@/components/layout/DashboardHeader";

export default function Dashboard() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <DashboardHeader
        isMobileMenuOpen={isMobileMenuOpen}
        toggleMobileMenu={toggleMobileMenu}
      />

      <div className="flex flex-1">
        {/* Sidebar */}
        <div
          className={`${
            isMobileMenuOpen ? "fixed inset-0 z-40 block" : "hidden"
          } w-full md:w-[370px] md:relative md:block`}
          style={{
            background: "linear-gradient(0deg, #F2F2F2, #F2F2F2)",
            borderRight: "1px solid black",
          }}
        >
          <Sidebar />
        </div>

        {/* Main content area */}
        <div className="flex-1 min-w-0 mt-[60px]">
          {/* Main content */}
          <div className="px-[10px] md:px-[40px]">
            <MainContent />
          </div>
        </div>
      </div>
    </div>
  );
}
