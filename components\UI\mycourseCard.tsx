"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { LearningPath } from "@/types/types"; // make sure this file exists!
import { CourseUnlockPopup } from "@/components/UI/mycard";

interface CourseCardProps {
  course: LearningPath;
}

export default function CourseCard({ course }: CourseCardProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const router = useRouter();

  const handlePayClick = () => {
    setIsPopupOpen(false);
  };

  const handleReferredClick = () => {
    setIsPopupOpen(false);
  };

  const handleContinueClick = () => {
    if (course.id === 4) {
      setIsPopupOpen(true); // Show popup only for course ID 4
    } else {
      router.push(course.link); // Navigate using the course's dynamic link
    }
  };

  return (
  <div className="bg-[#d9d9d9] rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
  <div className="relative w-full h-[268px]">
    <Image
      src={course.image}
      alt={course.title}
      fill
      className="object-cover object-top"
    />
  </div>
  <div className="p-[20px] md:p-[30px] flex flex-col flex-grow ">
    <h3 className="font-medium text-black text-base sm:text-lg md:text-xl leading-tight mb-[10px] lg:mb-[30px] mt-[10px] lg:mt-[20px] line-clamp-2">
      {course.title}
    </h3>
    <div className="mt-auto pt-2">
      <button
        onClick={handleContinueClick}
        className="w-full py-2 sm:py-3 rounded-md bg-green-600 text-white font-medium text-sm sm:text-base flex justify-center items-center cursor-pointer"
      >
        Continue
      </button>
    </div>
  </div>

  <CourseUnlockPopup
    isOpen={isPopupOpen}
    onClose={() => setIsPopupOpen(false)}
    onPayClick={handlePayClick}
    onReferredClick={handleReferredClick}
  />
</div>

  );
}
