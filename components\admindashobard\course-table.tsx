"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { X, Edit } from "lucide-react";
import { useGetCourses, useDeleteCourse } from "@/api-services/courses/courses";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import EditCourseModal from "./EditCourseModal";

// Define the course type to match API response
interface Course {
  id: string;
  name: string;
  tags: string[];
  thumbnail: string;
  price: string;
  is_active: boolean;
  is_published: boolean;
  created_by: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
}

// No static data needed - using API data

interface CourseTableProps {
  searchQuery: string;
}

export default function CourseTable({ searchQuery }: CourseTableProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const queryClient = useQueryClient();

  // Fetch courses from API
  const { data: coursesResponse, isLoading, error, refetch } = useGetCourses();
  // Filter out inactive/deactivated courses
  const allCourses = coursesResponse?.data || [];
  const courses = allCourses.filter(course => course.is_active === true);

  // Delete course mutation
  const deleteMutation = useDeleteCourse();

  const filteredCourses = courses.filter((course) =>
    course.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredCourses.length / itemsPerPage);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [courseToEdit, setCourseToEdit] = useState<Course | null>(null);

  const handleDelete = (id: string) => {
    const course = courses.find((course) => course.id === id);
    if (course) {
      setCourseToDelete(course);
      setIsModalOpen(true);
    }
  };

  const handleEdit = (id: string) => {
    const course = courses.find((course) => course.id === id);
    if (course) {
      setCourseToEdit(course);
      setIsEditModalOpen(true);
    }
  };

  const handleEditSuccess = () => {
    // Additional success handling if needed
    console.log('Course updated successfully');
  };

  const confirmDelete = async () => {
    if (courseToDelete) {
      try {
        console.log("Delete course:", courseToDelete.id);

        // Call DELETE API
        const response = await deleteMutation.mutateAsync(courseToDelete.id);
        console.log("Delete response:", response);

        // Show success message
        toast.success(`Course "${courseToDelete.name}" deleted successfully!`);

        // Close modal
        setIsModalOpen(false);
        setCourseToDelete(null);

        // Invalidate and refetch courses
        queryClient.invalidateQueries({ queryKey: ["courses"] });

      } catch (error) {
        console.error("Error deleting course:", error);
        console.error("Error details:", error);
        toast.error("Failed to delete course. Please try again.");

        // Close modal even on error
        setIsModalOpen(false);
        setCourseToDelete(null);
      }
    }
  };

  const cancelDelete = () => {
    setIsModalOpen(false);
    setCourseToDelete(null);
  };

  const paginatedCourses = filteredCourses.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleRowClick = (id: string) => {
    router.push(`/Admin/dashboard/viewcourse?id=${id}`);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full overflow-x-auto">
        <div className="min-w-[900px] shadow-md mx-[10px] min-h-[619px] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#0A5224] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading courses...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full overflow-x-auto">
        <div className="min-w-[900px] shadow-md mx-[10px] min-h-[619px] flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Error loading courses: {error.message}</p>
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-[#0A5224] text-white rounded-md"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[900px] shadow-md mx-[10px] min-h-[619px]">
        <table className="w-full text-center">
          <thead className="bg-[#ededed]">
            <tr>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                No
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Image
              </th>
              <th className="px-4 font-poppins font-semibold text-[18px] text-black">
                Course title
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Price
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Tag
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Enrolled
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Module
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Lesson
              </th>
              <th className="px-4 py-5 font-poppins font-semibold text-[18px] text-black">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#C3C3C3]">
            {paginatedCourses.length > 0 ? (
              paginatedCourses.map((course, index) => (
                <tr
                  key={course.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleRowClick(course.id)}
                >
                  <td className="px-4 py-3 text-sm text-gray-700">
                    {(currentPage - 1) * itemsPerPage + index + 1}
                  </td>
                  <td className="px-4 py-3">
                    <div className="h-[60px] w-[60px] relative bg-gray-100 rounded flex items-center justify-center">
                      <div className="text-gray-400 text-xs text-center">
                        📚
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 font-poppins font-normal text-[16px] text-black">
                    {course.name}
                  </td>
                  <td className="px-4 py-3 font-poppins font-normal text-[16px] text-black">
                    {course.price === "0.00" ? "Free" : `₹${course.price}`}
                  </td>
                  <td className="px-4 py-3">
                    <span className="px-2 py-1 font-poppins font-normal text-[16px] rounded-full">
                      {course.tags.length > 0 ? course.tags[0] : "No Tag"}
                    </span>
                  </td>
                  <td className="px-4 py-3 font-poppins font-normal text-[16px] text-black">
                    -
                  </td>
                  <td className="px-4 py-3 font-poppins font-normal text-[16px] text-black">
                    -
                  </td>
                  <td className="px-4 py-3 font-poppins font-normal text-[16px] text-black">
                    -
                  </td>
                <td
                  className="px-4 py-3 font-poppins font-normal text-[16px] text-black"
                  onClick={(e) => e.stopPropagation()} // prevent row click when clicking action buttons
                >
                  <div className="flex items-center gap-2 justify-center">
                    <button
                      onClick={() => handleEdit(course.id)}
                      className="text-blue-500 hover:text-blue-700"
                      aria-label={`Edit ${course.name}`}
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleDelete(course.id)}
                      className="text-red-500 hover:text-red-700"
                      aria-label={`Delete ${course.name}`}
                    >
                      <X size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            ))
            ) : (
              <tr>
                <td colSpan={9} className="px-4 py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center">
                    <p className="text-lg font-medium">No courses found</p>
                    <p className="text-sm mt-1">
                      {searchQuery ? "Try adjusting your search" : "Create your first course to get started"}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-4 py-4 ">
        <div className="text-sm text-gray-500 text-center sm:text-left">
          Showing{" "}
          <span className="font-medium text-[#438eff] px-6 ">
            {(currentPage - 1) * itemsPerPage + 1} -{" "}
            {Math.min(currentPage * itemsPerPage, filteredCourses.length)}
          </span>{" "}
          of <span className="font-medium text-[#438eff] px-9">{filteredCourses.length}</span>{" "}
          rows
        </div>
        <div className="flex flex-wrap justify-center sm:justify-end items-center gap-3 px-8">
          <button
            className="text-[#438eff] text-sm px-9"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </button>
          <button
            className="text-[#646464] text-sm px-5"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span className="bg-[#438eff] text-white px-3 py-1 text-sm">
            {currentPage}
          </span>
          <button
            className="text-[#438eff] border border-[#438eff] px-3 py-1 text-sm"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Next
          </button>
          <button
            className="text-[#438eff] text-sm px-10"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Last
          </button>
        </div>
      </div>

      {/* Confirmation Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-[#00000052] z-[99]">
          <div className="bg-white p-10 w-[433px] text-center relative">
            {/* Close Button */}
            <button
              onClick={cancelDelete}
              className="absolute top-2 right-2 text-xl text-gray-500 hover:text-gray-700"
              aria-label="Close delete confirmation"
            >
              <X />
            </button>

            <p className="text-sm text-gray-600 font-poppins font-normal text-[14px] mb-2">
              Are you sure you want to delete this course?
            </p>
            <p className="text-sm text-gray-600 mb-6 font-poppins font-normal text-[14px] text-center">
              This action cannot be undone.
            </p>

            <div className="flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                disabled={deleteMutation.isPending}
                className="px-4 py-2 border border-[#FF0000] text-[#FF0000] text-[14px] font-medium hover:bg-[#FF0000] hover:text-white transition-colors duration-300 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {deleteMutation.isPending ? "Deleting..." : "Delete"}
              </button>
              <button
                onClick={cancelDelete}
                disabled={deleteMutation.isPending}
                className="px-4 py-2 border border-gray-300 text-gray-700 text-[14px] font-medium hover:bg-gray-200 transition-colors duration-300 cursor-pointer disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Course Modal */}
      <EditCourseModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        course={courseToEdit}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
}
