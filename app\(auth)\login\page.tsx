'use client';
import Link from "next/link";
import WelcomeContent from "@/components/signupui/welcome";
import Image from "next/image";
import { LoginForm } from "@/components/signupui/LoginForm";


export default function Signup() {

  

  return (
    <>
      <header className="border-b border-[#868686]">
    

        <div className="px-4 py-2 flex flex-row justify-between items-center">
          {/* Header with logo */}
          <div className="">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="md:w-[150px] w-[100px] cursor-pointer"
            />
          </div>
<div>   <Link
        href="/signup"
        className="font-poppins py-[5px]   px-[20px] md:py-2 md:px-9 bg-[#1B57C0] text-white text-[16px] lg:text-[22px]  font-medium rounded-[8px] transition-colors cursor-pointer"
      >
        Signup
      </Link></div>
        </div>
      </header>
    <section className="md:py-[40px] py-[30px]">
      <main className="container">

    <div className="w-full flex flex-col-reverse lg:flex-row items-center justify-center gap-6 px-4 md:px-8">
  <div className="w-full lg:max-w-[640px]">
<LoginForm/>
  </div>

  <div className="w-full md:max-w-[500px] hidden md:flex flex-col items-center text-center justify-center px-4">
    <WelcomeContent />
   
  </div>
</div>

      </main>
      </section>
    </>
  );
}
