"use client";

import React, { useState } from 'react'
import HeaderPath from "@/components/layout/HeaderPath";
import HeroSection from "@/components/blog/hero";
import Blogcard, { cardsData } from "@/components/blog/blogcard";

const Page = () => {
  const [search, setSearch] = useState("");

  // Filter cards by title // logic: card title and search query dono ko lower case me convert kiya
  const filteredCards = cardsData.filter(card =>
    card.title.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <>
      <HeaderPath />
      
      <HeroSection search={search} setSearch={setSearch} />
      <Blogcard cardsData={filteredCards} />
    </>
  )
}

export default Page