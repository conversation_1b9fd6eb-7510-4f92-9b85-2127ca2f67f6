import { Benefit, FacultyMember, LearningPath, Module, Project, Testimonial, Tool, Topic,StatItem, AboutTestimonial, QuizQuestion } from "@/types/types";



const courses = [
    {
      id: 1,
      title: "Python",
      price: 1000,
      image: "/assets/images/Untitleddesign(1).png",
      isPaid: true,
      link: "/mycourse/prompteng",
    
    },
    {
      id: 2,
      title: "GitHub",
      price: 1000,
      image: "/assets/images/Untitleddesign(18).png",
      isPaid: true,
      link: "/mycourse/prompteng",
    },
    {
      id: 3,
      title: "MongoDB",
      price: 1000,
      image: "/assets/images/Untitleddesign(17).png",
      isPaid: true,
      link: "/mycourse/prompteng",
      
    },
    {
      id: 4,
      title: "EDA (Exploratory Data Analysis)",
      price: 1000,
      image: "/assets/images/Untitleddesign(16).png",
      isPaid: true,
      link: "/mycourse/prompteng",
     
    },
    {
      id: 5,
      title: "Statistics",
      price: 1000,
      image: "/assets/images/Untitleddesign(15).png",
      isPaid: true,
      link: "/mycourse/prompteng",
    },
    {
      id: 6,
      title: "Machine Learning",
      price: 1000,
      image: "/assets/images/Untitleddesign(14).png",
      isPaid: true,
      link: "/mycourse/prompteng",
   
    },
    {
      id: 7,
      title: "Deep Learning",
      price: 1000,
      image: "/assets/images/Untitleddesign(4).png",
      isPaid: true,
    },
    {
      id: 8,
      title: "Computer VIsion",
      price: 1000,
      image: "/assets/images/Untitleddesign(3).png",
      isPaid: true,
    },
    {
      id: 9,
      title: "MySQL",
      price: 1000,
      image: "/assets/images/Untitleddesign(2).png",
      isPaid: true,
    },
  ];



// src/data/learningPaths.ts


export const learningPaths: LearningPath[] = [
  {
    id: 1,
    image: "/assets/images/lp1.png",
    title: "Python",
    link: "/course1"
  },
  {
    id: 2,
    image: "/assets/images/lp2.png",
    title: "GitHub",
    link: "/course1"
  },
  {
    id: 3,
    image: "/assets/images/lp3.png",
    title: "MongoDB",
    link: "/course1"
  },
  {
    id: 4,
    image: "/assets/images/lp4.png",
    title: "EDA (Exploratory Data Analysis)",
    link: "/course1"
  },
  {
    id: 5,
    image: "/assets/images/lp5.png",

    title: "Statistics",
    link: "/course1"
  },
  {
    id: 6,
    image: "/assets/images/lp6.png",
    title: "Machine Learning",
    link: "/course1"
  }
];

export const topics: Topic[] = [
  { icon: "/assets/images/ai.png", title: "Artificial Intelligence", href: "/ai" },
  { icon: "/assets/images/data-science.png", title: "Data Science", href: "/ai" },
  { icon: "/assets/images/deep-learning.png", title: "Deep Learning", href: "/ai" },
  { icon: "/assets/images/machine-learning.png", title: "Machine Learning", href: "/ai" },
  { icon: "/assets/images/promt-e.png", title: "Prompt Engineering", href: "/ai" },
  { icon: "/assets/images/cloud-computing.png", title: "Cloud Computing", href: "/ai" },
  { icon: "/assets/images/generative-ai.png", title: "Generative AI", href: "/ai" },
  { icon: "/assets/images/chatgpt.png", title: "ChatGPT for Non-IT", href: "/ai" },
  { icon: "/assets/images/devops.png", title: "DevOps", href: "/ai" },
];

export const tools: Tool[] = [
  { src: "/assets/images/vscode.png", alt: "VSCode" },
{ src: "/assets/images/github.png", alt: "GitHub" },
{ src: "/assets/images/gitlab1.png", alt: "GitLab" },
{ src: "/assets/images/figma1.png", alt: "Figma" },
{ src: "/assets/images/photoshop1.png", alt: "Photoshop" },
{ src: "/assets/images/sketch1.png", alt: "Sketch" },
{ src: "/assets/images/python1.png", alt: "Python" },
{ src: "/assets/images/js1.png", alt: "JavaScript" },
{ src: "/assets/images/vscode.png", alt: "VSCode" },
{ src: "/assets/images/github.png", alt: "GitHub" }
];


export const cardsData = [
    {
      image: "/image.png",
      title: "Neural Networks & Deep Learning",
      description: "Master the fundamentals of pattern recognition through neural networks.",
      href: "/corporate/ai/neural-networks",
    },
    {
      image: "/aiimg.png",
      title: "AI Fundamentals History and Concepts",
      description: "Understand the evolution and core concepts of artificial intelligence.",
      href: "/corporate/ai/neural-networks",
    },
    {
      image: "/aiethics.png",
      title: "AI Ethics and Governance",
      description: " Explore ethical considerations in AI development and implementation.",
      href: "/corporate/ai/neural-networks",
     
    },

  ];

    export const benefits: Benefit[] = [
      {
        title: "Project Portfolio",
        description:
          "Develop a job-ready profile by building a strong portfolio with real-world deep learning projects.",
      },
      {
        title: "Interview Opportunities",
        description:
          "Access interview opportunities for roles in leading companies after course completion.",
      },
      {
        title: "Alumni Network",
        description:
          "Join a vibrant alumni community of AI enthusiasts and experts for networking and collaboration.",
      },
      {
        title: "Career Growth",
        description:
          "Accelerate your career with advanced knowledge and expertise in neural networks and deep learning.",
      },
      {
        title: "Certification",
        description:
          "Earn prestigious certifications for internships and course completion recognized by top industries.",
      },
    ];

    // Create an array of faculty members with the type `FacultyMember[]`
    export const facultyMembers: FacultyMember[] = [
      {
        name: "R.M Kumaran",
        degree: "BSc (Hons) in Computer Science",
        experience: "5+ Years",
        teaching: "6+ Years",
        skills: "Neural Network | Artificial Intelligence | Data Science",
        image: "/faculty1.png", // Replace with actual image path
        linkedin: "https://www.linkedin.com", // Replace with actual LinkedIn profile URL
      },
      {
        name: "R.M Kumaran",
        degree: "BSc (Hons) in Computer Science",
        experience: "5+ Years",
        teaching: "6+ Years",
        skills: "Neural Network | Artificial Intelligence | Data Science",
        image: "/faculty2.png", // Replace with actual image path
        linkedin: "https://www.linkedin.com", // Replace with actual LinkedIn profile URL
      },
    ];
    
    export const projects: Project[] = [
      {
        title: "Medical Image Analysis",
        description:
          "Create a neural network model to detect diseases like cancer, pneumonia, or diabetic retinopathy from medical images such as X-rays, MRIs, or retinal scans. This project hones skills in image processing, CNNs, data preprocessing, and transfer learning. Key tools include Python with TensorFlow, Keras, or PyTorch, along with OpenCV for image handling.",
        image: "/proj1.png",
      },
      {
        title: "Autonomous Vehicle Navigation Simulation",
        description:
          "Develop an AI-powered self-driving car simulation using reinforcement learning and computer vision. This project focuses on deep learning techniques, sensor fusion, and path planning. Key tools include Python, TensorFlow, OpenCV, and CARLA simulator.",
        image: "/proj2.png",
      },
      {
        title: "Facial Recognition and Emotion Detection",
        description:
          "Build a deep learning model to recognize faces and detect emotions from real-time video feeds. This project emphasizes computer vision, CNNs, and real-time data processing. Key tools include OpenCV, TensorFlow, and dlib.",
        image: "/proj3.png",
      },
    ];

// Define the testimonials array with the correct type
export const testimonials: Testimonial[] = [
  {
    text: "An excellent platform for assessing both strategic thinking and practical application. This approach allows us to identify the most skilled candidates.",
    author: "Sarah T. - Head of Talent Acquisition",
  },
  {
    text: "A comprehensive process that effectively evaluates core competencies and problem-solving abilities. We look forward to further discussions during the interviews.",
    author: "Anil M. - Senior Operations Manager",
  },
  {
    text: "This provides valuable insights into real-world skills, giving us a clear understanding of each candidate's potential. We'll cover additional aspects in the interviews.",
    author: "Emily B. - Director of Human Resources",
  },
  {
    text: "A thorough way to gauge essential skill sets and on-the-ground readiness. We'll focus on advanced skills in the next round.",
    author: "David L. - VP of Product Development",
  },
];

  export const modules: Module[] = [
    {
      id: "module-1",
      title: "Module 1",
      intro: "Introduction",
      items: [
        {
          id: 1,
          title: "1st April Introduction to prompt engineering",
          type: "lesson",
        },
        {
          id: 2,
          title: "2nd April Basic of prompt engineering",
          type: "reading",
        },
        {
          id: 3,
          title: "3rd April Reading Material",
          type: "book",
        },
        {
          id: 4,
          title: "4th April Introduction to prompt engineering",
          type: "reading",
        },
        {
          id: 5,
          title: "5th April Reading Material",
          type: "book",
        },
        {
          id: 6,
          title: "6th April Assignment no 1",
          type: "assignment",
        },
      ],
    },
    {
      id: "module-2",
      title: "Module 2",
      intro: "Introduction",
      items: [],
    },
    {
      id: "module-3",
      title: "Module 3",
      intro: "Introduction",
      items: [],
    },
    {
      id: "module-4",
      title: "Module 4",
      intro: "Introduction",
      items: [],
    },
    {
      id: "module-5",
      title: "Module 5",
      intro: "Introduction",
      items: [],
    },
    {
      id: "module-6",
      title: "Module 6",
      intro: "Introduction",
      items: [],
    },
  ]


// about page

export const statsData: StatItem[] = [
  { number: '10+', label: 'Year Experience' },
  { number: '99%', label: 'Accuracy Rate' },
  { number: '830+', label: 'Positive Reviews' },
  { number: '100K', label: 'Trusted Students' },
];


export 
const abouttestimonials: AboutTestimonial[] = [
  {
    name: "John Doe",
    title: "Lorem ipsum dolar",
    rating: 5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
  {
    name: "John Doe",
    title: "Lorem ipsum dolar",
    rating: 5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
  {
    name: "John Doe",
    title: "Lorem ipsum dolar",
    rating: 5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
  {
    name: "John Doe",
    title: "Student",
    rating: 4.5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
  {
    name: "John Doe",
    title: "Student",
    rating: 4.5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
  {
    name: "John Doe",
    title: "Student",
    rating: 4.5,
    feedback:
      "Lorem ipsum dolor sit amet consectetur. Ultricies in adipiscing augue netus potenti integer diam ultricies amet.",
    image: "/user-avatar.png",
  },
];






  export const partners: string[] = [
    "/assets/images/Vector(1).png",
    "/assets/images/fedex-express-61.png",
    "/assets/images/Vector(1).png",
    "/assets/images/Vector(1).png",
    "/assets/images/Vector(1).png",
  ];

// quiz

  export const quizData: QuizQuestion[] = [
    {
      id: 1,
      question: "What is prompt engineering?",
      options: [
        "The process of training an AI model.",
        "The art of crafting effective inputs to guide AI models.",
        "The ability to understand complex algorithms.",
        "The process of debugging AI-generated code.",
      ],
      explanation: "Prompt engineering is the art of crafting effective inputs to guide AI models.",
      correctAnswer: 1,
    },
    {
      id: 2,
      question: "What is prompt engineering?",
      options: [
        "The process of training an AI model.",
        "The art of crafting effective inputs to guide AI models.",
        "The ability to understand complex algorithms.",
        "The process of debugging AI-generated code.",
      ],
      explanation: "Prompt engineering is the art of crafting effective inputs to guide AI models.",
      correctAnswer: 1,
    },
    {
      id: 3,
      question: "What is prompt engineering?",
      options: [
        "The process of training an AI model.",
        "The art of crafting effective inputs to guide AI models.",
        "The ability to understand complex algorithms.",
        "The process of debugging AI-generated code.",
      ],
      explanation: "Prompt engineering is the art of crafting effective inputs to guide AI models.",
      correctAnswer: 1,
    },
    {
      id: 4,
      question: "What is prompt engineering?",
      options: [
        "The process of training an AI model.",
        "The art of crafting effective inputs to guide AI models.",
        "The ability to understand complex algorithms.",
        "The process of debugging AI-generated code.",
      ],
      explanation: "Prompt engineering is the art of crafting effective inputs to guide AI models.",
      correctAnswer: 1,
    },
    {
      id: 5,
      question: "What is prompt engineering?",
      options: [
        "The process of training an AI model.",
        "The art of crafting effective inputs to guide AI models.",
        "The ability to understand complex algorithms.",
        "The process of debugging AI-generated code.",
      ],
      explanation: "Prompt engineering is the art of crafting effective inputs to guide AI models.",
      correctAnswer: 1,
    },
  ]


  export {courses}

  