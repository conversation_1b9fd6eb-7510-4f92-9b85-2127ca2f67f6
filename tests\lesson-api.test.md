# Lesson API Testing Guide

## Manual Testing Steps

### Prerequisites
1. Ensure the development server is running
2. Have test files ready:
   - A `.md` file for notes (e.g., `lesson-notes.md`)
   - A `.json` file for quiz (e.g., `quiz-data.json`)
   - A `.pdf` file for project (e.g., `project-guide.pdf`)
3. Have a valid Vimeo video URL

### Test Case 1: Successful Lesson Creation

1. **Navigate to Add Lesson Page**
   - Go to `/Admin/dashboard/addlession?courseId=test-course-id&moduleId=f9551eae-11ba-4bee-80a8-a7255a6662f4`
   - Verify the page loads correctly
   - Check that the module title is fetched and displayed (not hardcoded)

2. **Fill Required Fields**
   - Enter lesson title: "Test Lesson - Introduction to Python"
   - Add video URL: "https://vimeo.com/123456789"
   - Upload a `.md` file for notes

3. **Submit Form**
   - Click "Save Lesson" button
   - Verify loading state shows "Saving..."
   - Check browser console for API call logs

4. **Expected Results**
   - Success toast notification appears
   - Form is disabled during submission
   - <PERSON> navigates back after success
   - <PERSON>sol<PERSON> shows successful API response

### Test Case 2: Form Validation

1. **Test Empty Title**
   - Leave title field empty
   - Try to submit
   - Expected: Error toast "Lesson title is required"

2. **Test Missing Video URL**
   - Fill title but no video URL
   - Try to submit
   - Expected: Error toast "Please add at least one video URL"

3. **Test Missing Notes**
   - Fill title and video URL but no notes file
   - Try to submit
   - Expected: Error toast "Please upload at least one notes file"

### Test Case 3: File Upload Testing

1. **Test Notes Upload**
   - Click notes upload area
   - Select a `.md` file
   - Verify file appears in the list with correct name and size

2. **Test Quiz Upload**
   - Click quiz upload area
   - Select a `.json` file
   - Verify file appears in the list

3. **Test Project Files Upload**
   - Click project files upload area
   - Select a `.pdf` file
   - Verify file appears in the list

4. **Test File Removal**
   - Upload files and click the "✕" button
   - Verify files are removed from the list

### Test Case 4: API Error Handling

1. **Test Network Error**
   - Disconnect internet or block API endpoint
   - Try to submit form
   - Expected: Error toast "Failed to create lesson. Please try again."

2. **Test Invalid Module ID**
   - Use an invalid moduleId in URL
   - Try to submit form
   - Expected: Appropriate error handling

### Console Debugging

Check browser console for these logs:
```
Calling GET module API for course: test-course-id module: f9551eae-11ba-4bee-80a8-a7255a6662f4
GET module API response: {message: "Success", data: {id: "...", title: "Module Title", ...}}
Creating lesson with payload: {title: "...", video_url: "...", ...}
Module ID: f9551eae-11ba-4bee-80a8-a7255a6662f4
FormData contents:
title: Test Lesson
video_url: https://vimeo.com/123456789
notes_md_0: [File object]
```

### API Response Verification

Expected successful response format:
```json
{
  "message": "Lesson created successfully",
  "data": {
    "id": "lesson-uuid",
    "title": "Test Lesson - Introduction to Python",
    "video_url": "https://vimeo.com/123456789",
    "notes_md": "uploaded-file-url",
    "module": "f9551eae-11ba-4bee-80a8-a7255a6662f4",
    "created_by": 1,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### Troubleshooting

#### Common Issues:

1. **Import Error**: `useCreateLesson is not defined`
   - Check if the import path is correct
   - Verify the lessons service is exported properly

2. **Toast Not Showing**:
   - Verify react-hot-toast is installed
   - Check if Toaster component is in layout

3. **File Upload Not Working**:
   - Check file size limits
   - Verify file types are accepted
   - Check FormData construction

4. **API Call Failing**:
   - Verify NEXT_PUBLIC_BASE_URL is set
   - Check network tab for actual request
   - Verify authentication token

#### Debug Commands:

```bash
# Check if dependencies are installed
npm list react-hot-toast @tanstack/react-query

# Start development server with verbose logging
npm run dev

# Check environment variables
echo $NEXT_PUBLIC_BASE_URL
```

### Performance Testing

1. **Large File Upload**
   - Upload files close to 20MB limit
   - Verify upload progress and completion

2. **Multiple Files**
   - Upload maximum allowed files
   - Check total size doesn't exceed limits

3. **Concurrent Uploads**
   - Try uploading multiple file types simultaneously
   - Verify all files are processed correctly
