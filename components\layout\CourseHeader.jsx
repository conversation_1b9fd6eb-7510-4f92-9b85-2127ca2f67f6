"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { HiMenu, HiX } from "react-icons/hi"; // Import icons from React Icons
import Image from "next/image";
import { GiBookmarklet } from "react-icons/gi";
import { FaUser } from "react-icons/fa";
import { BiSupport } from "react-icons/bi";
import { FiLogOut } from "react-icons/fi";
import { BsCartCheckFill } from "react-icons/bs";
const  CourseHeader = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  const [isOpen, setIsOpen] = useState(false);

  // Toggle dropdown function
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest("#dropdownMenu") && !event.target.closest("#profileImage")) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <>
      <nav className="bg-white border-b border-1 border-[#868686]">
        <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 z-10">
            {/* <!-- Logo --> */}
            <div className="flex-shrink-0 flex items-center">
              <Link href="/">
                <img
                  className="h-10 sm:h-12 w-auto"
                  src="/assets/images/logo.png"
                  alt="Logo"
                />
              </Link>
            </div>

            {/* <!-- Desktop Menu --> */}
            <div className="flex flex-row md:gap-20 gap-3 items-center">
          
     


            <div className="relative">
      {/* Profile Image - Click to Toggle Dropdown */}
      <button id="profileImage" onClick={toggleDropdown}>
        <Image
          src="/assets/images/user.png"
          alt="User Profile"
          width={50}
          height={50}
          className="rounded-full cursor-pointer w-[50px] h-[50px]"
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          id="dropdownMenu"
          className="absolute right-0 mt-2 w-[220px] bg-white shadow-lg rounded-lg border p-5 z-50"
        >
          <h2 className="font-bold text-[20px] text-[#4B207A] text-lg">Hi</h2>
          <h3 className="font-bold text-[20px] text-[#4B207A]">Dineshka.K</h3>
          <hr className="my-2" />
          <ul className="space-y-3 text-[#595959] text-[16px]">
          <Link href="/course">
          <li className="flex items-center space-x-4 cursor-pointer hover:text-purple-700">
            <GiBookmarklet /> <span>My Courses</span>
            </li>
          </Link>
           
            <li className="flex items-center space-x-4 cursor-pointer hover:text-purple-700">
            <FaUser /> <span>My Profile</span>
            </li>
            <li className="flex items-center space-x-4 cursor-pointer hover:text-purple-700">
            <BiSupport /> <span>Support</span>
            </li>
            <li className="flex items-center space-x-4 cursor-pointer hover:text-purple-700">
            <BsCartCheckFill /> <span>My Purchases</span>
            </li>
            <li className="flex items-center space-x-4 cursor-pointer hover:text-red-600">
            <FiLogOut /> <span>Log Out</span>
            </li>
          </ul>
        </div>
      )}
    </div>

       
            </div>
          
          
          </div>
        </div>

     
      </nav>
    </>
  );
};

export default CourseHeader;
