import { FaQuoteLeft, FaQuoteRight } from "react-icons/fa";

const Qutemain = () => {
  return (
    <div className="bg-[linear-gradient(90.3deg,#230A4D_-0.76%,#AEE08D_120.48%)] text-white py-8 px-4 text-center font-poppins">
      <div className="max-w-4xl mx-auto flex items-start justify-center gap-3">
        <FaQuoteLeft className="text-2xl md:text-[45px] text-white" />
        <p className="text-[18px] md:text-[20px] font-semibold">
        Empowering teams with cutting-edge skills through expert-led training. Elevate your workforce with expertise in AI, data science, and cloud computing.
        </p>
        <FaQuoteRight className="text-2xl md:text-[45px] text-white" />
      </div>
    </div>
  );
};

export default Qutemain;
