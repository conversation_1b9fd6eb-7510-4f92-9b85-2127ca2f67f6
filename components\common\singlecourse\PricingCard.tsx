'use client';

import React from 'react';

// TypeScript annotations can be added to the component if necessary, for example, for props or states.
// But in this case, there are no props or state variables being passed, so it's a stateless functional component.

const PricingCard: React.FC = () => {
  return (
    <div className="relative w-full flex flex-col items-center bg-white sm:mt-0 mt-10 font-poppins">
      <div className="relative mb-10 w-full h-[300px] md:h-[400px] bg-cover bg-center" 
           style={{ backgroundImage: "url('/pricing-bg.png')" }}>
        <div className="absolute inset-0 flex justify-center items-center">
          <div className="bg-gray-300 p-4 md:p-10 rounded-xl text-center max-w-[882px] shadow-lg m-10">
            <h2 className="text-sm md:text-[28px] font-poppins font-medium mb-2">Kickstart your upskilling journey with our Basic Plane
            The Beginner’s Choice!</h2>
           
            <h3 className="text-base md:text-[40px] font-poppins font-semibold text-black ">Basic Plan ₹ 200</h3>
            <button className="text-sm sm:text-2xl font-poppins mt-4 bg-[#0A5224] text-white px-3 py-2 sm:px-6 sm:py-2 rounded-[10px] hover:bg-green-800">Buy Now</button>
          </div>
        </div>
      </div>
      <div className="max-w-7xl w-full mb-10 hidden md:block">
        <div className="bg-gradient-to-r from-[#EEEEEE] via-[#FFFFFF] to-[#EEEEEE] py-2 px-4 md:px-10 flex flex-col md:flex-row justify-between items-center shadow-md">
          <p className="text-[24px] font-medium">Still Confused? Let us clear all your queries</p>
          <button className="mt-4 md:mt-0 bg-[#0A5224] text-white px-6 py-2 rounded-full hover:bg-green-800">Get Call Back</button>
        </div>
      </div>
    </div>
  );
};

export default PricingCard;
