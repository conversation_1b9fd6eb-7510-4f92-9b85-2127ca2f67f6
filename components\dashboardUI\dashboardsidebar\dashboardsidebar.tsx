import Image from "next/image";
import MainMenu from "./main-menu";

export default function Sidebar() {
  return (
    <div className="fixed top-[64px] left-0 h-screen w-full  md:w-[370px] flex flex-col z-50 px-[22px]"
         style={{
           background: "linear-gradient(0deg, #F2F2F2, #F2F2F2)",
           borderRight: "1px solid black"
         }}>
 
        <h1 className="font-poppins font-[700] text-[22px] md:text-[48px] text-[#4B207A] my-[20px] mt-[10px] ">
          EduTech
        </h1>
    
      <div>
        <div className="bg-[#f2f2f2] rounded-md py-3 px-3 border border-black flex items-center gap-4">
          <Image
            src="/assets/images/image(9).png"
            alt="Profile"
            width={65}
            height={65}
            className="rounded-full object-cover w-[65px] h-[65px]"
          />
          <div>
            <h3 className="font-medium text-[24px] mb-[5px] text-black leading-tight">Dineshka.K</h3>
            <p className="font-normal lg:font-semibold text-[14px] font-poppins text-[#585858] leading-tight">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>
      <div className="flex-1 mt-2">
        <MainMenu />
      </div>
    </div>
  );
}
