import { makeRequest } from "../utils";
import { useQuery, useMutation } from "@tanstack/react-query";

// ================== LESSON INTERFACES ==================
interface Lesson {
  id: string;
  title: string;
  video_url: string;
  notes_md: string;
  module: string;
  created_by: number;
  created_at: string;
}

interface GetLessonsResponse {
  message: string;
  data: Lesson[];
}

interface CreateLessonPayload {
  title: string;
  video_url: string;
  notes_md?: File[];
  quiz_files?: File[];
  project_description?: string;
  project_files?: File[];
}

interface CreateLessonResponse {
  message: string;
  data: Lesson;
}

interface UpdateLessonPayload {
  title?: string;
  video_url?: string;
  notes_md?: File[];
  quiz_files?: File[];
  project_description?: string;
  project_files?: File[];
}

interface UpdateLessonResponse {
  message: string;
  data: Lesson;
}

interface DeleteLessonResponse {
  message: string;
}

interface GetLessonResponse {
  message: string;
  data: Lesson;
}

// ================== GET LESSONS API ==================
async function getLessons(moduleId: string): Promise<GetLessonsResponse> {
  console.log("Calling GET lessons API for module:", moduleId);

  const response = await makeRequest({
    endpoint: `/api/modules/${moduleId}/lessons/`,
    method: "GET",
  });

  console.log("GET lessons API response:", response);
  return response;
}

// ================== GET SINGLE LESSON API ==================
async function getLesson(lessonId: string): Promise<GetLessonResponse> {
  console.log("Calling GET lesson API for lesson:", lessonId);

  const response = await makeRequest({
    endpoint: `/api/lessons/${lessonId}/`,
    method: "GET",
  });

  console.log("GET lesson API response:", response);
  return response;
}

// ================== CREATE LESSON API ==================
async function createLesson(moduleId: string, payload: CreateLessonPayload): Promise<CreateLessonResponse> {
  console.log("Calling CREATE lesson API for module:", moduleId, "with payload:", payload);

  try {
    // Create FormData for file upload
    const formData = new FormData();

    // Add basic fields
    formData.append("title", payload.title);
    formData.append("video_url", payload.video_url);

    // Add project description if provided
    if (payload.project_description) {
      formData.append("project_description", payload.project_description);
    }

    // Add notes files
    if (payload.notes_md && payload.notes_md.length > 0) {
      payload.notes_md.forEach((file, index) => {
        formData.append(`notes_md_${index}`, file, file.name);
      });
    }

    // Add quiz files
    if (payload.quiz_files && payload.quiz_files.length > 0) {
      payload.quiz_files.forEach((file, index) => {
        formData.append(`quiz_files_${index}`, file, file.name);
      });
    }

    // Add project files
    if (payload.project_files && payload.project_files.length > 0) {
      payload.project_files.forEach((file, index) => {
        formData.append(`project_files_${index}`, file, file.name);
      });
    }

    // Debug: Log FormData contents
    console.log("FormData contents:");
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }

    const response = await makeRequest({
      endpoint: `/api/modules/${moduleId}/lessons/`,
      method: "POST",
      data: formData,
      isFileUpload: true,
    });

    console.log("CREATE lesson API response:", response);
    return response;
  } catch (error) {
    console.error("CREATE lesson API error:", error);
    console.error("Payload that failed:", payload);
    console.error("ModuleId:", moduleId);
    throw error;
  }
}

// ================== UPDATE LESSON API ==================
async function updateLesson(lessonId: string, payload: UpdateLessonPayload): Promise<UpdateLessonResponse> {
  console.log("Calling UPDATE lesson API for lesson:", lessonId, "with payload:", payload);

  try {
    // Create FormData for file upload
    const formData = new FormData();

    // Add basic fields if provided
    if (payload.title) formData.append("title", payload.title);
    if (payload.video_url) formData.append("video_url", payload.video_url);
    if (payload.project_description) formData.append("project_description", payload.project_description);

    // Add notes files
    if (payload.notes_md && payload.notes_md.length > 0) {
      payload.notes_md.forEach((file, index) => {
        formData.append(`notes_md_${index}`, file, file.name);
      });
    }

    // Add quiz files
    if (payload.quiz_files && payload.quiz_files.length > 0) {
      payload.quiz_files.forEach((file, index) => {
        formData.append(`quiz_files_${index}`, file, file.name);
      });
    }

    // Add project files
    if (payload.project_files && payload.project_files.length > 0) {
      payload.project_files.forEach((file, index) => {
        formData.append(`project_files_${index}`, file, file.name);
      });
    }

    const response = await makeRequest({
      endpoint: `/api/lessons/${lessonId}/`,
      method: "PUT",
      data: formData,
      isFileUpload: true,
    });

    console.log("UPDATE lesson API response:", response);
    return response;
  } catch (error) {
    console.error("UPDATE lesson API error:", error);
    throw error;
  }
}

// ================== DELETE LESSON API ==================
async function deleteLesson(lessonId: string): Promise<DeleteLessonResponse> {
  console.log("Calling DELETE lesson API for lesson:", lessonId);

  const response = await makeRequest({
    endpoint: `/api/lessons/${lessonId}/delete/`,
    method: "DELETE",
  });

  console.log("DELETE lesson API response:", response);
  return response;
}

// ================== REACT QUERY HOOKS ==================
const useGetLessons = (moduleId: string) => {
  return useQuery<GetLessonsResponse, Error>({
    queryKey: ["lessons", moduleId],
    queryFn: () => getLessons(moduleId),
    enabled: !!moduleId, // Only run query if moduleId exists
  });
};

const useGetLesson = (lessonId: string) => {
  return useQuery<GetLessonResponse, Error>({
    queryKey: ["lesson", lessonId],
    queryFn: () => getLesson(lessonId),
    enabled: !!lessonId, // Only run query if lessonId exists
  });
};

const useCreateLesson = () => {
  return useMutation<CreateLessonResponse, Error, { moduleId: string; payload: CreateLessonPayload }>({
    mutationFn: ({ moduleId, payload }) => createLesson(moduleId, payload),
    onError: (error) => {
      console.error("Lesson creation error:", error);
    },
  });
};

const useUpdateLesson = () => {
  return useMutation<UpdateLessonResponse, Error, { lessonId: string; payload: UpdateLessonPayload }>({
    mutationFn: ({ lessonId, payload }) => updateLesson(lessonId, payload),
    onError: (error) => {
      console.error("Lesson update error:", error);
    },
  });
};

const useDeleteLesson = () => {
  return useMutation<DeleteLessonResponse, Error, string>({
    mutationFn: (lessonId: string) => deleteLesson(lessonId),
    onError: (error) => {
      console.error("Lesson deletion error:", error);
    },
  });
};

// ================== EXPORTS ==================
export {
  useGetLessons,
  useGetLesson,
  useCreateLesson,
  useUpdateLesson,
  useDeleteLesson,
  type Lesson,
  type CreateLessonPayload,
  type UpdateLessonPayload,
  type GetLessonsResponse,
  type GetLessonResponse,
  type CreateLessonResponse,
  type UpdateLessonResponse,
  type DeleteLessonResponse,
};
