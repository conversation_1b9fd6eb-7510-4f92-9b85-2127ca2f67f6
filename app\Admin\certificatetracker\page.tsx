
"use client";
import React, { useState } from "react";
import Header from "@/components/admindashobard/header";
import Sidebar from "@/components/admindashobard/sidebar";

import CertificateTrackerMain from "@/components/admindashobard/certificatetrackerui/CertificateTrackerMain";



export default function CertificateTrackerPage() {
  const [showSidebarMobile, setShowSidebarMobile] = useState(false);

  return (


    <div className=" bg-white">


      {/* Fixed Header */}
      <header className="fixed top-0 left-0 w-full h-[70px] bg-white z-40 shadow-sm">
        <Header showSidebarMobile={showSidebarMobile} setShowSidebarMobile={setShowSidebarMobile} />
      </header>

      {/* Mobile Sidebar Dropdown */}
      {showSidebarMobile && (
        <>
          <div
            className="fixed inset-0 bg-black opacity-40 z-40 md:hidden"
            onClick={() => setShowSidebarMobile(false)}
          />
          <div
            className={`fixed top-0 left-0 py-[70px] h-full max-w-[320px] bg-[#f2f2f2] border-r border-black z-50 md:hidden shadow-lg transform transition-transform duration-300 ease-in-out ${
              showSidebarMobile ? "translate-x-0" : "-translate-x-full"
            }`}
          >
            <Sidebar />
          </div>
        </>
      )}

      {/* Fixed Desktop Sidebar */}
      <aside className="hidden md:block fixed top-[70px] left-0 w-[369px] h-[calc(100vh-70px)] border-r border-black bg-[#f2f2f2] overflow-y-auto z-30">
        <Sidebar />
      </aside>

      {/* Main Content Area */}
    <main className="md:ml-[369px] pt-[70px] min-h-screen">
<CertificateTrackerMain/>
</main>
    </div>
  );
}
