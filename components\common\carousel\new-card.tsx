"use client"

import { useState, useEffect } from "react"
import { cn } from "@/api-services/utils"

interface CardData {
  id: number
  title: string
  points: string[]
  bgImage: string
}

const cardData: CardData[] = [
  {
    id: 1,
    title: "Preparation for Real-World Success",
    points: [
      "Apexiq Partasala's training is aligned with current industry needs.",
      "Courses equip students with practical abilities that match employer expectations.",
      "This targeted training prepares students for successful careers.",
    ],
    bgImage: "/assets/images/goal.png",
  },
  {
    id: 2,
    title: "Quality Education by Industry Experts",
    points: [
      "Courses are crafted by experienced professionals, combining theory with real-world skills.",
      "The curriculum emphasizes hands-on learning to build expertise.",
      "Students gain essential skills to meet industry demands.",
    ],
    bgImage: "/assets/images/industry.png",
  },
  {
    id: 3,
    title: "Hands-On Learning Approach",
    points: [
      "Apexiq Partasala emphasizes interactive learning, engaging students with the material.",
      "Hands-on activities and real-world scenarios help students gain experience.",
      "This approach builds confidence, preparing students to excel.",
    ],
    bgImage: "/assets/images/handson.png",
  },
]

export default function CardCarousel() {
  const [activeIndex, setActiveIndex] = useState<number>(0)
  const [isMobile, setIsMobile] = useState(false)
  const totalCards = cardData.length

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % totalCards)
    }, 2000)
    return () => clearInterval(interval)
  }, [totalCards])

  // Calculate cards to display
  const getCardClasses = (cardIndex: number) => {
    // For desktop view, we'll always render all cards but position them differently
    if (!isMobile) {
      const diff = (cardIndex - activeIndex + totalCards) % totalCards;
      
      // Center card
      if (diff === 0) {
        return "translate-x-0 z-30 scale-110 opacity-100 transition-smooth duration-200 ease-in-out";
      }
      // Left card
      else if (diff === totalCards - 1) {
        return "-translate-x-full z-20 scale-90 opacity-60 transition-smooth  duration-200 ease-in-out";
      }
      // Right card
      else if (diff === 1) {
        return "translate-x-full z-20 scale-90 opacity-60 transition-smooth duration-200 ease-in-out";
      }
      // Hidden cards
      else {
        return "translate-x-full z-10 opacity-0";
      }
    }
    
    // For mobile, simple center/left/right positioning
    const diff = (cardIndex - activeIndex + totalCards) % totalCards;
    if (diff === 0) return "translate-x-0";
    if (diff > 0) return "translate-x-full";
    return "-translate-x-full";
  }

  return (
    <div className="px-4 pb-8 sm:pt-16  relative">
      <div className="relative overflow-hidden  justify-center gap-4  w-full ">
        {/* Card container */}
        <div className="flex justify-center items-center relative h-[320px] md:h-[420px] p-5 rounded-md">
          {/* Render all cards but position them with transforms */}
          {cardData.map((card, idx) => (
            <div
              key={card.id}
              className={cn(
                "absolute w-full md:w-1/3 transition-all duration-200 ease-in-out",
                getCardClasses(idx)
              )}
            >
              <div
                className="shadow-[4px_4px_4px_0px_#00000040] p-6 transition-all duration-500 flex flex-col min-h-[290px] md:min-h-[420px] w-full"
                style={{
                  backgroundImage: `url(${card.bgImage})`,
                  backgroundSize: "70%",
                  backgroundPosition: "center",
                  backgroundColor: "#D9D9D9",
                  backgroundRepeat: "no-repeat",
                }}
              >
                <h3 className="font-semibold text-[20px] md:text-[24px] leading-[100%] tracking-[0%] text-center font-[Poppins] text-[#000000] mb-10">
                  {card.title}
                </h3>
                <ul className="space-y-2 md:space-y-3 flex-grow flex flex-col gap-3">
                  {card.points.map((point, pIdx) => (
                    <li key={pIdx} className="flex items-start gap-2 text-black font-poppins">
                      <span className="text-xs font-bold font-poppins mt-2">•</span>
                      <span className="text-xs md:text-lg font-normal font-poppins">{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Indicator Dots */}
      <div className="flex justify-center mt-10 space-x-2">
        {cardData.map((_, idx) => (
          <button
            key={idx}
            onClick={() => setActiveIndex(idx)}
            className={cn(
              "w-3 h-3 rounded-full transition-all",
              activeIndex === idx ? "bg-purple-700" : "bg-gray-300"
            )}
            aria-label={`Go to slide ${idx + 1}`}
          />
        ))}
      </div>
    </div>
  )
}