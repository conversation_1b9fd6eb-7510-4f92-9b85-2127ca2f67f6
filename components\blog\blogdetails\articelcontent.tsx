import React from 'react';

const ArticleContent = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-4 md:py-6 space-y-6 md:space-y-10 text-gray-800 text-justify font-poppins">
      {/* Section 1 */}
      <section id="personalized-learning">
        <h2 className="text-[20px] md:text-xl lg:text-[24px] font-semibold mb-2 md:mb-3">
          How AI makes personalized learning possible
        </h2>
        <p className="mb-3 md:mb-4 text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Dignissim lacus sit congue lacus aliquam. Ut non fermentum vulputate
          donec enim sed ornare scelerisque. Sollicitudin orci leo egestas
          fermentum platea a imperdiet nisl. Velit placerat nibh nisl ut
          feugiat. Egestas id egestas nunc mattis. Sed mauris vitae commodo
          pharetra, vestibulum rhoncus natoque.
        </p>
        <p className="text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Leo id aliquet potenti enim sed maecenas sed ornare. Risus nam mauris
          etiam nibh maecenas nibh interdum mauris suspendisse. Volutpat, enim
          interdum facilisi mauris vitae id. Vel malesuada sit etiam dolor.
          Ullamcorper habitant vitae, nec pretium ullamcorper viverra integer
          praesent tincidunt. At erat sagittis tellus ultricies in a.
        </p>
      </section>

      {/* Section 2 */}
      <section id="benefits-for-teachers">
        <h2 className="text-[20px] md:text-xl lg:text-[24px] font-semibold mb-2 md:mb-3">
          The hidden benefits of AI for teachers
        </h2>
        <p className="mb-3 md:mb-4 text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Massa, libero morbi morbi sed non sed. In et neque lectus ultricies
          leo eros. Auctor in elementum accumsan malesuada gravida neque cursus
          pellentesque nunc. Dui nullam odio neque varius massa praesent. Neque,
          porta vel, morbi nulla at tincidunt. Neque, et ultrices duis
          fermentum, tincidunt sem sed. Erat accumsan dui enim nisl morbi
          feugiat.
        </p>
        <p className="text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Ac eget lorem sit nulla sed fusce etiam. Elementum nibh in eu sed.
          Nibh non semper amet sit hac tristique orci. Quis velit vitae amet
          magnis nec in nunc. Amet vitae id cras nibh. Sed ipsum, lacinia in sit
          vitae. Duis luctus convallis risus purus sollicitudin purus id eu.
          Faucibus mattis eget ullamcorper eu suspendisse elit elit lacus purus.
          Tortor felis.
        </p>
      </section>

      {/* Section 3 */}
      <section id="ai-enhances-education">
        <h2 className="text-[20px] md:text-xl lg:text-[24px] font-semibold mb-2 md:mb-3">
          Why AI doesn’t replace education—it enhances it
        </h2>
        <p className="mb-3 md:mb-4 text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Massa aenean cursus nulla urna dui, fermentum cursus in facilisis.
          Vulputate euismod vestibulum dolor in elementum quis quis. Erat proin
          in eget arcu tellus ut ultrices. Quis maecenas ullamcorper ante sit
          leo placerat. Quisque dictum laoreet eget quam leo tortor scelerisque
          sit nullam. Lacus, tortor, aenean mattis lobortis lacinia mauris.
          Viverra nulla.
        </p>
        <p className="text-sm md:text-base lg:text-[20px] leading-[150%] md:leading-[160%] text-[#434343]">
          Sed pellentesque quam lorem urna. Mauris donec molestie eget massa
          pellentesque facilisis netus mauris. Magna eget eget sollicitudin at
          faucibus odio. At augue sit nisi et metus cras dignissim vel, tortor.
          Maecenas molestie consequat cursus posuere ultrices facilisis hac
          bibendum semper. Sed lorem vel donec proin. Volutpat aliquet rhoncus
          sit et.
        </p>
      </section>
    </div>
  );
};

export default ArticleContent;
