import React from "react";

const articles = [
  {
    title: "Top 10 Study Hacks Every Student Must Try in 2025 to Boost Productivity Like Never Before",
    description: "Struggling to focus or manage time while studying? In 2025, smart study hacks boost productivity and make learning more effective.",
    image: "/assets/about/ai.jpg", // Update with your actual path
    link: "#"
  },
  {
    title: "How is technology redefining classrooms and learning like never before?",
    description: "Technology is transforming classrooms with interactive tools, personalized learning, and global access, creating a more engaging and efficient education experience.",
    image: "/assets/about/ai.jpg",
    link: "#"
  }
];

const KeepReading = () => {
  return (
    <div className="my-8 md:my-12 px-4 max-w-4xl mx-auto font-poppins">
      <h2 className="text-xl md:text-[36px] font-semibold mb-6 text-[#303030]">Keep reading</h2>
      <div className="space-y-6">
        {articles.map((article, index) => (
          <a
            href={article.link}
            key={index}
            className="block md:flex md:gap-4 md:items-start hover:bg-gray-50 p-2 rounded-md transition"
          >
            {/* Image */}
            <img
              src={article.image}
              alt={article.title}
              className="w-full md:w-64 h-40 object-cover rounded-md shadow-sm md:flex-shrink-0 mb-3 md:mb-0"
              style={{boxShadow: '0px 5px 10px 0px #00000080'}}
            />

            {/* Content */}
            <div className="md:flex-1">
              <h3 className="font-semibold text-[#303030] text-base md:text-[24px] leading-[140%] md:leading-[160%] mb-2">
                {article.title}
              </h3>
              <p className="text-sm md:text-[18px] text-[#4F4F4F] leading-[140%] md:leading-[160%]">
                {article.description}
              </p>
            </div>
          </a>
        ))}
      </div>
    </div>
  );
};

export default KeepReading;
