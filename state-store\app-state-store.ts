
import { create } from "zustand";
type ActiveComponent = { id: string; type: string } | null;
type Store = {
  theme:  "light"|"dark" 
  setTheme: (theme: "dark" | "light") => void;

  
};

const getInitialTheme = (): "dark" | "light" => {
  // Always return light theme
  return "light";
};

export const useAppStore = create<Store>()((set) => ({


  theme: getInitialTheme(),
  setTheme: (theme) =>
    set(() => {
      if (typeof window !== "undefined") {
        localStorage.setItem("theme", "light"); // Force light theme
      }
      return { theme: "light" }; // Always return light
    }),

 
}));
