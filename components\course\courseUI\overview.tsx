export function OverviewContent() {
    return (
      <div className="space-y-4 font-poppins text-[#000000] font-normal text-[16px] leading-[28px] tracking-[-1%]  p-[10px]">
        <p>
          Prompt engineering is the art of crafting precise and effective inputs to guide AI models
          toward generating the desired output. It is like giving a chef detailed instructions for a recipe; the more
          specific and clear you are, the better the final dish will turn out.
        </p>
            <p>Understanding the Basics</p>
        <p>
          AI models, particularly large language models (LLMs), are trained on massive datasets of text and code. They
          learn to predict the next word or token in a sequence, enabling them to generate text that imitates languages,
          write different kinds of creative content, and answer your questions in an informative way. However, the quality
          of the output heavily relies on the quality of the input, which is where prompt engineering comes into play.
        </p>
  
        
      </div>
    )
  }
  