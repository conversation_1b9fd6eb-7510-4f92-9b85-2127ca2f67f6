"use client";
// import Image from "next/image";
import { useState } from "react";
import { Search,  X } from "lucide-react";
import CourseCard from "@/components/dashboardUI/mycoursecard/mycoursecard"

import PaidHeader from "@/components/layout/PaidHeader";
// import Link from "next/link";

export default function MyPurchase() {
  const [searchText, setSearchText] = useState("");

  const clearSearch = () => {
    setSearchText("");
  };

  const courses = [
    {
      id: 1,
      title: "<PERSON><PERSON>",
      instructor: "Dr<PERSON> <PERSON><PERSON><PERSON>",
      duration: "6h 30min",
      rating: 4.9,
      image: "/assets/images/lp3.png",
    },
    {
      id: 2,
      title: "Prompt Engineering",
      instructor: "Dr<PERSON> <PERSON><PERSON><PERSON>",
      duration: "6h 30min",
      rating: 4.9,
      image: "/assets/images/lp1.png",
    },
    {
      id: 3,
      title: "Data Analytics",
      instructor: "Dr. <PERSON><PERSON><PERSON>",
      duration: "6h 30min",
      rating: 4.9,
      image: "/assets/images/lp2.png",
    },
    {
        id: 4,
        title: "Html",
        instructor: "<PERSON><PERSON> <PERSON><PERSON><PERSON>",
        duration: "6h 30min",
        rating: 4.9,
        image: "/assets/images/lp5.png",
      },
      {
        id: 5,
        title: "Machine Learning",
        instructor: "Dr. Ruhiu",
        duration: "6h 30min",
        rating: 4.9,
        image: "/assets/images/lp4.png",
      },
      
     
  ];

  // Filter courses based on search text
  const filteredCourses = courses.filter((course) => {
    if (searchText.trim() === "") return true; // Show all courses when search is empty
    
    const searchLower = searchText.toLowerCase();
    return course.title.toLowerCase().includes(searchLower);
  });
   
  return (
    <>
     
    <PaidHeader />

      <main className="bg-white  md:px-[60px] pt-[60px]">
      <div className=" flex items-center justify-between pt-10 px-5  font-poppins">
            <h2 className="text-3xl font-semibold font-poppins">My Purchase</h2>
            
          </div>
      <div className="px-2 sm:px-4 flex flex-col sm:flex-row justify-between  w-full items-center">
  <div className="w-full flex justify-end   mb-8">
    <div className="relative mt-5 w-[300px] md:w-[551px] lg:w-[661px] h-[55px] lg:h-[61px] shadow-xl rounded-xl bg-[#ffffff] flex items-center border-none">
      <div className="bg-[#f1f1f1] w-[60px] h-full rounded-l-xl flex items-center justify-center md:justify-between  text-black">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#4B207A] h-5 w-5 sm:h-7 sm:w-7 font-poppins " />
      </div>
      <input
        type="text"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        placeholder="Search your courses"
        className="pl-2 pr-10  flex-grow rounded-r-xl text-sm sm:text-base focus:outline-none text-black ml-[50px] justify-center"
      />
      {searchText.length > 0 && (
        <button
          className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors text-black"
          onClick={clearSearch}
        >
          <X className="h-5 w-5 sm:h-6 sm:w-6 cursor-pointer" />
        </button>
      )}
    </div>
  </div>
</div>

       
<div className="pb-20">
          {filteredCourses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))}
        </div>
          
      </main>
    </>
  );
}