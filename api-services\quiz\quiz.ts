import { makeRequest } from "../utils";
import { useMutation } from "@tanstack/react-query";

// ================== QUIZ INTERFACES ==================
interface Quiz {
  id: string;
  title: string;
  lesson: string;
  quiz_file: string;
  created_by: number;
  created_at: string;
}

interface CreateQuizPayload {
  quiz_file: File;
}

interface CreateQuizResponse {
  message: string;
  data: Quiz;
}

// ================== CREATE QUIZ API ==================
async function createQuiz(lessonId: string, payload: CreateQuizPayload): Promise<CreateQuizResponse> {
  console.log("Calling CREATE quiz API for lesson:", lessonId, "with payload:", payload);

  if (!lessonId) {
    throw new Error("Lesson ID is required for quiz upload");
  }

  try {
    // Check if user has a valid token
    const token = localStorage.getItem("token");
    if (!token) {
      throw new Error("No authentication token found. Please log in again.");
    }

    // Create FormData for file upload
    const formData = new FormData();

    // Add the quiz file
    formData.append("quiz_file", payload.quiz_file, payload.quiz_file.name);

    // Debug: Log FormData contents
    console.log("FormData contents:");
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }

    console.log("Making request to endpoint:", `/api/lessons/${lessonId}/quiz/`);
    console.log("Using token:", token ? `${token.substring(0, 20)}...` : "No token");
    console.log("File details:", {
      name: payload.quiz_file.name,
      size: payload.quiz_file.size,
      type: payload.quiz_file.type
    });

    const response = await makeRequest({
      endpoint: `/api/lessons/${lessonId}/quiz/`,
      method: "POST",
      data: formData,
      isFileUpload: true,
    });

    console.log("CREATE quiz API response:", response);
    return response;
  } catch (error) {
    console.error("CREATE quiz API error:", error);
    console.error("Payload that failed:", payload);
    console.error("LessonId:", lessonId);

    // Check if it's a 403 Forbidden error
    if (error instanceof Error && error.message.includes("403")) {
      console.error("403 Forbidden - Authentication/Authorization issue");
      throw new Error("You don't have permission to upload quiz files. Please check your authentication or contact support.");
    }

    // Check if it's an HTML response (likely an error page)
    if (error instanceof Error && error.message.includes("DOCTYPE")) {
      console.error("Server returned HTML instead of JSON - likely an error page");
      throw new Error("Server error occurred while uploading quiz. Please try again or contact support.");
    }

    // Try alternative endpoint with trailing slash for 405 errors
    if (error instanceof Error && error.message.includes("Method") && error.message.includes("not allowed")) {
      console.log("Trying alternative endpoint with trailing slash...");
      try {
        const formData = new FormData();
        formData.append("quiz_file", payload.quiz_file, payload.quiz_file.name);

        const response = await makeRequest({
          endpoint: `/api/lessons/${lessonId}/quiz/`,
          method: "POST",
          data: formData,
          isFileUpload: true,
        });

        console.log("Alternative endpoint worked:", response);
        return response;
      } catch (altError) {
        console.error("Alternative endpoint also failed:", altError);
      }
    }

    throw error;
  }
}

// ================== REACT QUERY HOOKS ==================
const useCreateQuiz = () => {
  return useMutation<CreateQuizResponse, Error, { lessonId: string; payload: CreateQuizPayload }>({
    mutationFn: ({ lessonId, payload }) => createQuiz(lessonId, payload),
    onError: (error) => {
      console.error("Quiz creation error:", error);
    },
  });
};

// ================== EXPORTS ==================
export {
  useCreateQuiz,
  type Quiz,
  type CreateQuizPayload,
  type CreateQuizResponse,
};
