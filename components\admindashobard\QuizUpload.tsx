"use client";

import React, { useState, useRef } from "react";
import { FaTimes } from "react-icons/fa";
import { useCreateQuiz } from "@/api-services/quiz";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface QuizFile {
  id: string;
  name: string;
  size: string;
  file: File;
  uploaded: boolean;
}

interface QuizUploadProps {
  lessonId: string;
  courseId: string;
  onUploadComplete?: () => void;
}

const QuizUpload: React.FC<QuizUploadProps> = ({ lessonId, courseId, onUploadComplete }) => {
  const [quizFiles, setQuizFiles] = useState<QuizFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const createQuizMutation = useCreateQuiz();
  const queryClient = useQueryClient();

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const validFiles: QuizFile[] = [];
    Array.from(files).forEach((file) => {
      // Check if file is JSON
      if (file.type === "application/json" || file.name.endsWith('.json')) {
        // Check file size (20MB limit)
        if (file.size <= 20 * 1024 * 1024) {
          validFiles.push({
            id: Math.random().toString(36).substr(2, 9),
            name: file.name,
            size: (file.size / (1024 * 1024)).toFixed(2) + " MB",
            file: file,
            uploaded: false,
          });
        } else {
          toast.error(`File ${file.name} is too large. Maximum size is 20MB.`);
        }
      } else {
        toast.error(`File ${file.name} is not a valid JSON file.`);
      }
    });

    setQuizFiles((prev) => [...prev, ...validFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (fileId: string) => {
    setQuizFiles((prev) => prev.filter((file) => file.id !== fileId));
  };

  const uploadAllFiles = async () => {
    if (quizFiles.length === 0) {
      toast.error('Please select at least one quiz file to upload.');
      return;
    }

    setIsUploading(true);
    let successCount = 0;
    let errorCount = 0;

    for (const quizFile of quizFiles) {
      if (quizFile.uploaded) continue;

      try {
        await createQuizMutation.mutateAsync({
          lessonId,
          payload: { quiz_file: quizFile.file },
        });

        // Mark file as uploaded
        setQuizFiles((prev) =>
          prev.map((file) =>
            file.id === quizFile.id ? { ...file, uploaded: true } : file
          )
        );

        successCount++;
      } catch (error) {
        console.error(`Error uploading ${quizFile.name}:`, error);

        // Show specific error message to user
        if (error instanceof Error) {
          if (error.message.includes("permission") || error.message.includes("403")) {
            toast.error(`Permission denied: You don't have access to upload quiz files.`);
          } else if (error.message.includes("authentication") || error.message.includes("token")) {
            toast.error(`Authentication error: Please log in again.`);
          } else if (error.message.includes("Server error")) {
            toast.error(`Server error: Please try again later.`);
          } else {
            toast.error(`Failed to upload ${quizFile.name}: ${error.message}`);
          }
        } else {
          toast.error(`Failed to upload ${quizFile.name}`);
        }

        errorCount++;
      }
    }

    setIsUploading(false);

    if (successCount > 0) {
      toast.success(`${successCount} quiz file(s) uploaded successfully!`);
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["course-detail", courseId] });
      onUploadComplete?.();
    }

    // Don't show generic error message since we show specific errors above
    if (errorCount > 0 && successCount === 0) {
      // Only show generic message if all files failed and no specific errors were shown
      console.log(`${errorCount} file(s) failed to upload. Specific errors shown above.`);
    }
  };

  const totalSize = quizFiles.reduce((total, file) => {
    return total + file.file.size;
  }, 0);

  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  const isOverLimit = totalSize > 100 * 1024 * 1024; // 100MB limit

  return (
    <div className="mb-8">
      <label className="block text-base font-medium mb-3">
        Upload Quiz <span className="text-gray-500 text-sm font-normal">(Optional):</span>
      </label>

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 cursor-pointer transition-colors ${
          isDragOver
            ? "border-blue-400 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="flex flex-col items-center justify-center text-center">
          {/* JSON Icon */}
          <div className="w-12 h-12 mb-4 flex items-center justify-center bg-gray-100 rounded">
            <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M5,3H7V5H5V10A2,2 0 0,1 3,8V6A2,2 0 0,1 5,4V3M19,3V4A2,2 0 0,1 21,6V8A2,2 0 0,1 19,10V5H17V3H19M5,21V20A2,2 0 0,1 3,18V16A2,2 0 0,1 5,14V19H7V21H5M19,21H17V19H19V14A2,2 0 0,1 21,16V18A2,2 0 0,1 19,20V21Z" />
            </svg>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept=".json,application/json"
            multiple
            className="hidden"
            onChange={handleFileInputChange}
          />

          <p className="text-sm text-blue-600 underline mb-1 cursor-pointer">
            Choose file or Drag here
          </p>
          <p className="text-xs text-gray-500 mb-1">
            Supported file type(s) : .JSON
          </p>
          <p className="text-xs text-gray-500">
            Size limit 20MB per file, up to 20 file(s) with total file size not exceeding 100MB
          </p>
        </div>
      </div>

      {/* Files List */}
      {quizFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          {quizFiles.map((file, index) => (
            <div
              key={file.id}
              className="flex items-center justify-between border border-gray-300 rounded-md px-4 py-3"
            >
              <div className="flex items-center gap-3">
                <span className="text-gray-600 font-medium">{index + 1}.</span>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                  <span className="text-blue-600 underline text-sm">
                    {file.name}
                  </span>
                  <span className="text-gray-500 text-sm">( {file.size} )</span>
                  {file.uploaded && (
                    <span className="text-green-600 text-xs bg-green-100 px-2 py-1 rounded">
                      Uploaded
                    </span>
                  )}
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeFile(file.id);
                }}
                className="text-red-500 hover:text-red-700"
                disabled={file.uploaded}
              >
                <FaTimes />
              </button>
            </div>
          ))}

          {/* Total Size Warning */}
          {isOverLimit && (
            <div className="text-red-500 text-sm mt-2">
              ⚠️ Total file size ({totalSizeMB} MB) exceeds 100MB limit
            </div>
          )}
        </div>
      )}

      {/* Upload Button */}
      {quizFiles.length > 0 && (
        <div className="flex justify-end mt-4">
          <button
            onClick={uploadAllFiles}
            disabled={isUploading || isOverLimit || quizFiles.every(f => f.uploaded)}
            className="px-6 py-2 border border-green-600 text-green-600 rounded text-sm font-medium hover:bg-green-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isUploading ? 'Uploading...' : 'Upload'}
          </button>
        </div>
      )}
    </div>
  );
};

export default QuizUpload;
