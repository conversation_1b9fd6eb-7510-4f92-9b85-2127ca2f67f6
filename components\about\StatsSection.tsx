import { statsData } from '@/utils/constant';
import React from 'react';




const StatsSection: React.FC = () => {
  return (
    <div className="bg-gradient-to-r from-[#230A4D] via-gray-600 to-[#AEE08D] text-white py-9 px-6 shadow-lg">
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-6xl mx-auto text-center">
        {statsData.map((stat, index) => (
          <div
            key={index}
            className={`${
              index !== statsData.length - 1 ? 'border-r border-white/30' : ''
            }`}
          >
            <h3 className="text-xl sm:text-2xl font-semibold">{stat.number}</h3>
            <p className="text-sm sm:text-base font-medium">{stat.label}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StatsSection;
