"use client";
import { facultyMembers } from "@/utils/constant";
import Image from "next/image";
import { FaBriefcase } from "react-icons/fa6";
import { GiTeacher } from "react-icons/gi";
import { GrLinkedin } from "react-icons/gr";




// Faculty component definition with type annotations
const Faculty: React.FC = () => {
  return (
    <div className="max-w-6xl mx-auto sm:py-10 px-7 sm:px-4 ">
      <h2 className="text-xl sm:text-3xl md:text-[40px] font-semibold sm:text-center sm:mb-3 text-black font-poppins">Expert Guidance: Our Esteemed Faculty</h2>
      <p className="text-base md:text-[24px] font-medium text-[#323232] sm:text-center mt-1 mb-4 sm:mb-8 font-poppins">
        Gain invaluable mentorship from accomplished industry professionals.
      </p>
      <div className="overflow-x-auto lg:overflow-visible sm:px-8">
        <div className="flex justify-start sm:justify-center gap-8 lg:gap-20 flex-nowrap lg:flex-row mb-10">
          {facultyMembers.map((member, index) => (
            <div
              key={index}
              className="bg-[#d9d9d9] rounded-xl shadow-lg overflow-hidden lg:w-96 w-80 flex-shrink-0"
            >
              <div className="relative w-full h-64">
                <Image
                  src={member.image}
                  alt={member.name}
                  layout="fill"
                  objectFit="cover"
                />
              </div>
              <div className="p-4 sm:p-6 bg-gray-100 font-poppins">
                <div className="flex items-center justify-between">
                  <h3 className="text-base sm:text-2xl font-semibold">{member.name}</h3>
                  <a href={member.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-600 font-bold text-lg">
                    <GrLinkedin size={30} />
                  </a>
                </div>
                <p className="text-sm sm:text-base text-gray-900">{member.degree}</p>
                <div className="mt-3 text-xs sm:text-lg flex flex-col gap-1 sm:gap-3">
                  <p className="font-semibold flex items-center text-black text-[16px]">
                    <FaBriefcase className="sm:w-6 sm:h-6 text-black mr-2 md:mr-3" /> Working Experience: <span className="text-[#373737] text-[14px]"> {member.experience}</span> 
                  </p>
                  <p className="font-semibold flex items-center text-[16px]">
                    <GiTeacher className="sm:w-6 sm:h-6 text-black mr-2 md:mr-3" /> Teaching Experience: <span className="text-[#373737] text-[14px]"> {member.teaching}</span>
                  </p>
                </div>
                <p className="mt-4 text-[16px] font-semibold text-black">{member.skills}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Faculty;
