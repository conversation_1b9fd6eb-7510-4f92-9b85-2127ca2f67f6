import { ChevronLeft, Download } from "lucide-react"
import Link from "next/link"

export function CourseHeader() {
  return (
    <header className="flex flex-col md:flex-row items-center justify-between px-4 py-3 bg-[#f5f5f5] shadow-lg mb-2">
      {/* Left side with title */}
      <Link href="/mycourse" className="w-full">
  <div className="flex items-center md:ml-[40px] w-full cursor-pointer md:mb-0">
    <ChevronLeft className="w-5 h-5 md:w-6 md:h-6 mr-1" />
    <h1 className="font-[Poppins] font-medium text-[13px] md:text-[20px] leading-[100%] tracking-[-0.01em] text-[#000000]">
      Introduction to Prompt Engineering
    </h1>
  </div>
</Link>
      
      {/* Right side with progress */}
      <div className="flex items-center flex-shrink-0 space-x-2 sm:space-x-3">
        <span className="text-xs sm:text-sm md:text-[14px] font-light sm:font-normal md:font-medium font-poppins whitespace-nowrap text-[#000000]">
          Course progress
        </span>
        <div className="relative w-[24px] h-[24px] sm:w-[32px] sm:h-[32px] md:w-[36px] md:h-[36px] rounded-full border-2 sm:border-3 border-amber-400 bg-gray-100 flex items-center justify-center flex-shrink-0">
          <span className="text-xs md:text-sm font-medium text-[#000000]">0%</span>
        </div>
        <button className="rounded-full bg-[#b3b0b0] w-[28px] h-[28px] sm:w-[32px] sm:h-[32px] md:w-[34px] md:h-[34px] flex items-center justify-center flex-shrink-0 ml-2 sm:ml-3">
          <Download className="w-[16px] h-[16px] sm:w-[20px] sm:h-[20px] md:w-[24px] md:h-[24px]" />
        </button>
      </div>
    </header>
  )
}