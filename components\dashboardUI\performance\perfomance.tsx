'use client'

import React from 'react';
import Badges from './badges';
import WelcomeHeader from '../main-container/welcome-header';

interface Course {
  name: string;
  videoProgress: number;
  quiz: string;
  project: string;
  score: number;
}

export default function Performance() {
  const courses: Course[] = [
    { name: 'MySQL', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
    { name: 'Python', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
    { name: 'Machine Learning', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
    { name: 'Computer Vision', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
    { name: 'MongoD<PERSON>', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
    { name: 'PHP', videoProgress: 60, quiz: '5/10', project: '8/10', score: 50 },
  ];

  return (
    <div className="w-full flex flex-col justify-center items-center pt-[30px] md:py-[70px] px-4 md:px-10">
      {/* Welcome Section */}
      <WelcomeHeader />

      {/* Dashboard Content */}
      <div className="w-full max-w-screen-2xl mt-4 md:mt-8">
        
        {/* Courses Table */}
        <div className="w-full overflow-x-auto shadow-md rounded-lg">
          <table className="min-w-[700px] w-full border border-black text-center text-sm md:text-base lg:text-lg">
            <thead>
              <tr className="bg-[#9531ff] text-white font-poppins font-medium">
                <th className="p-3 md:p-4 border border-black">Course Name</th>
                <th className="p-3 md:p-4 border border-black">Video Progress</th>
                <th className="p-3 md:p-4 border border-black">Quiz</th>
                <th className="p-3 md:p-4 border border-black">Project</th>
                <th className="p-3 md:p-4 border border-black">Score</th>
              </tr>
            </thead>
            <tbody className="font-poppins text-black">
              {courses.map((course, index) => (
                <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-100'}`}>
                  <td className="p-2 md:p-4 border border-black">{course.name}</td>
                  <td className="p-2 md:p-4 border border-black">{course.videoProgress}%</td>
                  <td className="p-2 md:p-4 border border-black">{course.quiz}</td>
                  <td className="p-2 md:p-4 border border-black">{course.project}</td>
                  <td className="p-2 md:p-4 border border-black">{course.score}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Achievement Badges Section */}
        <div className="mt-8 md:mt-12">
          <Badges />
        </div>
      </div>
    </div>
  );
}
