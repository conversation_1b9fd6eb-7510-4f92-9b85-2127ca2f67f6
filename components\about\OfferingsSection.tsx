import Image from 'next/image';
import React from 'react';

const OfferingsSection = () => {
  return (
    <section className=" py-10 px-4 sm:px-6 lg:px-8 text-center pt-0">
        <div className="max-w-[974px] mx-auto">
        <h2 className="text-xl sm:text-4xl font-semibold font-poppins text-black mb-4">
        Our Offerings
      </h2>
      <p className="text-sm sm:text-base font-normal text-black mb-12 max-w-4xl mx-auto">
        Our offerings provide a dynamic learning experience with expert-led courses, real-world projects, AI-powered personalization, and a supportive community to enhance skill development.
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-12 gap-x-24 px-6 sm:px-20">
        {/* Card 1 */}
        <div className="bg-gray-100 p-6 rounded-2xl shadow-md flex flex-col items-start hover:bg-[#C6B8EB] transition">
          <div className="flex items-center space-x-4 mb-4">
            <Image src="/assets/images/choose-structured.png" alt="Structured Learning Paths" width={30} height={30} className="object-contain" />
            <h3 className="text-sm sm:text-lg font-semibold font-poppins text-[#2C2C2C] text-left">
              Structured Learning Paths
            </h3>
          </div>
          <p className="text-[#2C2C2C] text-xs sm:text-sm text-left font-poppins">
            Guided programs designed for career success.
          </p>
        </div>

        {/* Card 2 */}
        <div className="bg-gray-100 p-6 rounded-2xl shadow-md flex flex-col items-start hover:bg-[#C6B8EB] transition">
          <div className="flex items-center space-x-4 mb-4">
            <Image src="/assets/images/choose-industry.png" alt="Industry-Relevant Curriculum" width={30} height={30} className="object-contain" />
            <h3 className="text-sm sm:text-lg font-semibold font-poppins text-[#2C2C2C] text-left">
              Industry-Relevant Curriculum
            </h3>
          </div>
          <p className="text-[#2C2C2C] text-xs sm:text-sm text-left font-poppins">
            Stay ahead with market-demand skills.
          </p>
        </div>

        {/* Card 3 */}
        <div className="bg-gray-100 p-6 rounded-2xl shadow-md flex flex-col items-start hover:bg-[#C6B8EB] transition">
          <div className="flex items-center space-x-4 mb-4">
            <Image src="/assets/images/choose-real.png" alt="Real-World Projects"width={30} height={30} className="object-contain" />
            <h3 className="text-sm sm:text-lg font-semibold font-poppins text-[#2C2C2C] text-left">
              Real-World Projects
            </h3>
          </div>
          <p className="text-[#2C2C2C] text-xs sm:text-sm text-left font-poppins">
            Enhance skills through hands-on applications.
          </p>
        </div>

        {/* Card 4 */}
        <div className="bg-gray-100 p-6 rounded-2xl shadow-md flex flex-col items-start hover:bg-[#C6B8EB] transition">
          <div className="flex items-center space-x-4 mb-4">
            <Image src="/assets/images/choose-expert.png" alt="Expert Mentorship" width={30} height={30} className="object-contain" />
            <h3 className="text-sm sm:text-lg font-semibold font-poppins text-[#2C2C2C] text-left">
              Expert Mentorship
            </h3>
          </div>
          <p className="text-[#2C2C2C] text-xs sm:text-sm text-left font-poppins">
            Direct guidance from industry professionals.
          </p>
        </div>
      </div>
        </div>
        <div className="max-w-[1065px] mx-auto text-center ">
    <hr className="mt-8 sm:mt-20 border-black border-b-1 " />
    </div>

  
    </section>
  );
};

export default OfferingsSection;
