import Image from "next/image";
import React from "react";

const MissionVision = () => {
  return (
    <section className=" py-10 px-5  font-poppins">
    <div className="container border-b-2   border-gray-500  ">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center pb-[40px] md:pb-[80px]">
        {/* Mission Section */}
        <div className="order-2 md:order-1">
          <h2 className="text-2xl md:text-3xl font-bold text-[#1A2434]">
            Our <span className="text-[#6E1EA3]">Mission</span>
          </h2>
          <p className="text-black mt-4 text-base md:text-[20px] leading-relaxed">
            Our mission is to redefine education by making it accessible, engaging, and
            practical through cutting-edge AI-powered learning. We strive to bridge the gap 
            between theoretical knowledge and real-world application, ensuring that every 
            learner gains future-ready skills to thrive in a rapidly evolving digital landscape.
          </p>
        </div>
        <div className="order-1 md:order-2 md:pl-[70px]">
          <Image
          height={462}
          width={406}
            src="/assets/images/mission.png"
            alt="Mission"
            className="w-full md:w-[462px] rounded-lg shadow-lg"
          />
        </div>

        {/* Vision Section */}
        <div className="md:pr-[70px]">
          <Image
           height={462}
           width={406}
            src="/assets/images/vission.png"
            alt="Vision"
            className="w-full md:w-[462px] rounded-lg shadow-lg"
          />
        </div>
        <div>
          <h2 className="text-2xl md:text-3xl font-bold text-[#1A2434]">
            Our <span className="text-[#6E1EA3]">Vision</span>
          </h2>
          <p className="text-black mt-4 text-base md:text-[20px]  leading-relaxed">
            We envision ApexIQ as a global leader in smart learning solutions, empowering 
            individuals with the tools, knowledge, and confidence to innovate, solve complex 
            problems, and lead the future. By fostering a diverse and collaborative learning 
            community, we aim to shape a world where education knows no boundaries and every 
            learner unlocks their full potential.
          </p>
        </div>
      </div>
    </div>
     
    </section>
  );
};

export default MissionVision;
