"use client"
import React from 'react';
import { Search } from 'lucide-react';

interface ResourcesSectionProps {
  search: string;
  setSearch: (value: string) => void;
}

const ResourcesSection: React.FC<ResourcesSectionProps> = ({ search, setSearch }) => {
  return (
    <section className="w-full  px-4 py-16 bg-gradient-to-r from-[#9F6CEC] to-[#C4B0E4]">
      <div className="max-w-3xl mx-auto text-center">
        <h2 className="text-3xl md:text-5xl font-bold text-gray-900">
          Resources and insights
        </h2>
        <p className="mt-4 text-base md:text-lg text-gray-800">
          Stay ahead with the latest in EdTech – industry insights, expert interviews,
          innovative tools, and powerful resources transforming the future of learning.
        </p>

        {/* Search bar */}
        <div className="mt-6 flex justify-center">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 w-6 h-6" />
            <input
              type="text"
              placeholder="Search by Topic"
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="w-full h-12 pl-12 pr-4 bg-white rounded-xl shadow-md border-none text-base text-gray-600 placeholder-gray-600 focus:outline-none"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResourcesSection;
