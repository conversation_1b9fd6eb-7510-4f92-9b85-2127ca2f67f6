import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import { EffectCoverflow, Pagination } from 'swiper/modules';
import './Sliding.css';

const Sliding = () => {
  const cardData = [
    { title: 'Card 1', description: ['Description 1 for Card 1', 'Description 2 for Card 1'] },
    { title: 'Card 2', description: ['Description 1 for Card 2', 'Description 2 for Card 2'] },
    { title: 'Card 3', description: ['Description 1 for Card 3', 'Description 2 for Card 3'] },
    { title: 'Card 4', description: ['Description 1 for Card 4', 'Description 2 for Card 4'] },
  ];

  return (
    <div className="px-11"> {/* Add padding to the container */}
      <Swiper 
        effect={'coverflow'}
        grabCursor={true}
        centeredSlides={true}
        loop={false}
        slidesPerView={'auto'}
        initialSlide={1} // Set the third slide as the initial slide
        spaceBetween={30} // Add space between slides
        coverflowEffect={{
          rotate: 20,
          stretch: 0,
          depth: 100,
          modifier: 1,
          slideShadows: true,
        }}
        // pagination={{ clickable: true }}
        modules={[EffectCoverflow, Pagination]}
      >
        {cardData.map((card, index) => (
          <SwiperSlide key={index} className="swiper-slide-custom">
            <div className="p-4">
              <h3 className="text-xl font-semibold mb-2">{card.title}</h3>
              <ul className="list-disc list-inside">
                {card.description.map((desc, idx) => (
                  <li key={idx} className="text-sm text-gray-600">{desc}</li>
                ))}
              </ul>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Sliding;