import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const CarouselCard = () => {
  const settings = {
    dots: true,
    infinite: true, // Enables cyclic looping
    speed: 1000,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    centerMode: true, // Centers the main card
    centerPadding: '0px', // Ensures cards are in the middle of the screen
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          infinite: true,
          dots: true,
          centerMode: true,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          centerMode: true,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          centerMode: true,
        },
      },
    ],
  };

  const cardData = [
    { title: 'Card 1', content: ['Item 1', 'Item 2', 'Item 3', 'Item 4'] },
    { title: 'Card 2', content: ['Item 1', 'Item 2', 'Item 3', 'Item 4'] },
    { title: 'Card 3', content: ['Item 1', 'Item 2', 'Item 3', 'Item 4'] },
    { title: 'Card 4', content: ['Item 1', 'Item 2', 'Item 3', 'Item 4'] },
  ];

  return (
    <div className="w-3/4 mx-auto p-8 relative z-10"> {/* Main frame centered with positive z-index */}
      <Slider {...settings}>
        {cardData.map((card, index) => (
          <div key={index} className="px-2 relative"> {/* Position Relative */}
            <div className="bg-gray-300 rounded-lg shadow-md p-6 h-full text-black relative transform scale-100 hover:scale-105 transition-transform duration-300"> {/* Gray Card Background */}
              <h3 className="text-xl font-semibold mb-4 text-center">
                {card.title}
              </h3>
              <ul className="list-disc list-inside space-y-2">
                {card.content.map((item, idx) => (
                  <li key={idx} className="text-black flex items-center">
                    <span className="text-black-500 mr-2 ">•</span> {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default CarouselCard;
