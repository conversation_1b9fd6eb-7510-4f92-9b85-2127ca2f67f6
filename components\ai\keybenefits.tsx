import React from 'react';
import Image from 'next/image';

const KeyBenefits: React.FC = () => {
  return (
    <div className="max-w-7xl mx-auto px-4">
      {/* Key Benefits Section */}
      <section className="mb-12">
        <h2 className="text-xl sm:text-3xl lg:text-3xl font-poppins font-semibold text-black mt-10 sm:mt-0 mb-8">
          Key Benefits
        </h2>

        {/* On small screens: stacked; on md+: row */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 md:gap-8">
          
          {/* Left Column */}
          <div className="flex flex-col space-y-10">
            <div className="bg-[#d7d7d7]  shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:ml-0 md:mr-20">
              Comprehensive <br />Understanding of AI
            </div>
            <div className="bg-[#d7d7d7]  shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:ml-10 md:mr-10">
              Comprehensive <br />Understanding of AI
            </div>
            <div className="bg-[#d7d7d7] shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:ml-20 md:mr-0">
              Comprehensive <br />Understanding of AI
            </div>
          </div>

          {/* Center Image */}
          <div className="flex justify-center w-fit">
            <Image
              src="/assets/images/undraw_personal_file_re_5joy1.png"
              alt="Person with document"
              className="w-fit h-[385px]"
              width={300} 
              height={300} 
            />
          </div>

          {/* Right Column */}
          <div className="flex flex-col space-y-10">
            <div className="bg-[#d7d7d7] shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:mr-0 md:ml-20">
              Comprehensive <br />Understanding of AI
            </div>
            <div className="bg-[#d7d7d7]  shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:mr-10 md:ml-10">
              Comprehensive <br />Understanding of AI
            </div>
            <div className="bg-[#d7d7d7]  shadow-lg px-20 py-4 rounded-lg text-center text-xs sm:text-xl font-bold
                            md:mr-20 md:ml-0">
              Comprehensive <br />Understanding of AI
            </div>
          </div>

        </div>
      </section>

      {/* Divider */}
      <div className="w-4/5 mx-auto border-b-2 border-black mt-10 sm:mt-20"></div>
      
      {/* Related Topics Section */}
      <section className="mt-10 mb-10">
        <h2 className="text-xl sm:text-3xl lg:text-3xl font-poppins font-semibold text-black mb-4 sm:mb-8">
          Related Topics
        </h2>

        {/* Outer container for horizontal scroll */}
        <div className="overflow-x-auto"> 
          <div className="flex flex-nowrap sm:flex-wrap gap-4 lg:gap-10 whitespace-nowrap"> 
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Data Science
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Deep Learning
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Machine Learning
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Prompt Engineering
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              DevOps
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Cloud Computing
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              Generative AI
            </span>
            <span className="bg-white border border-black px-4 py-2 rounded-md text-sm sm:text-xl font-medium hover:bg-[#4B207A] hover:text-white cursor-pointer">
              ChatGPT for Dev IT
            </span>
          </div>
        </div>
      </section>

    </div>
  );
};

export default KeyBenefits;
