"use client";
import { useState } from "react";
import dynamic from "next/dynamic";
import { pdfjs } from "react-pdf";
import { Page } from "react-pdf";
import Image from "next/image";

// Configure PDF.js Worker for Next.js (client-side)
if (typeof window !== "undefined") {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

const PDFViewer = dynamic(() => import("react-pdf").then((mod) => mod.Document), { ssr: false });

interface Module {
  name: string;
  duration: string;
  pdf: string;
  nextModule: string;
}

const ModulePDFViewer: React.FC = () => {
  const [selectedModule, setSelectedModule] = useState<string>("module1");

  const modules: { [key: string]: Module } = {
    module1: { name: "Programming Language Fundamentals", duration: "2 Months", pdf: "/module1.pdf", nextModule: "MODULE 2 : Data Structure & Algorithm" },
    module2: { name: "Data Structures & Algorithms", duration: "2 Months", pdf: "/module2.pdf", nextModule: "MODULE 3 : SQL & Databases" },
    module3: { name: "SQL & Databases", duration: "0.5 Months", pdf: "/module3.pdf", nextModule: "MODULE 4 : LLD and Project Specializations" },
    module4: { name: "LLD and Project Specializations", duration: "2 Months", pdf: "/module4.pdf", nextModule: "MODULE 1 : Programming Language Fundamentals" },
  };

  return (
    <div className="flex flex-col md:flex-row bg-gray-100 lg:px-16 lg:py-10 rounded-lg shadow-md">
      {/* Left Section - Module Selection */}
      <div className="w-full md:w-1/2 p-4">
        <ul className="space-y-4 sm:space-y-8">
          {Object.keys(modules).map((moduleKey, index) => (
            <li key={index}>
              <button
                className={`w-full flex items-center px-6 py-4 rounded-lg transition-all duration-300 
                  ${selectedModule === moduleKey
                    ? "bg-purple-200 text-black shadow-md relative"
                    : "bg-white text-gray-800 border"
                  }`}
                onClick={() => setSelectedModule(moduleKey)}
              >
                {/* Left Purple Border for Active Button */}
                {selectedModule === moduleKey && (
                  <span className="absolute left-0 h-full w-2 bg-purple-700 rounded-l-lg"></span>
                )}
                <div className="flex flex-col pl-4">
                  <span className="text-[10px] sm:text-xs font-bold text-start uppercase">Module {index + 1}</span>
                  <span className="text-[13px] sm:text-lg font-semibold text-start">{modules[moduleKey].name}</span>
                </div>
                <span className="ml-auto text-[10px] sm:text-sm text-gray-700">{modules[moduleKey].duration}</span>
              </button>
            </li>
          ))}
        </ul>
      </div>

      {/* Right Section - PDF Viewer */}
      <div className="hidden md:block w-full md:w-1/2 p-4 bg-white shadow-lg mt-4 rounded-lg relative">
        <div className="flex items-center justify-between">
         <Image
                        width={142}
                        height={62}
                        className="h-10 sm:h-12 w-auto cursor-pointer"
                        src="/assets/images/logo.png"
                        alt="Logo"
                      />
                       <Image
                        width={297}
                        height={96}
                        className="absolute top-0 right-0"
                        src="/assets/images/pdfshape.png"
                        alt="Logo"
                      />
        </div>
        <div className="max-w-[283px]">
        <h3 className="text-[20px] font-semibold text-gray-900 mt-2 mb-3 font-poppins">{modules[selectedModule].name}</h3>
        </div>
       
        <hr className="my-3 border-gray-300" />
        <div className="h-[300px] border">
          <PDFViewer file={modules[selectedModule].pdf}>
            <Page pageNumber={1} />
          </PDFViewer>
        </div>
        {/* Read Next Module Link */}
        <div className="mt-4 border-t pt-3">
          <a href="#" className="text-blue-600 underline text-md font-medium">
            Read Next: {modules[selectedModule].nextModule}
          </a>
        </div>
        <div className="mt-3 text-center">
        <button className="text-sm sm:text-2xl font-poppins mt-4 bg-[#0F9340] text-white px-3 py-2 sm:px-6 sm:py-2 rounded-[10px] hover:bg-green-800">Download Curriculum</button>
        </div>
      </div>
    </div>
  );
};

export default ModulePDFViewer;
