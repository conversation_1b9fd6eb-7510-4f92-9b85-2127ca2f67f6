"use client";
import React, { useState } from "react";
import { ChevronLeft, ChevronRight, Pencil } from "lucide-react";
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, addMonths, subMonths, isSameMonth, isSameDay } from "date-fns";
import NotePopup from "@/components/dashboardUI/main-container/notepopup";

const notes = {
  "2025-01-16": 'Last Date for complete your Project in Course “Python”',
};

export default function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date("2025-01-16"));
  const [isPopupOpen, setPopupOpen] = useState(false);

  const Header = () => (
    <div className="flex justify-between items-center  p-2">
      <h2 className="font-poppins font-semibold text-[24px] leading-[22px] tracking-[-0.02em] text-black p-5" >
        {format(currentDate, "MMMM yyyy")}
      </h2>
      <div className="flex items-center gap-4">
        <button onClick={() => setCurrentDate(subMonths(currentDate, 1))}>
          <ChevronLeft />
        </button>
        <button onClick={() => setCurrentDate(addMonths(currentDate, 1))}>
          <ChevronRight />
        </button>
      </div>
    </div>
  );

  const DaysOfWeek = () => (
    <div className="grid grid-cols-7 text-md text-center text-black bg-[#e0e0e0] py-3">
      {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
        <div key={day} className="font-poppins font-medium text-[14px]">{day}</div>
      ))}
    </div>
  );

  const generateDates = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const start = startOfWeek(monthStart, { weekStartsOn: 1 });
    const end = endOfWeek(monthEnd, { weekStartsOn: 1 });

    const dates = [];
    let day = start;
    while (day <= end) {
      for (let i = 0; i < 7; i++) {
        dates.push(day);
        day = addDays(day, 1);
      }
    }
    return dates;
  };

  const Cells = () => {
    const dates = generateDates();

    return (
      <div className="grid grid-cols-7 text-center gap-y-6 py-4">
        {dates.map((day, index) => {
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isToday = isSameDay(day, new Date());
          const isSelected = isSameDay(day, selectedDate);

          return (
            <button
              key={index}
              onClick={() => setSelectedDate(day)}
              className={`w-9 h-9 mx-auto flex items-center justify-center rounded-full 
                ${isSelected ? "bg-[#4b207a] text-white" : isToday ? "text-white bg-[#4b207a]" : ""}
                ${!isCurrentMonth ? "text-gray-400" : "text-black"}
                font-poppins font-medium text-[14px]`}
            >
              {format(day, "d")}
            </button>
          );
        })}
      </div>
    );
  };

  const NoteSection = () => {
    const formattedDate = format(selectedDate, "yyyy-MM-dd");
    const eventNote = notes[formattedDate as keyof typeof notes];
    const dayName = format(selectedDate, "EEEE"); // Full day name like "Thursday"

    return (
      <div className="mt-6 px-6 flex flex-col gap-2">
        {eventNote ? (
          <>
            <div className="flex items-center gap-4">
              <div className="flex flex-col">
                <span className="font-poppins font-bold text-[16px] text-black">
                  {format(selectedDate, "dd-MMM-yyyy")} :
                </span>
                <span className="font-poppins font-semibold text-[16px] text-black">
                  {dayName}
                </span>
              </div>
              <div className="flex-1 font-poppins text-[14px] text-black">
                {eventNote}
              </div>
              <button className="text-black">
                <Pencil size={18} />
              </button>
            </div>
          </>
        ) : (
          <div className="text-center font-poppins font-normal text-[14px] text-gray-500">
            No events for this day.
          </div>
        )}
      </div>
    );
  };

  function setShowNotePopup(isOpen: boolean): void {
    setPopupOpen(isOpen);
  }

  return (
    <div className=" bg-[#f2f2f2]  rounded-[10px] space-y-2">
      <Header />
      <DaysOfWeek />
      <Cells />
      <NoteSection />
      <div className="px-6 my-4">
        <div className="flex justify-end">
          <button
            className="bg-[#4b207a] hover:bg-[#3d1862] transition-all text-white py-2 px-4 text-sm font-poppins rounded-lg"
            onClick={() => setShowNotePopup(true)}
          >
            Create Note
          </button>
        </div>
      </div>

      {/* Note Popup */}
      <NotePopup
        isOpen={isPopupOpen}
        onClose={() => setPopupOpen(false)}
        date={selectedDate}
      />
    </div>
  );
}
