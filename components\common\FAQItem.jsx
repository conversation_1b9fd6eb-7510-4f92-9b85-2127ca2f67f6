"use client"
import React, { useState } from "react";

const FAQItem = ({ question, answer }) => {
  const [isOpen, setIsOpen] = useState(true);

  const toggleFAQ = () => setIsOpen(!isOpen);

  return (
    <div className="border rounded-lg overflow-hidden">
      <button
        onClick={toggleFAQ}
        className={`w-full flex items-center justify-between p-4 ${
          isOpen ? "bg-blue-100" : "bg-blue-50"
        } hover:bg-blue-200 transition-colors`}
      >
        <span className="text-left font-medium">{question}</span>
        <svg
          className={`w-5 h-5 transform transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div className="p-4 bg-blue-50">
          <p className="text-gray-600">{answer}</p>
        </div>
      )}
    </div>
  );
};

export default FAQItem;
