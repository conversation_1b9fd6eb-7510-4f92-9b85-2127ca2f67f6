"use client"

import { useState } from "react"
import { cn } from "@/api-services/utils"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { OverviewContent } from "@/components/course/courseUI/overview"
import { Notes } from "@/components/course/courseUI/notes"
import QuizInterface from "@/components/course/tab-contents/quiz/quiz"
import Projects from "@/components/course/projects/page"

type TabType = "overview" | "notes" | "quizzes" | "projects"

const tabs: TabType[] = ["overview", "notes", "quizzes", "projects"]

export function CourseTabs() {
  const [activeTab, setActiveTab] = useState<TabType>("overview")
  const currentIndex = tabs.indexOf(activeTab)

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1])
    }
  }

  const handleNext = () => {
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1])
    }
  }

  // ✅ Function to switch to "projects" tab
  const goToProjectsTab = () => {
    setActiveTab("projects")
  }

  return (
    <div className="flex items-center w-full h-full md:px-[60px]">
      <div className="w-full">
        {/* Navigation */}
        <div className="flex justify-end mb-4 gap-[30px] md:gap-[50px] lg:gap-[60px]">
          <button
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            className="flex items-center text-sm font-semibold font-poppins text-[#252424] disabled:opacity-50"
          >
            <ChevronLeft className="w-4 h-4 mr-[-6px]" />
            <ChevronLeft className="w-4 h-4 ml-[-6px]" />
            previous
          </button>
          <button
            onClick={handleNext}
            disabled={currentIndex === tabs.length - 1}
            className="flex items-center text-sm font-semibold font-poppins text-[#252424] disabled:opacity-50"
          >
            Next
            <ChevronRight className="w-4 h-4 mr-[-6px]" />
            <ChevronRight className="w-4 h-4 ml-[-6px]" />
          </button>
        </div>

        {/* Scrollable Tabs */}
        <div className="overflow-x-auto">
          <div className="flex border-b mb-4 items-center w-[370px] gap-3 min-w-max">
            {tabs.map((tab) => (
              <button
                key={tab}
                className={cn(
                  "px-3 py-2 font-[500px] text-[16px] leading-[100%] capitalize",
                  activeTab === tab ? "border-b-2 border-[#2b2a2a]" : "text-[#4b4b4b]"
                )}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === "overview" && <OverviewContent />}
        {activeTab === "notes" && <Notes />}
        {activeTab === "quizzes" && <QuizInterface onNext={goToProjectsTab} />} {/* ✅ Pass onNext */}
        {activeTab === "projects" && <Projects />}
      </div>
    </div>
  )
}
