import { Lock } from "lucide-react"

export default function QuizHeader({ attemptNumber }: { attemptNumber: number }) {
  const left = 3 - attemptNumber
  
  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0">
        {/* Left side: Quiz label and Attempt number */}
        <div className="flex flex-wrap items-center space-x-2">
          <h2 className="font-bold text-base sm:text-lg">Quiz:</h2>
          <div>
            <p
              className={`font-medium text-xl leading-none tracking-[-0.01em] font-poppins ${
                attemptNumber === 1
                  ? 'text-[#0a5224]'
                  : attemptNumber === 2
                  ? 'text-[#b7a501]'
                  : attemptNumber === 3
                  ? 'text-[#b90000]'
                  : 'text-gray-700'
              }`}
            >
              Attempt {attemptNumber}{' '}
              <span className="font-medium text-base leading-none tracking-[-0.01em] font-poppins block sm:inline mt-1 sm:mt-0">
                (Score will be calculated)
              </span>
            </p>
          </div>
        </div>
        
        {/* Right side: Attempts left */}
        <div className="flex items-center space-x-4 mt-2 sm:mt-0">
          {left > 0 ? (
            <p className="text-[#ff0000]">
              {left} Attempt{left > 1 ? 's' : ''} left
            </p>
          ) : (
            <p className="text-red-600 flex items-center gap-1">
          <Lock className="w-4 h-4" />    No attempts left 
            </p>
          )}
        </div>
      </div>
      
      <div className="font-normal text-sm sm:text-base md:text-[16px] leading-snug tracking-[-0.01em] font-poppins text-black py-4 sm:py-6 md:py-9">
        <span className="font-medium text-red-500 px-2">Instructions:</span>
        Carefully read each question and choose the best answer.
        <div className="px-4 sm:px-8 md:px-12 lg:px-16 xl:px-24 mt-2">
          This is your first attempt. Your score will be calculated based on this attempt.
        </div>
      </div>
    </div>
  )
}