"use client"

import { CourseHeader } from "@/components/course/courseUI/course-header"
import { CourseSidebar } from "@/components/course/courseUI/course-sidebar"
import { CourseTabs } from "@/components/course/courseUI/course-tab"
import { VideoPlayer } from "@/components/course/courseUI/video-player"
import PaidHeader from "@/components/layout/PaidHeader";

export default function CoursePage() {
  return (
    <>
      <PaidHeader />
      <div className="flex flex-col bg-white min-h-screen mb-[130px] overflow-hidden">
        {/* For mobile: sidebar first, then header */}
        <div className="md:hidden">
          <CourseSidebar />
          <CourseHeader />
        </div>
        
        {/* For desktop: header first, then content with sidebar */}
        <div className="hidden md:block">
          <CourseHeader />
        </div>
        
        <div className="flex flex-col md:flex-row">
          {/* Sidebar only visible on desktop here */}
          <div className="hidden md:block">
            <CourseSidebar />
          </div>
          
          {/* Main Content */}
          <main className="flex-1">
            <div>
              
              <VideoPlayer />
              <CourseTabs />
            </div>
          </main>
        </div>
      </div>
    </>
  )
}