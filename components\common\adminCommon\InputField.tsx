"use client";
import React from 'react';

interface InputFieldProps {
  id: string;
  name: string;
  type: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
  suffix?: React.ReactNode; // Add suffix as an optional prop
  error?: string; // Add error as an optional prop
}

const InputField: React.FC<InputFieldProps> = ({
  id,
  name,
  type,
  value,
  onChange,
  label,
  error,
  suffix,
}) => {
  return (
    <div className="relative mb-4 w-full">
      <input
        type={type}
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        className=" w-full px-10  pt-5 pb-4 border border-black rounded-sm lg:text-[22px] text-[17px] focus:outline-none focus:border-black bg-[#eeeeee] placeholder-transparent"
        placeholder=" "
        required
      />
      <label
        htmlFor={id}
        className="absolute left-3 top-[-12px] bg-[#eeeeee] text-center font-medium text-[16px] leading-[30px] tracking-[0] font-[Poppins] px-5"
      >
        {label}
      </label>
      {suffix && <div className="absolute right-3 top-6">{suffix}</div>}
      {error && <span className="text-red-500 text-xs">{error}</span>}
    </div>
  );
};

export default InputField;
