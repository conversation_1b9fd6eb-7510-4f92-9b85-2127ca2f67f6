import React from 'react'
import Link from 'next/link'

const SignUpMain = () => {
  return (
 <>

{/* <!-- sign up --> */}
<div className="bg-gradient-to-r from-[#46167a] to-[#893ade] w-full h-auto py-10 mt-10 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8">
        {/* <!-- Heading --> */}
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white text-center">
          Enhance your skills today
        </h2>
        {/* <!-- Subtitle --> */}
        <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-white text-center mt-3">
          Whether you're advancing or just beginning, discover your next <br className="hidden sm:block"/> move here.
        </p>
        {/* <!-- Buttons --> */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
          <Link href="/signup">
            <button className="px-5 py-2 bg-white text-[#4B207A] rounded-lg text-base sm:text-lg font-sans hover:bg-opacity-70">
              Get Started Free
            </button>
          </Link>
          <Link href="/signup">
            <button className="px-5 py-2 text-white border border-white rounded-lg hover:border-[#4B207A] text-base sm:text-lg font-sans">
              Sign Up
            </button>
          </Link>
        </div>
      </div>
 </>
  )
}

export default SignUpMain
