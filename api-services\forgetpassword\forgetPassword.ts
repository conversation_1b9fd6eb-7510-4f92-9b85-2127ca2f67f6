// api-services/forgetPassword.ts
import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils";

// ================== INTERFACES ==================
interface ForgetPasswordPayload {
  email: string;
}

interface ForgetPasswordResponse {
  success: string;
}

// ================== API FUNCTION ==================
async function forgetPassword(payload: ForgetPasswordPayload): Promise<ForgetPasswordResponse> {
  const response = await makeRequest({
    endpoint: "/accounts/auth/forgot-password/", 
    method: "POST",
    data: { ...payload },
  });

  return response;
}

// ================== REACT QUERY HOOK ==================
const useForgetPassword = () => {
  return useMutation<ForgetPasswordResponse, Error, ForgetPasswordPayload>({
    mutationFn: forgetPassword,
    onError: (error) => {
      console.error("Forgot Password error:", error);
    },
  });
};

// ✅ Fix: Also export the function so it can be used separately if needed
export { useForgetPassword, forgetPassword as sendResetLink };
