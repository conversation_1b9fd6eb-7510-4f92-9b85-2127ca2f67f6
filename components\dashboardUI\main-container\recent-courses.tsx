"use client"

import { MoreVertical } from "lucide-react"
import { useState } from "react"
import Image from "next/image"

interface Course {
  id: number
  name: string
  progress: number
  startDate: string
  startTime: string
  image: string
}

export default function RecentCourses() {
  const [courses] = useState<Course[]>([
    {
      id: 1,
      name: "Python",
      progress: 50,
      startDate: "01-Apr-2025",
      startTime: "08:00AM",
      image: "/assets/images/Untitleddesign(1).png", 
    },
    {
      id: 2,
      name: "GitHub",
      progress: 33,
      startDate: "05-Apr-2025",
      startTime: "10:00AM",
      image: "/assets/images/Untitleddesign(18).png",
    },
    {
      id: 3,
      name: "Machine Learning",
      progress: 20,
      startDate: "10-Apr-2025",
      startTime: "09:00PM",
      image: "/assets/images/Untitleddesign(14).png",
    },
  ])

  return (
    <div className="bg-[#f2f2f2] p-3 rounded-[10px]">
      <div className="flex justify-between items-center p-2 py-2">
        <h2 className="font-poppins font-semibold text-[24px] leading-[100%] tracking-[-0.01em] text-black p-5">
          Recent Courses
        </h2>
        <button className="text-black">
          <MoreVertical width={32} height={32} />
        </button>
      </div>

      <div className="space-y-4">
        {courses.map((course) => (
          <CourseCard key={course.id} course={course} />
        ))}
      </div>
    </div>
  )
}

interface CourseCardProps {
  course: Course
}

function CourseCard({ course }: CourseCardProps) {
  const radius = 36;
  const stroke = 6;
  const normalizedRadius = radius - stroke / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (course.progress / 100) * circumference;

  return (
    <div className="bg-[#e5e5e5] flex items-center justify-between p-3 rounded-lg">
      <div className="flex items-center space-x-7">
        <Image
          src={course.image}
          alt={course.name}
          className="w-22 h-22 object-cover rounded-none"
          width={50}
          height={50}
        />
        <div>
          <h3 className="font-poppins font-semibold text-[20px] leading-[22px] tracking-[-0.02em] text-start text-black py-3">
            {course.name}
          </h3>
          <div className="font-poppins font-normal text-[13px] leading-[22px] tracking-[-0.02em] text-start text-black">
            <div>Start Date: {course.startDate}</div>
            <div>Start time: {course.startTime}</div>
          </div>
        </div>
      </div>

      {/* Progress Circle */}
      <div className="flex flex-col items-center">
        <span className="text-xs text-black p-2">In Progress</span>
        <div className="relative w-20 h-20">
          <svg height="80" width="80" className="transform -rotate-90">
            {/* Background Circle */}
            <circle
              stroke="rgba(76, 76, 76, 0.2)" // <-- #4C4C4C with 20% opacity
              fill="transparent"
              strokeWidth={stroke}
              r={normalizedRadius}
              cx="40"
              cy="40"
            />
            {/* Progress Circle */}
            <circle
              stroke="#4B207A"
              fill="transparent"
              strokeWidth={stroke}
              strokeLinecap="round"
              strokeDasharray={`${circumference} ${circumference}`}
              style={{ strokeDashoffset }}
              r={normalizedRadius}
              cx="40"
              cy="40"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="font-figtree font-bold text-[16px] text-[#4B207A]">
              {course.progress}%
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
