import { projects } from "@/utils/constant";
import Image from "next/image";





const RealWorldProjects: React.FC = () => {
  return (
    <section className="pt-[50px] pb-[10px]">
        <div className="container">
         
      <h2 className="text-xl sm:text-3xl md:text-4xl font-semibold text-center mb-8">
        Real-World Projects: Apply What You Learn
      </h2>
      <div className="overflow-x-auto md:overflow-visible">
        <div className="flex flex-nowrap sm:space-y-8 md:flex-col space-x-4 md:space-x-0">
          {projects.map((project, index) => (
            <div
              key={index}
              className="flex-none w-full md:w-auto md:flex flex-row block bg-[#F1F1F1] shadow-md rounded-xl overflow-hidden"
            >
              {/* Image Section */}
              <div className="w-full md:w-1/4 lg:w-1/5 p-4 flex justify-center">
                <Image
                  src={project.image}
                  alt={project.title}
                  width={250} // Reduced size
                  height={300} // Reduced size
                  className="w-full h-auto object-cover rounded-lg"
                />
              </div>

              {/* Text Section */}
              <div className="w-full md:w-3/4 lg:w-4/5 py-4 md:px-2 px-4 sm:p-6 flex flex-col justify-center">
                <h3 className="text-sm md:text-[24px] font-semibold text-center  sm:mb-2 font-poppins">
                  {project.title}
                </h3>
                <p className="text-black text-xs md:text-[20px] mt-3 font-poppins font-[300] md:leading-[30px]">{project.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Button */}
      <div className="hidden sm:block text-end mt-8">
        <button className="bg-[#4B207A] text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition duration-300">
          See More
        </button>
      </div>

      {/* Divider */}
      <div className="w-4/5 mx-auto border-b-2 border-black mt-20 sm:mt-10"></div>
    </div>
    </section>
  
  );
};

export default RealWorldProjects;
