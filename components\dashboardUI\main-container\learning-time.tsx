"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";

type LearningActivity = {
  name: string;
  projects: number;
  quiz: number;
  videos: number;
};

const data: LearningActivity[] = [
  { name: "Jan 01", projects: 30, quiz: 45, videos: 15 },
  { name: "Jan 02", projects: 45, quiz: 60, videos: 15 },
  { name: "Jan 03", projects: 45, quiz: 75, videos: 15 },
  { name: "Jan 04", projects: 30, quiz: 45, videos: 15 },
  { name: "Jan 05", projects: 30, quiz: 75, videos: 30 },
  { name: "Jan 06", projects: 15, quiz: 30, videos: 15 },
  { name: "Jan 07", projects: 30, quiz: 45, videos: 15 },
  { name: "Jan 08", projects: 15, quiz: 45, videos: 15 },
  { name: "Jan 09", projects: 45, quiz: 60, videos: 15 },
  { name: "Jan 10", projects: 30, quiz: 60, videos: 15 },
  { name: "Jan 11", projects: 30, quiz: 60, videos: 15 },
  { name: "Jan 12", projects: 30, quiz: 60, videos: 15 },
];

const formatYAxis = (minutes: number) => {
  if (minutes < 60) return `${minutes}min`;
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return mins > 0
    ? `${hours}.${mins.toString().padStart(2, "0")}hr`
    : `${hours}hr`;
};

export default function LearningTimeChart() {
  const [timeRange, setTimeRange] = useState("12");

  const filteredData =
    timeRange === "all" ? data : data.slice(data.length - Number.parseInt(timeRange));

  return (
    <div className="w-full bg-[#F8F8F8] rounded-lg p-6 shadow-sm">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h2 className="text-2xl font-bold text-black">Total Learning Time</h2>
        <div className="relative">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="block w-[140px] bg-white border border-purple-700 text-purple-700 rounded-md px-4 py-2 text-sm font-semibold focus:outline-none"
          >
            <option value="7">Last 7 days</option>
            <option value="12">Last 12 days</option>
            <option value="30">Last 30 days</option>
            <option value="all">All time</option>
          </select>
        </div>
      </div>

      <div className="w-full h-[400px] flex">
        <div className="flex-1">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={filteredData}
              margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
              barCategoryGap="30%"
              barSize={20} 
            >
              <CartesianGrid strokeDasharray="0" vertical={false} stroke="#E0E0E0" />
              <XAxis
                dataKey="name"
                tick={{ fontSize: 12, fill: "#000" }}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                tickFormatter={formatYAxis}
                tick={{ fontSize: 12, fill: "#000" }}
                tickLine={false}
                axisLine={false}
                tickCount={6}
              />
              <Tooltip
                formatter={(value: number) => [`${value} min`, ""]}
                labelFormatter={(label: string) => `Date: ${label}`}
              />

              {/* Draw each part separately to control gaps & radius */}
              {["videos", "quiz", "projects"].map((key) => (
                <Bar
                  key={key}
                  dataKey={key}
                  stackId="a"
                  radius={[8, 8, 8, 8]} // full radius on all sides
                  barSize={30}
                >
                  {filteredData.map((_, idx) => (
                    <Cell
                      key={`cell-${idx}`}
                      fill={
                        key === "videos"
                          ? "#2563EB"
                          : key === "quiz"
                          ? "#67E8F9"
                          : "#F87171"
                      }
                      stroke="#F8F8F8" // gap color = background
                      strokeWidth={4} // create visible separation
                    />
                  ))}
                </Bar>
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Legend */}
        <div className="hidden md:flex flex-col justify-center items-start ml-4 space-y-4">
          <div className="flex items-center space-x-2">
            <span className="inline-block w-3 h-3 rounded-full bg-[#F87171]" />
            <span className="text-sm font-semibold text-black">Projects</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-block w-3 h-3 rounded-full bg-[#67E8F9]" />
            <span className="text-sm font-semibold text-black">Quiz</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-block w-3 h-3 rounded-full bg-[#2563EB]" />
            <span className="text-sm font-semibold text-black">Videos</span>
          </div>
        </div>
      </div>
    </div>
  );
}
