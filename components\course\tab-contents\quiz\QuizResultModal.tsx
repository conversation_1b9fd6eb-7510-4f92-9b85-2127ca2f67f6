import Image from "next/image"

export default function QuizResultModal({
  show,
  score,
  total,
  perfect,
  attempt,
  onAction,
}: {
  show: boolean
  score: number
  total: number
  perfect: boolean
  attempt: number
  onAction: () => void
}) {
  if (!show) return null
  
  // Determine message and image path based on score
  let message = "";
  let imagePath = "";
  // Define text color based on score
  const textColor = score < 3 ? "#ffc107" : "#1b57c0";
  
  if (perfect) {
    message = "Excellent work!";
    imagePath = "/assets/images/idea2.png";
  } else if (score > 3) {
    message = "Great job! A little more practice and you're perfect.";
    imagePath = "/assets/images/idea2.png";
  } else {
    message = "Good attempt! Review the notes once more.";
    imagePath = "/assets/images/bookquiz.png";
  }
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white p-4 sm:p-6 md:p-10 rounded-lg w-full max-w-xs sm:max-w-md md:max-w-2xl sm:h-auto md:h-80 text-center">
        <h2 className="font-normal text-2xl sm:text-3xl md:text-4xl leading-none tracking-tight text-blue-600 p-2 sm:p-4 md:p-6">Valid Marks {score}/{total}</h2>
        
        <div className="flex flex-col sm:flex-row items-center justify-center p-1 space-y-2 sm:space-y-0">
          <Image
            src={imagePath}
            alt="Result icon"
            width={40}
            height={40}
            className="sm:mr-2"
          />
          <p style={{ color: textColor }} className="font-normal text-lg sm:text-xl leading-tight sm:leading-none tracking-tight px-2">
            {attempt === 3
              ? "Final attempt done. Review your answers."
              : message}
          </p>
        </div>
        
        <button
          onClick={onAction}
          className="w-full sm:w-64 md:w-80 bg-purple-600 text-white p-2 sm:p-3 mt-4 sm:mt-7 mb-2 rounded-md font-semibold text-sm sm:text-base  cursor-pointer"
        >
          {attempt === 3 || perfect ? "Review the answers" : "Try Again"}
        </button>
      </div>
    </div>
  )
}