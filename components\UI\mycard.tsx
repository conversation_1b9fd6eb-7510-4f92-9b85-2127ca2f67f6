// Modified CourseUnlockPopup.tsx
"use client"

import { X } from "lucide-react"

interface CourseUnlockPopupProps {
  isOpen: boolean
  onClose: () => void
  onPayClick: () => void
  onReferredClick: () => void
}

export function CourseUnlockPopup({ isOpen, onClose, onPayClick, onReferredClick }: CourseUnlockPopupProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center  z-50 ">
       <div className="text-center pt-2 bg-white rounded-lg p-3 w-[411px] h-[242px]">
      <div className="w-full  relative">
        <button
          onClick={onClose}
          className="absolute right-0 top-0 text-black cursor-pointer"
          aria-label="Close"
        >
          <X className="h-6 w-6" />
        </button>
       <div className="flex flex-col  py-8 gap-7">
          <h3 className="font-medium text-[20px] leading-[150%] tracking-[2%] text-center font-poppins text-black">Unlock this course by making a <span className="font-semibold text-[20px] leading-[150%] tracking-[2%] text-center font-poppins text-purple-900 p-1">payment</span>or being <span className="font-semibold text-[20px] leading-[150%] tracking-[2%] text-center font-poppins text-purple-900 p-1 ">referring</span>a friend. Select an option to continue!</h3>
         
          </div>
          <div className="flex justify-center gap-10 ">
            <button 
              onClick={onPayClick} 
              className="bg-[#1b993d] text-white py-3 px-9 rounded-md  font-semibold text-[20px] leading-[100%] tracking-[0%] font-work-sans cursor-pointer"
            >
              Pay
            </button>
            <button
              onClick={onReferredClick}
              type="button"
              className="bg-[#1b5299] text-white border-0 py-3 px-9 rounded-md font-semibold text-[20px] leading-[100%] tracking-[0%] font-work-sans  cursor-pointer"
            >
              Referred
            </button>
           
          </div>
        </div>
      </div>
    </div>
  )
}