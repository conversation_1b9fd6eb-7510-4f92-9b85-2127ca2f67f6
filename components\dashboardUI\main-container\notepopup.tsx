"use client"

import React, { useState } from "react"

interface NotePopupProps {
  isOpen: boolean
  onClose: () => void
  date: Date
}

export default function NotePopup({ isOpen, onClose, date }: NotePopupProps) {
  const [note, setNote] = useState("")

  if (!isOpen) return null

  const formattedDate = date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  })

  const weekday = date.toLocaleDateString("en-US", {
    weekday: "long",
  })

  function handleSaveToMyCalendar(event: React.MouseEvent<HTMLButtonElement, MouseEvent>): void {
    event.preventDefault()

    const savedNotes = JSON.parse(localStorage.getItem("myCalendarNotes") || "{}")

    const dateKey = date.toISOString().split("T")[0] // e.g., "2025-01-17"

    const newNote = {
      date: dateKey,
      note,
      timestamp: new Date().toISOString(),
    }

    const updatedNotes = {
      ...savedNotes,
      [dateKey]: newNote,
    }

    localStorage.setItem("myCalendarNotes", JSON.stringify(updatedNotes))

    onClose()
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-40">
      <div className="bg-[#f3f3f3] p-8  rounded-none w-[483px] md:w-[600px] h-[350px]  ">
        <h2 className="text-black font-[Poppins] font-bold text-[20px] leading-[25px] tracking-[-0.02em]">{formattedDate}</h2>
        <p className="text-black font-[Poppins] font-bold text-[20px] leading-[25px] tracking-[-0.02em] mb-4">{weekday}</p>
<div className="flex flex-col px-10 py-3">
<textarea
  className="w-full h-32 py-8 px-5 bg-[#e2e2e2] text-black rounded-none resize-none outline-none items-center justify-center font-[Poppins] font-normal text-[16px] leading-[25px] tracking-[-0.02em]"
  placeholder="Type here and Save your note. We'll remind you to complete your task."
  value={note}
  onChange={(e) => setNote(e.target.value)}
/>
</div>

<div className="flex justify-end py-6 gap-8">
  <button
    className="bg-[#0a5224] text-white px-6 py-3 rounded-nome font-[Poppins] font-medium text-[24px] leading-[100%] tracking-[-0.01em]"
    onClick={handleSaveToMyCalendar}
  >
    Save
  </button>
</div>

        </div>
      </div>
   
  )
}
