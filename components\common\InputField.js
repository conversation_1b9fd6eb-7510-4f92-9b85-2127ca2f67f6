import React from 'react';

const InputField = ({ id, name, type, value, onChange, label, error }) => {
  return (
    <div className="relative">
      <input
        type={type}
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        className="w-full px-3 sm:px-4 py-2 sm:py-3 border-2 border-[#9B58A3] focus:outline-none focus:border-[#9B58A3] bg-transparent peer rounded-tr-lg rounded-bl-lg"
        placeholder=" "
        required
      />
      <label htmlFor={id} className="absolute left-3 -top-3 px-2 bg-[#cdcdcd] text-black text-xs sm:text-sm">
        {label}
      </label>
      {error && <span className="text-red-500 text-xs">{error}</span>}
    </div>
  );
};

export default InputField;