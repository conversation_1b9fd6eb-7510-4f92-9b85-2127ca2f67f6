"use client";

import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface BlogDetailsHeaderProps {
  blogData: {
    id: number;
    title: string;
    category: string;
    description: string;
    image: string;
    profileImage: string;
    author: string;
    date: string;
    likes: number;
  };
}

const BlogDetailsHeader: React.FC<BlogDetailsHeaderProps> = ({ blogData }) => {
  const router = useRouter();

  const handleBackClick = () => {
    router.push('/blogs');
  };

  return (
    <>
      {/* Desktop/Laptop View */}
      <div className="hidden md:block">
        <div className="max-w-5xl mx-auto mt-10 px-4">
          <div className="py-4">
            <div className="flex items-center justify-between">
              {/* Left side - Back button and category */}
              <div className="flex items-center">
                <button
                  onClick={handleBackClick}
                  className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>

                <div className="flex items-center pl-8">
                  <div className="px-3 py-1 bg-[#633D9D] rounded-[3px] text-white text-sm font-medium">
                    {blogData.category}
                  </div>
                </div>
              </div>

              {/* Right side - Author info */}
              <div className="flex items-center gap-3 pr-24">
                <img
                  src={blogData.profileImage}
                  alt={blogData.author}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <span className="text-sm font-medium text-gray-700">
                  {blogData.author}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden bg-white">
        <div className="px-4 py-3">
          {/* Top row - Back button, category, and author */}
          <div className="flex items-center justify-between mb-3">
            {/* Left side - Back button and category */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleBackClick}
                className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 text-gray-600" />
              </button>

              <div className="px-2 py-1 bg-[#633D9D] rounded-[3px] text-white text-xs font-medium">
                {blogData.category}
              </div>
            </div>

            {/* Right side - Author info */}
            <div className="flex items-center gap-2">
              <img
                src={blogData.profileImage}
                alt={blogData.author}
                className="w-6 h-6 rounded-full object-cover"
              />
              <span className="text-xs font-medium text-gray-700">
                {blogData.author}
              </span>
            </div>
          </div>

          
        </div>
      </div>
    </>
  );
};

export default BlogDetailsHeader;
