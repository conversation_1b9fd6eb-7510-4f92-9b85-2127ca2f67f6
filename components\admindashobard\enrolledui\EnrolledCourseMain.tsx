"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { FiSearch } from "react-icons/fi";
import CertificateModal from "./CertificateModal";
import BadgesModal from "./BadgesModal";

interface StudentRow {
  no: string;
  name: string;
  email: string;
  progress: string;
  enrolled: string;
}

const studentData: StudentRow[] = [
  {
    no: "01",
    name: "Aditi S.",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python , MongoDB, MySQL",
  },
  {
    no: "02",
    name: "Aditi <PERSON>.",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python , MongoDB",
  },
  {
    no: "03",
    name: "Aditi S.",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python",
  },
  {
    no: "04",
    name: "Aditi S.",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python",
  },
  {
    no: "05",
    name: "<PERSON>iti <PERSON>",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python",
  },
  {
    no: "06",
    name: "Aditi S.",
    email: "<EMAIL>",
    progress: "75%",
    enrolled: "Python",
  },
];

export default function EnrolledCourseMain() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();
  const [isCertificateOpen, setIsCertificateOpen] = useState(false);
    const [isBadgeOpen, setIsBadgeOpen] = useState(false);


  const filteredData = studentData.filter((row) =>
    row.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <>
    <div className="p-4 sm:p-6 md:p-8 lg:p-10">
      {/* Header & Search */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-[30px]">
        <div className="relative w-full md:w-[453px] rounded bg-[#fafafa] shadow-md">
          <FiSearch className="absolute left-3 top-3 text-gray-500" size={18} />
          <input
            type="text"
            placeholder="Search by students"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 text-sm rounded border border-gray-200 bg-transparent focus:outline-none"
          />
        </div>
      </div>

      {/* Table */}
      <div className="w-full overflow-x-auto shadow-md rounded-md">
        <table className="min-w-[800px] w-full text-center">
          <thead className="bg-[#ededed]">
            <tr className="whitespace-nowrap md:whitespace-normal">
              <th className="px-4 md:py-4 py-3  text-sm sm:text-base font-semibold text-black">No</th>
              <th className="px-4 md:py-4 py-3 text-sm sm:text-base font-semibold text-black">Name</th>
              <th className="px-4 md:py-4 py-3 text-sm sm:text-base font-semibold text-black">Email</th>
              <th className="px-4 md:py-4 py-3 text-sm sm:text-base font-semibold text-black">Progress</th>
              <th className="px-4 md:py-4 py-3 text-sm sm:text-base font-semibold text-black">Enrolled</th>
              <th className="px-4 md:py-4 py-3 text-sm sm:text-base font-semibold text-black">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-[#C3C3C3] bg-white">
            {paginatedData.length > 0 ? (
              paginatedData.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50 text-sm sm:text-base whitespace-nowrap lg:whitespace-normal">
                  <td className="px-4 md:py-5 py-2">{row.no}</td>
                  <td className="px-4 md:py-5 py-2 font-medium text-black">{row.name}</td>
                  <td className="px-4 md:py-5 py-2">{row.email}</td>
                  <td className="px-4 md:py-5 py-2">{row.progress}</td>
                  <td className="px-4 md:py-5 py-2">{row.enrolled}</td>
                  <td className="px-4 md:py-5 py-2 space-x-2">
                    <button className="border border-[#438EFF] text-[#28509C] text-[13px] px-4 py-[6px] rounded-full">
                      View
                    </button>
                  <button
  onClick={() => setIsCertificateOpen(true)}
  className="border border-[#50C878] text-[#309C28] text-[13px] px-4 py-[6px] rounded-full"
>
  Certificate
</button>
                    <button onClick={() => setIsBadgeOpen(true)}
                     className="border border-[#9B59B6] text-[#64289C] text-[13px] px-4 py-[6px] rounded-full">
                      Badges
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-6 text-gray-500">
                  No students found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-6">
        <div className="text-sm text-gray-500 text-center md:text-left">
          Showing{" "}
          <span className="font-medium text-[#438eff] px-1">
            {filteredData.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1} -{" "}
            {Math.min(currentPage * itemsPerPage, filteredData.length)}
          </span>{" "}
          of{" "}
          <span className="font-medium text-[#438eff] px-1">{filteredData.length}</span>{" "}
          rows
        </div>

        <div className="flex flex-wrap justify-center md:justify-end items-center gap-2">
          <button
            className="text-[#438eff] text-sm px-3 py-1"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </button>
          <button
            className="text-[#646464] text-sm px-3 py-1"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span className="bg-[#438eff] text-white px-4 py-1 text-sm rounded">
            {currentPage}
          </span>
          <button
            className="text-[#438eff] border border-[#438eff] px-3 py-1 text-sm rounded"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Next
          </button>
          <button
            className="text-[#438eff] text-sm px-3 py-1"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            Last
          </button>
        </div>
      </div>
    </div>

    {/* certificate modal */}
    <CertificateModal
      isOpen={isCertificateOpen}
  onClose={() => setIsCertificateOpen(false)}
    />
    {/* badge modal */}
    <BadgesModal 
    isOpen={isBadgeOpen}
    onClose={() => setIsBadgeOpen(false)}
    />
    </>
  );
}
