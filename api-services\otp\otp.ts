// api-service/otp.ts
import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils";

// ================== OTP INTERFACES ==================
interface VerifyOtpPayload extends Record<string, unknown> {
  otp: number;
}

interface VerifyOtpResponse {
  message: string; // Example: "OTP verified successfully. You can now log in."
}

// ================== OTP VERIFY API ==================
async function verifyOtp(payload: VerifyOtpPayload): Promise<VerifyOtpResponse> {
  const response = await makeRequest({
    endpoint: "/accounts/verify-otp/",
    method: "POST",
    data: payload, // ✅ this will now work without type error
  });

  return response;
}

// ================== REACT QUERY HOOK ==================
const useVerifyOtp = () => {
  return useMutation<VerifyOtpResponse, Error, VerifyOtpPayload>({
    mutationFn: verifyOtp,
    onError: (error) => {
      console.error("OTP verification error:", error);
    },
  });
};

export { useVerifyOtp };
