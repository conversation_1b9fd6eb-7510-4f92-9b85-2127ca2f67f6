// import ModulePDFViewer from "./ModulePDFViewer";


const CurriculumPage: React.FC = () => {
  return (
    <section className="pt-[50px]">
          <div className="container">
      <h1 className="font-poppins font-semibold text:[20px] md:text-[40px] leading-[30px] tracking-[-0.01em] text-center text-[#000000] mb-5 cursor-pointer">
        Enhance Your Expertise: Our Curriculum Blueprint
      </h1>

      {/* Curriculum Blueprint Buttons */}
      <div className="flex flex-col md:flex-row justify-center items-center lg:gap-10 gap-6 my-10">
  <button className="bg-[#CFCFCF] text-black font-semibold p-3 sm:px-8 sm:py-4 rounded-lg shadow-md w-full sm:w-[320px] md:w-[357px] text-center">
    <span className="block text-[13px] md:text-[20px] font-medium">For Beginners</span>
    <span className="block text-[10px] md:text-[15px] font-medium text-[#3F3F3F]">11.5 months duration</span>
  </button>
  <button className="bg-[#CFCFCF] text-black font-semibold p-3 sm:px-8 sm:py-4 rounded-lg shadow-md w-full sm:w-[320px] md:w-[357px] text-center">
    <span className="block text-[13px] md:text-[20px] font-medium">For Intermediate</span>
    <span className="block text-[10px] md:text-[15px] font-medium text-[#3F3F3F]">11.5 months duration</span>
  </button>
  <button className="bg-[#CFCFCF] text-black font-semibold p-3 sm:px-8 sm:py-4 rounded-lg shadow-md w-full sm:w-[320px] md:w-[357px] text-center">
    <span className="block text-[13px] md:text-[20px] font-medium">For Advanced</span>
    <span className="block text-[10px] md:text-[15px] font-medium text-[#3F3F3F]">9.5 months duration</span>
  </button>
</div>

      {/* New Component - Module PDF Viewer */}
      {/* <ModulePDFViewer /> */}
      <div className="w-4/5 mx-auto border-b-2 border-black mt-20"></div>
    </div>
    </section>
  
  );
};

export default CurriculumPage;
