import React from 'react';
import CardPopular from './CardPopular';
import { topics } from '@/utils/constant';





const PopularTopic: React.FC = () => {
  return (
    <section className="bg-white p-4 sm:p-6 lg:p-8 mt-8 ">
      <div className="container">
        
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className=" sm:text-[36px] text-xl font-semibold text-black font-poppins">
            Popular Topics
          </h2>
          <button className="bg-[#4B207A] md:text-[20px] text-[10px]  font-medium text-white px-4 py-2 rounded-md hover:bg-purple-800 transition-colors font-poppins">
            See All Topics
          </button>
        </div>

        {/* 
          Single container that behaves differently on mobile vs. desktop:
          - Mobile (< md): flex row with horizontal scroll
          - Desktop (≥ md): 3-column grid, no scroll
        */}
        <div
          className="
            flex space-x-4 py-4
            overflow-x-auto snap-x snap-mandatory
            md:grid sm:grid-cols-2 lg:grid-cols-3 md:gap-5
            md:overflow-x-visible md:snap-none md:space-x-0 
          "
        >
          {topics?.map((topic, idx) => (
            <div
              key={idx}
              className="
                snap-start shrink-0 min-w-[200px]  rounded-xl 
                md:min-w-0 
              "
            >
              <CardPopular 
                icon={topic.icon}
                title={topic.title}
                href={topic.href} 
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PopularTopic;
