'use client';

import React from 'react';
import Link from 'next/link';

type QuizReviewProps = {
  quizData: {
    id: number;
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  }[];
  userAnswers: Record<number, number>;
  score: number;
  onNext?: () => void;
};

export default function QuizReview({
  quizData,
  userAnswers,
  score,
  onNext,
}: QuizReviewProps) {
  return (
    <div className="w-full max-w-3xl">
      <div className="p-2 sm:p-3">
        <h2 className="font-bold text-base sm:text-lg">Quiz:</h2>
        <h2 className="font-[Poppins] font-medium text-lg sm:text-[20px] leading-[120%] sm:leading-[100%] tracking-[-0.01em] text-black mt-2">
          Valid Answers: {score}/{quizData.length}
        </h2>
      </div>

      <div className="space-y-4 sm:space-y-6">
        {quizData.map((q) => {
          const isCorrect = userAnswers[q.id] === q.correctAnswer;
          return (
            <div key={q.id} className="mb-4 sm:mb-6 p-3 sm:p-4">
              <h3 className="font-semibold text-base sm:text-lg mb-2 sm:mb-4 text-gray-900">
                {q.id}. {q.question}
              </h3>
              <div className="space-y-2">
                {q.options.map((opt: string, idx: number) => {
                  const selectedIdx = userAnswers[q.id];
                  const isSelected = selectedIdx === idx;
                  const correct = q.correctAnswer === idx;
                  const wrongSelected = isSelected && !correct;

                  const accentColor = correct
                    ? 'accent-[#1b993d]'
                    : wrongSelected
                    ? 'accent-[#e83232]'
                    : 'accent-gray-300';

                  const textColor = correct
                    ? 'text-[#1b993d]'
                    : wrongSelected
                    ? 'text-[#e83232]'
                    : 'text-gray-700';

                  const isChecked = correct || isSelected;

                  return (
                    <label
                      key={idx}
                      className={`flex items-start sm:items-center space-x-2 ${textColor} text-sm sm:text-base`}
                    >
                      <input
                        type="radio"
                        name={`review-${q.id}-${idx}`}
                        checked={isChecked}
                        readOnly
                        className={`${accentColor} mt-1 sm:mt-0`}
                      />
                      <span className={isChecked ? 'font-normal' : ''}>{opt}</span>
                    </label>
                  );
                })}
              </div>

              <div className="mt-2 text-xs sm:text-sm font-medium">
                Points:{' '}
                <span className={isCorrect ? 'text-[#1b993d]' : 'text-[#e83232]'}>
                  {isCorrect ? 1 : 0}
                </span>
              </div>

              {!isCorrect && (
                <p className="mt-2 sm:mt-3 text-xs sm:text-sm text-gray-700">
                  <span className="font-normal text-gray-900">Explanation: </span>
                  {q.explanation}
                </p>
              )}

              <div
                className={`mt-2 sm:mt-3 font-normal text-xs sm:text-sm ${
                  isCorrect ? 'text-[#1b993d]' : 'text-[#e83232]'
                }`}
              >
                <span className="mr-2">{isCorrect ? '✔' : '✖'}</span>
                {isCorrect ? 'Right Answer' : 'Wrong Answer'}
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex justify-center sm:justify-end mt-4 sm:mt-6">
        {/* Button handling click separately */}
        <button
          onClick={onNext}
          className="px-6 py-2 sm:px-8 md:px-9 sm:py-3 bg-[#8b4fcc] text-white rounded-md font-semibold hover:bg-[#763bb8] text-sm sm:text-base cursor-pointer"
        >
          <Link href="/notes" className="w-full h-full block text-center">
            Go Next
          </Link>
        </button>
      </div>
    </div>
  );
}
