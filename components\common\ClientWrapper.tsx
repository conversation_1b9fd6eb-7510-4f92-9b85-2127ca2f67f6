// components/ClientWrapper.tsx
"use client";

import { usePathname } from "next/navigation";
import FotterCon from "@/components/layout/FotterMain";
import { ReactNode } from "react";

export default function ClientWrapper({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const hideFooter =
    pathname.startsWith("/dashboard") || pathname.startsWith("/Admin");

  return (
    <>
      {children}
      {!hideFooter && <FotterCon />}
    </>
  );
}
