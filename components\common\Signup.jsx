"use client"; 

import { useState } from "react";

import Link from "next/link";
import Image from "next/image"; 


const Signup = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);

  return (
    <>
        <section>
   <div className="container">
   <div className="flex flex-col lg:flex-row items-center justify-center min-h-screen bg-white p-4">
      {/* Signup Form */}
      <div className="bg-[#EBEBEB] lg:p-10 p-5 rounded-lg shadow-[4px_4px_4px_0px_rgba(0,0,0,0.25)] w-full max-w-[604px] ">
      

        <h2 className="text-center text-[25px] font-[600] mb-4 font-sans">Sign Up Today!</h2>
        <h4 className="font-[500] text-start text-[20px] font-sans">Continue With</h4>

        {/* Social Sign In */}
        <div className="flex  justify-between mb-4">
  <button className="   w-12 h-12 flex items-center justify-center">
    <Image src="/assets/images/google.png" alt="Google" width={60} height={60} />
  </button>
  <button className="   w-12 h-12 flex items-center justify-center">
    <Image src="/assets/images/github.png" alt="GitHub" width={43} height={43} />
  </button>
  <button className="  w-12 h-12 flex items-center justify-center">
    <Image src="/assets/images/facebook.png" alt="Facebook" width={43} height={43} />
  </button>
</div>

        <div className="flex items-center mb-4">
  <div className="w-full border-t border-black border-[2px]"></div>
  <span className="px-2 text-black text-[20px] font-[500]">or</span>
  <div className="w-full border-t border-black border-[2px]"></div>
</div>

        {/* Form */}
        <form className="lg:p-2">
          <div className="mb-3">
            <label className="block text-black text-[20px] font-sans">First Name</label>
            <input type="text" className="w-full p-3 border rounded-lg" placeholder="Enter first name" />
          </div>

          <div className="mb-3">
            <label className="block text-black text-[20px] font-sans">Last Name</label>
            <input type="text" className="w-full p-3 border rounded-lg " placeholder="Enter last name" />
          </div>

          <div className="mb-3 relative">
            <label className="block text-black text-[20px] font-sans">Password</label>
            <input
              type={passwordVisible ? "text" : "password"}
              className="w-full border rounded-lg pr-10 p-3"
              placeholder="Enter password"
            />
            <span
              className="absolute right-3 top-10 cursor-pointer"
              onClick={() => setPasswordVisible(!passwordVisible)}
            >
              {passwordVisible ? "🙈" : "👁️"}
            </span>
          </div>

          <div className="mb-3">
            <label className="block text-black text-[20px] font-sans">Email</label>
            <input type="email" className="w-full  border rounded-lg p-3" placeholder="Enter email" />
          </div>

          <div className="flex items-center mb-3">
            <input type="checkbox" className="mr-2 w-[22px] h-[22px] border-none" />
            <span className="text-[15px] font-[500] font-sans">
              I agree to the{" "}
              <Link href="#" className="text-[#063585] underline">
                privacy policy
              </Link>
            </span>
          </div>

          <button type="submit" className="w-full bg-[#063585] text-white p-2 text-[22px] rounded-lg hover:bg-blue-700 mt-5">
            Get Started
          </button>
        </form>
      </div>

      {/* Info Section */}
      <div className=" flex flex-col items-center text-center justify-center lg:w-1/2 lg:p-10 p-5">
      <Image src="/assets/images/logobig.png" alt="Facebook" width={395} height={128} className="w-[150px] lg:w-[395px]" />
        <p className="text-black text-[18px] font-[500]  text-center mt-4 font-sans">
       <b> Welcome to ApexIQ!</b> Sign up today to access personalized learning experiences designed to help you grow smarter and achieve your educational goals. With ApexIQ, you'll unlock expert resources, interactive tools, and a supportive community to elevate your learning journey.
        </p>
      
        <div className="mt-4 text-center">
          <p className="text-black text-[18px] font-sans font-[500]">Already have an account?</p>
          <Link href="/login">
            <button className="mt-4 px-8 py-2 bg-[#063585] text-[22px] text-white rounded-lg hover:text-white">
              Sign In
            </button>
          </Link>
        </div>
      </div>
    </div>
   </div>

     
    </section>

    <section>
   <div className="container">
   <div className="flex flex-col lg:flex-row items-center justify-center min-h-screen bg-white p-4">
   
      <div className="bg-[#EBEBEB] lg:p-10 p-5 rounded-lg shadow-[4px_4px_4px_0px_rgba(0,0,0,0.25)] w-full max-w-[604px] ">
      

        <h2 className="text-center text-[25px] font-[600] mb-4 font-sans">Sign in!</h2>


   



        {/* Form */}
        <form className="lg:p-2">
        

        <div className="mb-3">
            <label className="block text-black text-[20px] font-sans">Email</label>
            <input type="email" className="w-full  border rounded-lg p-3" placeholder="Enter email" />
          </div>

          <div className="mb-3 relative">
            <label className="block text-black text-[20px] font-sans">Password</label>
            <input
              type={passwordVisible ? "text" : "password"}
              className="w-full border rounded-lg pr-10 p-3"
              placeholder="Enter password"
            />
            <span
              className="absolute right-3 top-10 cursor-pointer"
              onClick={() => setPasswordVisible(!passwordVisible)}
            >
              {passwordVisible ? "🙈" : "👁️"}
            </span>
          </div>

       


          <button type="submit" className="w-full bg-[#063585] text-white font-sans p-2 text-[22px] rounded-lg hover:bg-blue-700 mt-5">
            Sign In
          </button>
          
          <div className="flex items-center text-center justify-center mb-3 mt-5">
          
            <span className="text-[18px] font-[500] font-sans">
            Don’t have an account?
              <Link href="/login" className="text-[#063585] underline ml-6">
                Sign In
              </Link>
            </span>
          </div>
        </form>
      </div>

      {/* Info Section */}
      <div className=" flex flex-col items-center text-center justify-center lg:w-1/2 lg:p-10 p-5">
      <Image src="/assets/images/logobig.png" alt="Facebook" width={395} height={128} className="w-[150px] lg:w-[395px]" />
        <p className="text-black text-[18px] font-[500]  text-center mt-4 font-sans">
        "<b>Welcome back to ApexIQ!</b>  Sign in to continue your learning journey with personalized courses, expert resources, and powerful tools designed to help you reach your goals. Let’s keep moving forward together!"
        </p>
      
   
      </div>
    </div>
   </div>

     
    </section>
    </>
  

  );
};

export default Signup;
