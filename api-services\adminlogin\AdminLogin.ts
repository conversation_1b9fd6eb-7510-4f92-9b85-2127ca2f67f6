import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils";
import Cookies from "js-cookie";

// ================== ADMIN LOGIN INTERFACES ==================
interface AdminLoginPayload extends Record<string, unknown> {
  username: string | number;
  password: string;
}

interface AdminLoginResponse {
  message: string;
  refresh: string;
  access: string;
  user_id: number;
  email: string;
  role: string;
}

// ================== ADMIN LOGIN API ==================
async function adminLogin(payload: AdminLoginPayload): Promise<AdminLoginResponse> {
  // Auto convert numeric username string to number
  const normalizedUsername =
    typeof payload.username === "string" && !isNaN(Number(payload.username))
      ? Number(payload.username)
      : payload.username;

  const requestData: Record<string, unknown> = {
    username: normalizedUsername,
    password: payload.password,
  };

  // ✅ Debug log to see sent data
  console.log("Sending admin login payload:", requestData);

  try {
    const response = await makeRequest({
      endpoint: "/accounts/login/admin/",
      method: "POST",
      data: requestData,
    });

    if (!response || typeof response !== "object") {
      throw new Error("Invalid response from server");
    }

    const { access, refresh } = response as AdminLoginResponse;

    if (!access || !refresh) {
      throw new Error("Login failed: Missing tokens in response.");
    }

    // Store tokens in cookies
    Cookies.set("access", access);
    Cookies.set("refresh_token", refresh);

    // Also store in localStorage if needed
    localStorage.setItem("token", access);
    localStorage.setItem("refresh_token", refresh);

    return response;
  } catch (error: any) {
    console.error("Admin login error:", error);
    throw new Error(error?.message || "Login failed");
  }
}

// ================== REACT QUERY HOOK ==================
const useAdminLogin = () => {
  return useMutation<AdminLoginResponse, Error, AdminLoginPayload>({
    mutationFn: adminLogin,
    onError: (error) => {
      console.error("Admin login failed:", error.message);
    },
  });
};

export { useAdminLogin };
