"use client";

import React, { useState } from "react";
import Image from "next/image";
import WelcomeContent from "@/components/signupui/welcome";
import { <PERSON>, <PERSON>, EyeOff, Check } from "lucide-react";
import { sendResetLink } from "../../../api-services/forgetpassword/forgetPassword";
import { useResetPassword } from "../../../api-services/forgetpassword/resetPassword";

interface CreateNewPasswordData {
  password: string;
  confirmPassword: string;
}

interface ValidationMessages {
  email?: string;
  password?: string;
  confirmPassword?: string;
}

const calculatePasswordStrength = (password: string) => {
  let strength = 0;
  const checks = {
    length: password.length >= 8,
    hasNumber: /\d/.test(password),
    hasLowerCase: /[a-z]/.test(password),
    hasUpperCase: /[A-Z]/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  strength += checks.length ? 20 : 0;
  strength += checks.hasNumber ? 20 : 0;
  strength += checks.hasLowerCase ? 20 : 0;
  strength += checks.hasUpperCase ? 20 : 0;
  strength += checks.hasSpecial ? 20 : 0;

  if (strength <= 20) return { strength, text: "Very Weak", color: "bg-red-500" };
  if (strength <= 40) return { strength, text: "Weak", color: "bg-orange-500" };
  if (strength <= 60) return { strength, text: "Good", color: "bg-yellow-500" };
  if (strength <= 80) return { strength, text: "Strong", color: "bg-blue-500" };
  return { strength, text: "Very Strong", color: "bg-green-500" };
};

const ForgotPassword: React.FC = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState("");
  const [formData, setFormData] = useState<CreateNewPasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [validationMessages, setValidationMessages] = useState<ValidationMessages>({});
  const [isLoading, setIsLoading] = useState(false);

  const passwordStrength = calculatePasswordStrength(formData.password);

  // Import the reset password mutation hook
  const resetPasswordMutation = useResetPassword();

  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const messages: ValidationMessages = {};

    if (!email) messages.email = "Email is required";
    else if (!emailRegex.test(email)) messages.email = "Enter a valid email";

    setValidationMessages(messages);
    return Object.keys(messages).length === 0;
  };

  const validateStep3 = () => {
    const messages: ValidationMessages = {};
    let isValid = true;

    if (!formData.password) {
      messages.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 8) {
      messages.password = "Min 8 characters";
      isValid = false;
    }

    if (formData.password !== formData.confirmPassword) {
      messages.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setValidationMessages(messages);
    return isValid;
  };

  // Handle sending reset link (Step 1)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail()) return;

    try {
      setIsLoading(true);
      const res = await sendResetLink({ email });
      console.log(res.success || "Link sent successfully");
      setStep(2);
    } catch (error: any) {
      console.error("Reset link failed:", error);
      setValidationMessages({
        email:
          error?.response?.data?.message ||
          "Failed to send reset link. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Continue to step 3 after email is sent (Step 2)
  const handleNext = () => setStep(3);

  // Handle resetting the password (Step 3)
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep3()) return;

    try {
      setIsLoading(true);
      await resetPasswordMutation.mutateAsync({
          new_password: formData.password,
  confirm_password: formData.confirmPassword,
      });
      setStep(4);
    } catch (error) {
      console.error("Reset password failed:", error);
      setValidationMessages({
        password: "Failed to reset password. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => window.history.back();

  const handleSignIn = () => (window.location.href = "/login");

  return (
    <>
      {/* HEADER */}
      <header className="border-b border-[#868686] flex items-center justify-between">
        <Image
          src="/assets/images/logobig.png"
          alt="logo"
          width={395}
          height={128}
          className="w-[80px] sm:w-[100px] lg:w-[130px] p-1 ml-4"
        />
      </header>

      <section className="flex flex-col px-4 sm:px-3 xl:px-[120px]">
        <div className="flex flex-col lg:flex-row items-center justify-between py-6 sm:py-10">
          <div className="bg-[#EBEBEB] md:p-[60px] p-[30px] rounded-lg shadow-md w-full max-w-[640px] relative">
            <button onClick={handleClose} className="absolute top-3 right-3 text-gray-500">
              <X size={24} />
            </button>

            {/* STEP 1: EMAIL ENTRY */}
            {step === 1 && (
              <>
                <h2 className="text-[#4B207A] text-2xl md:text-3xl font-bold font-poppins mb-[20px]">
                  Forgot Password <span className="text-[#0A5224] text-sm">EduTech</span>
                </h2>
                <p className="text-[14px] font-medium text-[#083747] mb-[20px]">
                  Enter Your Email Address...we will send the link to your Email
                </p>
                <form onSubmit={handleSubmit} className="space-y-6 mt-4">
                  <div className="relative">
                    <input
                      type="email"
                      id="email"
                      placeholder=" "
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
                    />
                    <label
                      htmlFor="email"
                      className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all \
                      peer-placeholder-shown:top-4 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400
                      peer-focus:top-2 peer-focus:text-[12px] peer-focus:text-[#0065ff] font-poppins"
                    >
                      Email Address
                    </label>
                  </div>
                  {validationMessages.email && (
                    <p className="text-red-600 text-sm">{validationMessages.email}</p>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-[#0D47A1] text-white py-3 rounded-md font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? "Sending..." : "Send Reset Link"}
                  </button>
                </form>
              </>
            )}

            {/* STEP 2: SHOW EMAIL SENT */}
            {step === 2 && (
              <>
                <h2 className="text-[#4B207A] text-2xl md:text-3xl font-bold font-poppins mb-[20px]">
                  Forgot Password <span className="text-[#0A5224] text-sm">EduTech</span>
                </h2>
                <div className="space-y-4 mt-4">
                  <input
                    type="email"
                    value={email}
                    disabled
                    className="w-full p-3 border rounded-lg bg-gray-100"
                  />
                  <button
                    onClick={handleNext}
                    className="w-full bg-[#063585] text-white py-3 rounded-md"
                  >
                    Continue
                  </button>
                  <p className="text-[12px] text-[#083747] font-medium">
                    We’ve sent a reset link to: <span className="text-[#0065ff]">{email}</span>
                  </p>
                </div>
              </>
            )}

            {/* STEP 3: CREATE NEW PASSWORD */}
            {step === 3 && (
              <>
                <h2 className="text-[#4B207A] text-2xl md:text-3xl font-bold font-poppins mb-[20px]">
                  Create New Password <span className="text-[#0A5224] text-sm">EduTech</span>
                </h2>
                <form onSubmit={handleResetPassword} className="space-y-4 mt-4">
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder=" "
                      value={formData.password}
                      onChange={(e) =>
                        setFormData({ ...formData, password: e.target.value })
                      }
                      required
                      className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
                    />
                    <label className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all font-poppins">
                      New Password
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500"
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  <div className="w-full h-2 rounded bg-gray-300">
                    <div
                      className={`${passwordStrength.color} h-full`}
                      style={{ width: `${passwordStrength.strength}%` }}
                    />
                  </div>
                  <div className="text-xs font-medium text-gray-700">
                    {passwordStrength.text}
                  </div>

                  <div className="relative">
                    <input
                      type="password"
                      placeholder=" "
                      value={formData.confirmPassword}
                      onChange={(e) =>
                        setFormData({ ...formData, confirmPassword: e.target.value })
                      }
                      required
                      className="peer w-full px-4 pt-6 pb-2 border border-black rounded-md focus:outline-none focus:ring-2 focus:ring-[#0065ff]"
                    />
                    <label className="absolute left-4 top-2 bg-[#EBEBEB] text-[12px] text-gray-500 transition-all font-poppins">
                      Confirm Password
                    </label>
                  </div>

                  {validationMessages.password && (
                    <p className="text-red-600 text-sm">{validationMessages.password}</p>
                  )}
                  {validationMessages.confirmPassword && (
                    <p className="text-red-600 text-sm">{validationMessages.confirmPassword}</p>
                  )}

                  <button
                    type="submit"
                    className="w-full bg-[#0D47A1] text-white py-3 rounded-md font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? "Resetting..." : "Reset Password"}
                  </button>
                </form>
              </>
            )}

            {/* STEP 4: SUCCESS */}
            {step === 4 && (
              <div className="flex flex-col items-center justify-center mt-6 space-y-4">
                <div className="w-16 h-16 bg-green-900 rounded-full flex items-center justify-center">
                  <Check className="w-8 h-8 text-[#1cfe03]" strokeWidth={3} />
                </div>
                <h2 className="text-[#0A5224] text-base font-semibold font-poppins">
                  Password Changed Successfully!
                </h2>
                <button
                  onClick={handleSignIn}
                  className="bg-[#063585] text-white py-2 px-5 rounded-lg font-semibold"
                >
                  Login
                </button>
              </div>
            )}
          </div>

          <div className="w-full hidden md:flex justify-center">
            <WelcomeContent />
          </div>
        </div>
      </section>
    </>
  );
};

export default ForgotPassword;
