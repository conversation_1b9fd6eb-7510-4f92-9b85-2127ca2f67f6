"use client";
import Image from "next/image";
import { useState, useMemo } from "react";
import { Search,  X } from "lucide-react";
import Link from "next/link";
import CourseCard from "@/components/UI/courseCard";
import PaidHeader from "@/components/layout/PaidHeader";
import Dropdown from "@/components/layout/paiddropdown";

export default function PaidCourses() {
  const [searchText, setSearchText] = useState("");

  const clearSearch = () => {
    setSearchText("");
  };
  
  const courses = useMemo(() => [
    {
      id: 1,
      title: "Ethical Hacking & Cybersecurity",
      price: 1000,
      image: "/assets/images/Untitleddesign(5).png",
      isPaid: true,
      badge: "New Updated",
      badgeColor: "green",
    },
    {
      id: 2,
      title: "Full-Stack Web Development",
      price: 1000,
      image: "/assets/images/Untitleddesign(13).png",
      isPaid: true,
    },
    {
      id: 3,
      title: "Machine Learning with TensorFlow & Python",
      price: 1000,
      image: "/assets/images/Untitleddesign(12).png",
      isPaid: true,
      badge: "LIVE",
      badgeColor: "red",
    },
    {
      id: 4,
      title: "Model Evaluation & Performance Metrics",
      price: 1000,
      image: "/assets/images/Untitleddesign(11).png",
      isPaid: true,
      badge: "Trending",
      badgeColor: "blue",
    },
    {
      id: 5,
      title: "Transfer Learning in Generative Models",
      price: 1000,
      image: "/assets/images/Untitleddesign(10).png",
      isPaid: true,
    },
    {
      id: 6,
      title: "Evaluation of Model Outputs Based on Prompts",
      price: 1000,
      image: "/assets/images/Untitleddesign(9).png",
      isPaid: true,
      badge: "Popular",
      badgeColor: "yellow",
    },
    {
      id: 7,
      title: "MLOps for ML Models using Jenkins or GitHub CI",
      price: 1000,
      image: "/assets/images/Untitleddesign(8).png",
      isPaid: true,
    },
    {
      id: 8,
      title: "Controlling the Output in Specific Applications",
      price: 1000,
      image: "/assets/images/Untitleddesign(7).png",
      isPaid: true,
    },
    {
      id: 9,
      title: "Model Deployment with TensorFlow Serving & Flask",
      price: 1000,
      image: "/assets/images/Untitleddesign(6).png",
      isPaid: true,
    },
  ], []);

  // Filter courses based on search text
  const filteredCourses = useMemo(() => {
    if (!searchText.trim()) {
      return courses; 
    }
    
    const lowerCaseSearch = searchText.toLowerCase();
    
   
    return courses.filter((course) => 
      course.title.toLowerCase().includes(lowerCaseSearch)
    );
  }, [searchText, courses]);

  return (
    <>
      <PaidHeader/>

      <main className="mt-4 md:px-[60px] pt-[60px]">
        <div className="px-2 sm:px-4 flex flex-col sm:flex-row items-center justify-between gap-4 w-full">
          <div className="flex-1 w-full max-w-md relative">
            <div className="relative mt-5 sm:w-[400px] md:w-[500px] lg:w-[661px] h-[55px] lg:h-[61px] shadow-md rounded-xl text-black flex items-center ">
            <div className="bg-[#f1f1f1] w-[60px] h-full rounded-l-xl flex items-center justify-between px-6 text-black">
  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#4B207A] h-5 w-5 sm:h-7 sm:w-7 font-poppins " />
  <input
    type="text"
    value={searchText}
    onChange={(e) => setSearchText(e.target.value)}
    placeholder="Search your courses"
    className="pl-2 pr-10 py-5 flex-grow rounded-r-xl text-sm sm:text-base focus:outline-none text-black ml-[50px]"
  />
  {searchText.length > 0 && (
    <button
      className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors text-black"
      onClick={clearSearch}
    >
      <X className="h-5 w-5 sm:h-6 sm:w-6 cursor-pointer" />
    </button>
  )}
</div>
           
            </div>
          </div>
          <div className="mt-4 sm:mt-0">
            <Link href="/mycourse">
              <button className="bg-[#4B207A] text-white font-medium px-4 py-2 sm:px-3 sm:py-3 rounded-lg shadow-lg transform transition-transform duration-200 hover:scale-105 flex items-center justify-center gap-2 text-sm sm:text-base w-full sm:w-auto font-poppins cursor-pointer">
                My Courses
              </button>
            </Link>
          </div>
        </div>
        <section className="px-4 py-5 md:py-8">
        <div className="flex items-center justify-between mb-6 mt-3">
  {/* Heading + Star image together */}
  <div className="flex items-center ">
    <h2 className="font-poppins font-semibold text-3xl leading-[1.2] tracking-[-0.01em] text-center text-[#000000] ">
      Paid Courses
    </h2>
    <Image
      src="/assets/images/stars1.png"
      alt="stars"
      width={40}
      height={40}
      className="w-[45px] sm:w-[55px] lg:w-[66px]  ml-[-55px] mb-9" 
    />
  </div>

  {/* Dropdown aligned to the right */}
  <div className="flex items-center justify-center px-8 md:px-12 cursor-pointer">
    <Dropdown />
  </div>
</div>


          {/* Show message when no courses match search */}
          {filteredCourses.length === 0 && searchText.trim() !== "" && (
            <div className="text-center py-8">
              <p className="text-lg text-black font-poppins">
                No courses found matching &quot;{searchText}&quot;. Try a different search term.
              </p>
            </div>
          )}

          <div className="flex space-x-4 overflow-x-auto snap-x snap-mandatory 
            md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-[50px] md:overflow-x-visible md:snap-none md:space-x-0 
            md:mx-8 items-center justify-center text-black font-poppins">
            {filteredCourses.map((course) => (
              <div key={course.id} className="snap-start shrink-0 min-w-[250px] md:min-w-0 ">
                <CourseCard course={course} />
              </div>
            ))}
          </div>
        </section>
      </main>
    </>
  );
}