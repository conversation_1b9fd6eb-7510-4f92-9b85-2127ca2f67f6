'use client';

import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import WelcomeRefer from "./WelcomeRefer";
import Image from "next/image";

export default function ReferredMain() {
  const [filter, setFilter] = useState("None");
  const [seeMore, setSeeMore] = useState(false);

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");

  const [referrals, setReferrals] = useState([
    { id: 1, name: "<PERSON><PERSON><PERSON>", status: "Sent", date: "05-Apr-2025" },
    { id: 2, name: "<PERSON><PERSON>", status: "Reinvite", date: "07-Apr-2025" },
    { id: 3, name: "<PERSON><PERSON><PERSON>", status: "Used", date: "06-Apr-2025", reward: "+1" },
    { id: 4, name: "DK", status: "Reinvite", date: "05-Apr-2025" },
    { id: 5, name: "<PERSON><PERSON><PERSON>", status: "Used", date: "05-Apr-2025", reward: "+1" },
  ]);

  const handleSendReferral = () => {
    if (!name || !email) return;

    const newReferral = {
      id: Date.now(),
      name,
      email,
      status: "Sent",
      date: new Date().toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric"
      })
    };

    setReferrals(prev => [newReferral, ...prev]);
    setName("");
    setEmail("");
    setFilter("Send");
    setSeeMore(true);
  };

  const getFilteredReferrals = () => {
    let filtered = [...referrals];

    if (filter === "Send") filtered = filtered.filter(r => r.status === "Sent");
    else if (filter === "Re invite") filtered = filtered.filter(r => r.status === "Reinvite");
    else if (filter === "Used") filtered = filtered.filter(r => r.status === "Used");
    else if (filter === "None") filtered = filtered;

    if (!seeMore) filtered = filtered.slice(0, 3);

    if (filter === "Re invite" || filter === "Used") {
      filtered.sort((a, b) => b.status === filter ? 1 : -1);
    }

    return filtered;
  };

  return (
    <div className="w-full justify-center items-center py-[70px] px-4 md:px-0">
      <WelcomeRefer />

      {seeMore && (
        <button
          onClick={() => setSeeMore(false)}
          className="mb-6 mt-5 flex text-sm px-4 py-2 rounded bg-[#373737] text-white cursor-pointer"
        >
          <ArrowLeft className="w-4 h-4 mr-2" /> Back
        </button>
      )}

      {/* Form */}
      <div className="w-full max-w-2xl mb-10">
        <h2 className="text-[20px] font-semibold font-poppins mb-6">SEND A REFERRAL</h2>

        <div className="flex flex-col md:flex-row items-start md:items-center mb-4 gap-2 md:gap-6">
          <label className="text-[16px] font-[500] text-black min-w-[120px]">Invitee Name :</label>
          <input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Type invitee name"
            className="w-full border border-black rounded-md px-4 py-2"
          />
        </div>

        <div className="flex flex-col md:flex-row items-start md:items-center mb-6 gap-2 md:gap-6">
          <label className="text-[16px] font-[500] text-black min-w-[120px]">Invitee Email :</label>
          <input
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            type="email"
            placeholder="Type invitee email"
            className="w-full border border-black rounded-md px-4 py-2"
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-5">
          <button
            onClick={handleSendReferral}
            className="bg-[#283DA6] text-white px-[30px] py-2 rounded-lg text-base font-medium"
          >
            Send
          </button>
          <button className="border border-black px-6 py-2 rounded-lg text-base font-medium">
            🔗 Copy Link
          </button>
        </div>
      </div>

      {/* Referral List */}
      <div className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-[30px] gap-4">
          <h2 className="text-[16px] font-bold">SEND REFERRALS</h2>
          <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border text-[20px] font-medium px-[20px] py-[5px] rounded-[5px] text-[#4B207A]"
            >
              <option>None</option>
              <option>All</option>
              <option>Send</option>
              <option>Re invite</option>
              <option>Used</option>
            </select>
            <button
              onClick={() => setSeeMore(true)}
              className="bg-[#4B207A] text-white text-[20px] font-medium px-[30px] py-[5px] rounded-[5px] cursor-pointer"
            >
              See more
            </button>
          </div>
        </div>

        <div className="space-y-5">
          {getFilteredReferrals().map((referral) => (
            <div
              key={referral.id}
              className="flex flex-col md:flex-row gap-4 md:gap-0 items-start md:items-center justify-between px-4 py-4 rounded bg-[#E4E4E440]"
              style={{ boxShadow: "2px 2px 4px 0px #00000040" }}
            >
              <div className="flex items-start gap-[20px] md:gap-[30px]">
                <Image
                  src="/assets/images/newprofile.png"
                  alt="User"
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
                <div className="text-[18px] font-poppins text-[#2A2A2A]">
                  {referral.status === "Reinvite" || referral.status === "Used" || referral.status === "Sent"
                    ? `You have sent a referred invitation to ${referral.name}`
                    : `${referral.name} missed your referral link.`}
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-[40px] text-[16px] font-medium">
                {referral.status === "Reinvite" && (
                  <button className="border text-[#D56E29] border-[#D56E29] rounded-full py-[7px] px-[30px]">
                    Reinvite
                  </button>
                )}
                {referral.status === "Sent" && (
                  <span className="bg-[#00A13B] text-white py-[7px] px-[30px]">Sent</span>
                )}
                {referral.status === "Used" && (
                  <div className="flex gap-2 items-center">
                    <span className="bg-[#6A6A6A] text-white py-[7px] px-[30px]">Used</span>
                    <span className="text-[#00A13B] text-[16px]">{referral.reward}</span>
                  </div>
                )}
                <span className="text-[#373737] text-[16px] font-light font-poppins">{referral.date}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
