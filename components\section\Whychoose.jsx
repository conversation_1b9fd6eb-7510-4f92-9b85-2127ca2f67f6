import React from "react";

// Dummy data with image paths
const features = [
  {
    id: 1,
    image: "/assets/images/choose1.png", // Update with your actual image path
    title: "Expert Instructor",
    description: "Learn from industry experts with years of experience."
  },
  {
    id: 2,
    image: "/assets/images/choose2.png", // Update with your actual image path
    title: "Interactive Learning",
    description: "Engage in hands-on, practical learning sessions."
  },
  {
    id: 3,
    image: "/assets/images/choose3.png", // Update with your actual image path
    title: "Verified Certificate",
    description: "Receive an industry-recognized certificate on completion."
  },
];

const Whychoose = () => {
  return (
    <section className="py-10 ">
      <div className="container text-center">
        {/* Heading Section */}
        <h2 className="text-[20px] md:text-[36px] font-bold text-[#6E1EA3] font-poppins">
          Why choose us
        </h2>
        <h3 className="text-[24px] md:text-[32px] lg:text-[36px] font-semibold text-[#1A2434] font-poppins">
          Best Learning Experience
        </h3>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:my-12 pt-[30px]">
          {features.map((feature) => (
            <div
              key={feature.id}
              className="flex flex-col items-start text-start p-6 rounded-lg bg-[#F3F1F1] shadow-lg transition-all duration-300 gap-3"
              style={{ boxShadow: "0px 4px 4px 0px #0000004D" }}
            >
              <div className="flex items-center justify-between w-full">
                <h4 className="text-[18px] lg:text-[24px] font-semibold">
                  {feature.title}
                </h4>
                <img src={feature.image} alt={feature.title} className="w-8 h-8" />
              </div>
              <p className="text-[#1A2434] text-sm md:text-[16px] md:leading-[24px]">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Whychoose;
