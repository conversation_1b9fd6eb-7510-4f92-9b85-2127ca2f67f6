'use client';
import Link from "next/link";
import SignupForm from "@/components/signupui/signup-form";
import WelcomeContent from "@/components/signupui/welcome";
import Image from "next/image";


export default function Signup() {

  

  return (
    <>
      <header className="border-b border-[#868686]">
    

        <div className="px-4 flex flex-col">
          {/* Header with logo */}
          <div className="p-1">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="w-[80px] md:w-[150px]   cursor-pointer"
            />
          </div>

        </div>
      </header>
    <section className="md:py-[40px] py-[30px]">
      <main className="container">

    <div className="w-full flex flex-col-reverse lg:flex-row items-center justify-center gap-6 px-4 md:px-8">
  <div className="w-full lg:max-w-[640px]">
    <SignupForm />
  </div>

  <div className="w-full md:max-w-[500px] hidden md:flex flex-col items-center text-center justify-center px-4">
    <WelcomeContent />
    <div className="mt-4">
      <p className="text-black font-poppins mb-2">Already have an account?</p>
      <Link
        href="/login"
        className="font-poppins py-2 px-9 bg-[#063585] text-white font-medium rounded-md transition-colors cursor-pointer"
      >
        Login
      </Link>
    </div>
  </div>
</div>

      </main>
      </section>
    </>
  );
}
