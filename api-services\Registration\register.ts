// api-service/register.ts
import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "../utils";

// ================== REGISTER INTERFACES ==================
interface StudentRegisterPayload extends Record<string, unknown> {
  email: string;
  name: string;
  mobile_number: string;
  password: string;
  confirm_password: string;
  [key: string]: string
}

interface StudentRegisterResponse {
  message: string; // Example: "otp is send to your respective email address"
}

// ================== REGISTER API ==================
async function studentRegister(payload: StudentRegisterPayload): Promise<StudentRegisterResponse> {
  const response = await makeRequest({
    endpoint: "/accounts/student/register/",
    method: "POST",
    data: payload,
  });

  return response;
}

// ================== REACT QUERY HOOK ==================
const useStudentRegister = () => {
  return useMutation<StudentRegisterResponse, Error, StudentRegisterPayload>({
    mutationFn: studentRegister,
    onError: (error) => {
      console.error("Registration error:", error);
    },
  });
};

export { useStudentRegister };
