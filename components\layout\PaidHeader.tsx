import React from 'react';
import Image from 'next/image';
import ProfileDropdown from '@/components/sidebar-dropdown/dropdown';
import Link from 'next/link';

const PaidHeader = () => {
  return (
    <header className="border-b border-[#868686] flex flex-row items-center justify-between bg-[#fff] fixed top-0 w-full z-[99] px-[10px] lg:px-[40px]">
      <div className="flex items-center">
        {/* Header with logo */}
        <div className="">
          <Link href="/">
            <Image
              src="/assets/images/logobig.png"
              alt="logo"
              width={395}
              height={128}
              className="w-[110px] sm:w-[110px] lg:w-[130px]   cursor-pointer"
            />
          </Link>
        </div>
      </div>
      <div className="flex items-center justify-center  cursor-pointer">
        <ProfileDropdown />
      </div>
    </header>
  );
};

export default PaidHeader;
