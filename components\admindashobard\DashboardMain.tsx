"use client";

import React, { useState } from "react";
import { Search, Plus } from "lucide-react";
import CourseTable from "./course-table";
import AddCourseModal from "./addnewcourse";
import { useQueryClient } from "@tanstack/react-query";


// Import the modal

export const DashboardMain = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false); // State to manage modal visibility
  const queryClient = useQueryClient();

  // Handle the search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Open the modal
  const openModal = () => {
    setIsModalOpen(true);
  };

  // Close the modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Handle successful course creation
  const handleCourseCreated = () => {
    // Invalidate and refetch courses data
    queryClient.invalidateQueries({ queryKey: ["courses"] });
  };

  return (
    <>
      <div className="relative px-[20px] py-[40px]">
        {/* Top Bar */}
        <div className="flex flex-col lg:flex-row sm:items-center sm:justify-between gap-4 pb-[30px] px-[10px]">
          {/* Search */}
          <div className="relative w-full md:w-[324px] bg-[#fbfbfb] shadow-[0px_4px_4px_0px_#00000040]">
            <Search className="absolute left-4 top-2.5 h-4 w-4 text-[#828282]" />
            <input
              placeholder="Search your courses"
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-[45px] h-[40px] w-full outline-none bg-transparent"
            />
          </div>

          {/* Add Course Button */}
          <div className="flex justify-end pr-8">
            <button
              onClick={openModal}
              className="flex items-start gap-2 border border-[#0A5224] px-5 py-2 text-[#0A5224] hover:bg-[#0A5224] hover:text-white w-full"
            >
              <Plus className="w-6 h-6 text-[#0A5224] hover:text-white" />
              Add Courses
            </button>

          </div>
        </div>

        {/* Main Scrollable Content */}
        <div>
          {/* Pass the search query to the CourseTable */}
          <CourseTable searchQuery={searchQuery} />
        </div>
      </div>

      {/* Modal Component */}
      <AddCourseModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSuccess={handleCourseCreated}
      />
    </>
  );
};
