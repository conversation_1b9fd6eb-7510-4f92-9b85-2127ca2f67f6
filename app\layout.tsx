import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "./theme-provider";
import { QueryProvider } from "@/provider/QueryClientProvider";
import { Toaster } from "react-hot-toast";
import ClientWrapper from "@/components/common/ClientWrapper";
// import ClientWrapper from "@/components/ClientWrapper";

export const metadata: Metadata = {
  title: "Edtech AI E-learning",
  description: "Learn with Expert Tutors with AI Enhanced Learning",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <ThemeProvider>
            <ClientWrapper>{children}</ClientWrapper>
          </ThemeProvider>
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}
