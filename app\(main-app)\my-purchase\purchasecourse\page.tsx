"use client";

import Image from "next/image"
import Link from "next/link"
import { ChevronRight } from "lucide-react"

import CourseDetails from "@/components/purchasedetail/coursedetail"
import PaymentHistory from "@/components/purchasedetail/paymenthistory"
import SimilarCourses from "@/components/purchasedetail/similarcourse"
import PaidHeader from "@/components/layout/PaidHeader";
// import Link from "next/link";

export default function purchasedetail() {

   
  return (
    <>
     
    <PaidHeader />
    <main className="container pt-[100px]">
      {/* Breadcrumb */}
      <nav className="flex items-center pb-20 text-black">
        <Link href="/my-purchase" className="hover:text-primary font-[Poppins] font-medium  text-[12px] md:text-[20px] leading-[100%] tracking-[-0.01em] text-black cursor-pointer">
          My Purchase
        </Link>
        <ChevronRight className="h-4 w-4 " />
        <span className="font-[Poppins] font-medium text-[10px]  md:text-[16px] leading-[100%] tracking-[-0.01em] text-black cursor-pointer">Course Details/ prompt Engineering</span>
      </nav>

      <div className="flex gap-[40px] flex-wrap">
        {/* Course Image */}
        <div className="md:col-span-1 w-full lg:w-[300px]">
          <div className="rounded-lg">
            <Image 
              src="/pe.png" 
              alt="Introduction to Prompt Engineering" 
              width={300} 
              height={400}
              className="lg:w-[300px] h-[407px] w-full object-cover"
            />
          </div>
        </div>

        {/* Course Details */}
        <div className="md:col-span-2 md:-ml-8 ml-0 ">
          <CourseDetails />
          <PaymentHistory />
        </div>
      </div>

      {/* Similar Courses */}
      <SimilarCourses />
    </main>
     
    </>
  );
}