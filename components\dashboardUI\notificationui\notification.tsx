'use client'; // Needed when using useRouter in Client Component (Next.js App Router)

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import WelcomeHeader from "../main-container/welcome-header";

export default function NotificationPanel() {
  const router = useRouter();

  const notifications = [
    {
      id: 1,
      icon: "/assets/images/medal1.png",
      title: "Badge Earned",
      type: "Badge",
      message: "You've earned the Silver Coder badge!",
      date: "14 Apr 2025",
      time: "10:30 AM",
      actions: [ "Unread"],
      isRead: false,
    },
    {
      id: 2,
      icon: "/assets/images/feedback1.png",
      title: "Project Feedback",
      type: "Project Feedback",
      message: "Your project on AI chatbot has been reviewed.",
      date: "14 Apr 2025",
      time: "10:30 AM",
      actions: [ "Unread"],
      isRead: false,
    },
    {
      id: 3,
      icon: "/assets/images/warning1.png",
      title: "Quiz Failed (3 Attempts)",
      type: "Info",
      message: "You've used all 3 attempts on 'JS Basics Quiz'.",
      date: "14 Apr 2025",
      time: "10:30 AM",
      actions: [ "Unread"],
      isRead: false,
    },
    {
      id: 4,
      icon: "/assets/images/hat1.png",
      title: "Certificate Ready",
      type: "Certificate",
      message: "Download your 'Web Dev Bootcamp' certificate now!",
      date: "14 Apr 2025",
      time: "10:30 AM",
      actions: ["Read"],
      isRead: true,
    },
    {
      id: 5,
      icon: "/assets/images/comments1.png",
      title: "Admin Message",
      type: "Info",
      message: "System maintenance on 15 Apr, 1 AM - 3 AM.",
      date: "14 Apr 2025",
      time: "10:30 AM",
      actions: ["Read"],
      isRead: true,
    },
  ];

  return (
    <div className="w-full justify-center items-center py-[70px]">
      <header>
        <WelcomeHeader />
        <button
          onClick={() => router.back()}
          className="mb-6 mt-5 flex text-sm px-4 py-2 rounded bg-[#373737] text-white cursor-pointer"
        >
          <ArrowLeft className="w-4 h-4 mr-2" /> Back
        </button>
      </header>

      <div className="space-y-4 lg:px-[80px]">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4  shadow-[0px_4px_8px_0px_#00000040] rounded-md max-w-[629px] ${notification.isRead ? "bg-[#fafafa]" : "bg-[#ededed]"}`}
          >
            <div className="flex flex-col md:flex-row md:items-start gap-4">
              <div className="w-8 h-8 flex items-center justify-center">
                <Image
                  src={notification.icon}
                  alt={`${notification.title} icon`}
                  width={40}
                  height={40}
                  className="object-contain"
                />
              </div>

              <div className="flex-1 w-full">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 gap-3">
                  <h3 className="font-poppins font-semibold text-[16px] leading-[100%] tracking-[-0.01em] text-black pb-2">
                    {notification.title}
                  </h3>
                  <span className="font-poppins font-normal text-[14px] leading-[100%] tracking-[-0.01em] text-gray-500">
                    {notification.date} • {notification.time}
                  </span>
                </div>
                <p className="font-poppins font-normal text-[16px] leading-[100%] tracking-[-0.01em] text-black  pb-4">
                  Type: {notification.type}
                </p>
                <p className="mb-4 font-poppins font-normal text-[16px] leading-[100%] tracking-[-0.01em] text-black">
                  {notification.message}
                </p>
                <div className="flex  justify-between">
                {!notification.isRead && (
                    <button className="text-sm px-4 py-1 lg:px-10 lg:py-2 rounded-full font-poppins font-medium border border-[#4f46e5] text-[#4f46e5]  flex hover:bg-[#dcdafc] hover:text-[#4f46e5] cursor-pointer">
                      mark as read
                    </button>
                  )}
                
                <div className="flex justify-end">
                </div>
                  <div className="flex flex-wrap ">
                    {notification.actions.map((action, index) => {
                      const isRead = action === "Read";
                      const isUnread = action === "Unread";
                     
                      return (
                        <button
                          key={index}
                          className={`text-sm px-4 py-1 lg:px-10 lg:py-2 rounded-full font-poppins font-medium flex 
                            ${isRead
                              ? "bg-gray-100 text-[#0a5224] border border-[#0a5224] hover:bg-gray-200  cursor-pointer"
                              : isUnread
                              ? "border border-[#9ca3af] text-[#9ca3af] hover:bg-gray-100 flex  cursor-pointer"
                              : ""
                            }`}
                        >
                          {action}
                        </button>
                      );
                    })}
                   
                  </div>
                </div>
                
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
